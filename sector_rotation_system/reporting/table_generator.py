"""
表格生成器
负责生成各种格式的数据表格
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime

from config.settings import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("TableGenerator")


class TableGenerator:
    """通用表格生成器"""
    
    def __init__(self):
        self.config = config_manager.get_reporting_config()
        
    def create_correlation_table(self, data: pd.DataFrame, 
                               sector_column: str = 'sector_code',
                               return_column: str = 'daily_return') -> pd.DataFrame:
        """
        创建行业相关性表格
        
        Args:
            data: 包含行业和收益率数据的DataFrame
            sector_column: 行业代码列名
            return_column: 收益率列名
            
        Returns:
            相关性矩阵DataFrame
        """
        if data.empty:
            logger.error("Input data is empty")
            return pd.DataFrame()
            
        # 透视表：日期为索引，行业为列，收益率为值
        pivot_data = data.pivot_table(
            index='date',
            columns=sector_column,
            values=return_column,
            aggfunc='first'
        )
        
        if pivot_data.empty:
            logger.error("Failed to create pivot table")
            return pd.DataFrame()
            
        # 计算相关性矩阵
        correlation_matrix = pivot_data.corr()
        
        logger.info(f"Generated correlation matrix for {len(correlation_matrix)} sectors")
        return correlation_matrix
        
    def identify_high_correlation_pairs(self, correlation_matrix: pd.DataFrame,
                                      threshold_high: float = 0.7,
                                      threshold_low: float = -0.7) -> Dict[str, List[tuple]]:
        """
        识别高相关性和低相关性的行业对
        
        Args:
            correlation_matrix: 相关性矩阵
            threshold_high: 高相关性阈值
            threshold_low: 低相关性阈值
            
        Returns:
            包含高相关性和低相关性行业对的字典
        """
        if correlation_matrix.empty:
            return {"high_correlation": [], "low_correlation": []}
            
        high_corr_pairs = []
        low_corr_pairs = []
        
        # 遍历相关性矩阵的上三角部分（避免重复）
        for i in range(len(correlation_matrix.columns)):
            for j in range(i + 1, len(correlation_matrix.columns)):
                sector1 = correlation_matrix.columns[i]
                sector2 = correlation_matrix.columns[j]
                corr_value = correlation_matrix.iloc[i, j]
                
                if pd.notna(corr_value):
                    if corr_value >= threshold_high:
                        high_corr_pairs.append((sector1, sector2, corr_value))
                    elif corr_value <= threshold_low:
                        low_corr_pairs.append((sector1, sector2, corr_value))
                        
        # 按相关性值排序
        high_corr_pairs.sort(key=lambda x: x[2], reverse=True)
        low_corr_pairs.sort(key=lambda x: x[2])
        
        logger.info(f"Found {len(high_corr_pairs)} high correlation pairs and {len(low_corr_pairs)} low correlation pairs")
        
        return {
            "high_correlation": high_corr_pairs,
            "low_correlation": low_corr_pairs
        }
        
    def create_concentration_summary_table(self, data: pd.DataFrame,
                                         volume_column: str = 'sector_volume_amount') -> pd.DataFrame:
        """
        创建成交金额集中度汇总表
        
        Args:
            data: 包含成交金额数据的DataFrame
            volume_column: 成交金额列名
            
        Returns:
            集中度汇总表DataFrame
        """
        if data.empty:
            logger.error("Input data is empty")
            return pd.DataFrame()
            
        # 按日期分组计算每日总成交额和各行业占比
        daily_summary = []
        
        for date, group in data.groupby('date'):
            total_volume = group[volume_column].sum()
            
            if total_volume > 0:
                # 计算各行业占比
                group_with_ratio = group.copy()
                group_with_ratio['volume_ratio'] = group_with_ratio[volume_column] / total_volume
                
                # 计算HHI
                hhi = (group_with_ratio['volume_ratio'] ** 2).sum()
                
                # 找出最大占比行业
                max_ratio_sector = group_with_ratio.loc[group_with_ratio['volume_ratio'].idxmax()]
                
                daily_summary.append({
                    'date': date,
                    'total_volume': total_volume,
                    'hhi': hhi,
                    'max_ratio_sector': max_ratio_sector['sector_code'],
                    'max_ratio_value': max_ratio_sector['volume_ratio'],
                    'sector_count': len(group)
                })
                
        summary_df = pd.DataFrame(daily_summary)
        
        if not summary_df.empty:
            # 添加统计信息
            summary_df['hhi_percentile'] = summary_df['hhi'].rank(pct=True) * 100
            
        logger.info(f"Generated concentration summary for {len(summary_df)} trading days")
        return summary_df
        
    def save_table_to_multiple_formats(self, df: pd.DataFrame, 
                                     output_dir: str, 
                                     filename: str,
                                     formats: List[str] = None) -> Dict[str, str]:
        """
        将表格保存为多种格式
        
        Args:
            df: 要保存的DataFrame
            output_dir: 输出目录
            filename: 文件名（不含扩展名）
            formats: 要保存的格式列表，默认为['csv', 'excel']
            
        Returns:
            保存的文件路径字典
        """
        if df.empty:
            logger.warning("DataFrame is empty, nothing to save")
            return {}
            
        if formats is None:
            formats = ['csv', 'excel']
            
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        saved_files = {}
        
        # 保存为CSV
        if 'csv' in formats:
            csv_path = output_path / f"{filename}.csv"
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            saved_files['csv'] = str(csv_path)
            logger.info(f"Table saved as CSV: {csv_path}")
            
        # 保存为Excel
        if 'excel' in formats:
            excel_path = output_path / f"{filename}.xlsx"
            try:
                df.to_excel(excel_path, index=False, engine='openpyxl')
                saved_files['excel'] = str(excel_path)
                logger.info(f"Table saved as Excel: {excel_path}")
            except ImportError:
                logger.warning("openpyxl not available, Excel file not saved")
                
        # 保存为JSON
        if 'json' in formats:
            json_path = output_path / f"{filename}.json"
            df.to_json(json_path, orient='records', indent=2, force_ascii=False)
            saved_files['json'] = str(json_path)
            logger.info(f"Table saved as JSON: {json_path}")
            
        return saved_files
        
    def create_summary_statistics_table(self, data: pd.DataFrame,
                                      numeric_columns: List[str] = None) -> pd.DataFrame:
        """
        创建汇总统计表
        
        Args:
            data: 输入数据
            numeric_columns: 要统计的数值列，如果为None则自动检测
            
        Returns:
            汇总统计表DataFrame
        """
        if data.empty:
            logger.error("Input data is empty")
            return pd.DataFrame()
            
        if numeric_columns is None:
            numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()
            # 排除日期相关列
            numeric_columns = [col for col in numeric_columns if 'date' not in col.lower()]
            
        if not numeric_columns:
            logger.warning("No numeric columns found for statistics")
            return pd.DataFrame()
            
        # 计算统计指标
        stats_data = []
        
        for col in numeric_columns:
            if col in data.columns:
                series = data[col].dropna()
                
                if len(series) > 0:
                    stats_data.append({
                        'indicator': col,
                        'count': len(series),
                        'mean': series.mean(),
                        'std': series.std(),
                        'min': series.min(),
                        'q25': series.quantile(0.25),
                        'median': series.median(),
                        'q75': series.quantile(0.75),
                        'max': series.max(),
                        'skewness': series.skew(),
                        'kurtosis': series.kurtosis()
                    })
                    
        stats_df = pd.DataFrame(stats_data)
        
        if not stats_df.empty:
            # 格式化数值
            numeric_cols = ['mean', 'std', 'min', 'q25', 'median', 'q75', 'max', 'skewness', 'kurtosis']
            for col in numeric_cols:
                if col in stats_df.columns:
                    stats_df[col] = stats_df[col].round(6)
                    
        logger.info(f"Generated summary statistics for {len(stats_df)} indicators")
        return stats_df
