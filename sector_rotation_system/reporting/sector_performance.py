"""
行业表现报告生成器
生成行业涨跌幅表格报告
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path

from config.settings import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("SectorPerformanceReporter")


class SectorPerformanceReporter:
    """行业表现报告生成器"""
    
    def __init__(self):
        self.config = config_manager.get_reporting_config()
        
    def generate_sector_performance_table(self, data: pd.DataFrame, 
                                        end_date: Optional[str] = None,
                                        custom_start_date: Optional[str] = None) -> pd.DataFrame:
        """
        生成行业涨跌幅表格
        
        Args:
            data: 包含daily_return字段的数据
            end_date: 结束日期，默认为数据中的最新日期
            custom_start_date: 自定义时间段的起始日期
            
        Returns:
            包含各时间段涨跌幅的DataFrame
        """
        if data.empty:
            logger.error("Input data is empty")
            return pd.DataFrame()
            
        # 确保数据包含必要字段
        required_columns = ['date', 'sector_code', 'daily_return']
        missing_columns = set(required_columns) - set(data.columns)
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            return pd.DataFrame()
            
        # 准备数据
        data = data.copy()
        data['date'] = pd.to_datetime(data['date'])
        data = data.sort_values(['sector_code', 'date'])
        
        # 确定结束日期
        if end_date is None:
            end_date = data['date'].max()
        else:
            end_date = pd.to_datetime(end_date)
            
        # 获取所有行业
        sectors = data['sector_code'].unique()
        
        # 初始化结果字典
        results = []
        
        for sector in sectors:
            sector_data = data[data['sector_code'] == sector].copy()
            sector_data = sector_data[sector_data['date'] <= end_date]
            
            if sector_data.empty:
                continue
                
            sector_result = {'sector_name': sector}
            
            # 计算各时间段涨跌幅
            try:
                # 本周涨跌幅（最近5个交易日）
                weekly_return = self._calculate_period_return(
                    sector_data, self.config.time_periods['weekly']
                )
                sector_result['weekly_return'] = weekly_return
                
                # 本月涨跌幅（最近22个交易日）
                monthly_return = self._calculate_period_return(
                    sector_data, self.config.time_periods['monthly']
                )
                sector_result['monthly_return'] = monthly_return
                
                # 本年度涨跌幅（最近252个交易日）
                yearly_return = self._calculate_period_return(
                    sector_data, self.config.time_periods['yearly']
                )
                sector_result['yearly_return'] = yearly_return
                
                # 自定义时间段涨跌幅
                if custom_start_date:
                    custom_return = self._calculate_custom_period_return(
                        sector_data, custom_start_date, end_date
                    )
                    sector_result['custom_return'] = custom_return
                    
            except Exception as e:
                logger.warning(f"Error calculating returns for sector {sector}: {e}")
                sector_result.update({
                    'weekly_return': np.nan,
                    'monthly_return': np.nan,
                    'yearly_return': np.nan
                })
                if custom_start_date:
                    sector_result['custom_return'] = np.nan
                    
            results.append(sector_result)
            
        # 转换为DataFrame
        result_df = pd.DataFrame(results)
        
        if result_df.empty:
            logger.warning("No valid sector performance data generated")
            return pd.DataFrame()
            
        # 格式化百分比
        percentage_columns = ['weekly_return', 'monthly_return', 'yearly_return']
        if custom_start_date:
            percentage_columns.append('custom_return')
            
        for col in percentage_columns:
            if col in result_df.columns:
                result_df[f'{col}_pct'] = (result_df[col] * 100).round(2)
                
        # 按指定字段排序
        sort_column = f"{self.config.sort_by}_return"
        if sort_column in result_df.columns:
            result_df = result_df.sort_values(sort_column, ascending=False)
            
        logger.info(f"Generated sector performance table with {len(result_df)} sectors")
        return result_df
        
    def _calculate_period_return(self, sector_data: pd.DataFrame, periods: int) -> float:
        """计算指定期间的累计收益率"""
        if len(sector_data) < 2:
            return np.nan
            
        # 获取最近N个交易日的数据
        recent_data = sector_data.tail(periods + 1)  # +1 是为了计算收益率
        
        if len(recent_data) < 2:
            return np.nan
            
        # 计算累计收益率
        returns = recent_data['daily_return'].dropna()
        if len(returns) == 0:
            return np.nan
            
        # 使用复合收益率公式
        cumulative_return = (1 + returns).prod() - 1
        return cumulative_return
        
    def _calculate_custom_period_return(self, sector_data: pd.DataFrame, 
                                      start_date: str, end_date: pd.Timestamp) -> float:
        """计算自定义时间段的累计收益率"""
        start_date = pd.to_datetime(start_date)
        
        # 筛选时间段内的数据
        period_data = sector_data[
            (sector_data['date'] >= start_date) & 
            (sector_data['date'] <= end_date)
        ]
        
        if len(period_data) < 2:
            return np.nan
            
        # 计算累计收益率
        returns = period_data['daily_return'].dropna()
        if len(returns) == 0:
            return np.nan
            
        cumulative_return = (1 + returns).prod() - 1
        return cumulative_return
        
    def save_performance_table(self, performance_df: pd.DataFrame, 
                             output_dir: str, filename: str) -> str:
        """保存表现表格到文件"""
        if performance_df.empty:
            logger.warning("Performance table is empty, nothing to save")
            return ""
            
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存为CSV
        csv_path = output_path / f"{filename}.csv"
        performance_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        # 保存为Excel（如果需要）
        excel_path = output_path / f"{filename}.xlsx"
        try:
            performance_df.to_excel(excel_path, index=False, engine='openpyxl')
            logger.info(f"Performance table saved to {excel_path}")
        except ImportError:
            logger.warning("openpyxl not available, Excel file not saved")
            
        logger.info(f"Performance table saved to {csv_path}")
        return str(csv_path)
        
    def generate_markdown_table(self, performance_df: pd.DataFrame) -> str:
        """生成Markdown格式的表格"""
        if performance_df.empty:
            return "## 行业表现数据为空\n"
            
        markdown = "## 行业涨跌幅表现\n\n"
        
        # 创建表格头
        headers = ["行业名称", "本周涨跌幅(%)", "本月涨跌幅(%)", "本年度涨跌幅(%)"]
        if 'custom_return_pct' in performance_df.columns:
            headers.append("自定义期间涨跌幅(%)")
            
        markdown += "| " + " | ".join(headers) + " |\n"
        markdown += "| " + " | ".join(["---"] * len(headers)) + " |\n"
        
        # 添加数据行
        for _, row in performance_df.iterrows():
            row_data = [
                row['sector_name'],
                f"{row.get('weekly_return_pct', 'N/A'):.2f}" if pd.notna(row.get('weekly_return_pct')) else "N/A",
                f"{row.get('monthly_return_pct', 'N/A'):.2f}" if pd.notna(row.get('monthly_return_pct')) else "N/A",
                f"{row.get('yearly_return_pct', 'N/A'):.2f}" if pd.notna(row.get('yearly_return_pct')) else "N/A"
            ]
            
            if 'custom_return_pct' in performance_df.columns:
                custom_val = row.get('custom_return_pct', 'N/A')
                row_data.append(f"{custom_val:.2f}" if pd.notna(custom_val) else "N/A")
                
            markdown += "| " + " | ".join(row_data) + " |\n"
            
        return markdown
