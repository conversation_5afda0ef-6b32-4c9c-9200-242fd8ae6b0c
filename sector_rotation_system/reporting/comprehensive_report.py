"""
综合分析报告生成器
整合所有分析结果生成完整的行业轮动速度量化分析报告
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from config.settings import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("ComprehensiveReporter")


class ComprehensiveReporter:
    """综合分析报告生成器"""
    
    def __init__(self):
        self.config = config_manager.config
        
    def generate_comprehensive_report(self, 
                                    analysis_results: Dict[str, Any],
                                    output_dir: str,
                                    timestamp: str) -> str:
        """
        生成综合分析报告
        
        Args:
            analysis_results: 分析结果字典
            output_dir: 输出目录
            timestamp: 时间戳
            
        Returns:
            报告文件路径
        """
        report_content = self._create_report_content(analysis_results)
        
        # 保存报告
        report_filename = f"comprehensive_analysis_report_{timestamp}.md"
        report_path = Path(output_dir) / report_filename
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
            
        logger.info(f"Comprehensive report saved to: {report_path}")
        return str(report_path)
        
    def _create_report_content(self, results: Dict[str, Any]) -> str:
        """创建报告内容"""
        report = "# 行业轮动速度量化分析报告\n\n"
        
        # 报告生成时间
        report += f"**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 分析概览
        report += self._create_overview_section(results)
        
        # 指标计算结果摘要
        report += self._create_indicators_summary_section(results)
        
        # 历史百分位分析
        report += self._create_percentile_analysis_section(results)
        
        # HHI集中度分析
        report += self._create_hhi_analysis_section(results)
        
        # 行业表现对比
        report += self._create_performance_comparison_section(results)
        
        # 相关性分析结论
        report += self._create_correlation_analysis_section(results)
        
        # 主要发现和结论
        report += self._create_conclusions_section(results)
        
        # 附录：技术说明
        report += self._create_technical_appendix()
        
        return report
        
    def _create_overview_section(self, results: Dict[str, Any]) -> str:
        """创建分析概览部分"""
        section = "## 分析概览\n\n"
        
        # 基本信息
        section += "### 基本信息\n\n"
        section += f"- **数据记录数**: {results.get('data_records', 'N/A'):,}\n"
        section += f"- **指标计算期间数**: {results.get('indicator_periods', 'N/A'):,}\n"
        section += f"- **分析日期范围**: {results.get('date_range', 'N/A')}\n"
        
        # 输出文件统计
        viz_files = results.get('visualization_files', {})
        reports = results.get('reports', {})
        section += f"- **生成图表数量**: {len(viz_files)}\n"
        section += f"- **生成报告数量**: {len(reports)}\n\n"
        
        return section
        
    def _create_indicators_summary_section(self, results: Dict[str, Any]) -> str:
        """创建指标摘要部分"""
        section = "## 指标计算结果摘要\n\n"
        
        indicators_summary = results.get('indicators_summary', {})
        
        if indicators_summary:
            section += "### 主要指标统计\n\n"
            section += "| 指标名称 | 平均值 | 标准差 | 最小值 | 最大值 | 最新值 |\n"
            section += "|---------|--------|--------|--------|--------|---------|\n"
            
            for indicator, stats in indicators_summary.items():
                if isinstance(stats, dict):
                    mean_val = stats.get('mean', 'N/A')
                    std_val = stats.get('std', 'N/A')
                    min_val = stats.get('min', 'N/A')
                    max_val = stats.get('max', 'N/A')
                    latest_val = stats.get('latest', 'N/A')

                    # 安全格式化数值
                    mean_str = f"{mean_val:.4f}" if isinstance(mean_val, (int, float)) and not pd.isna(mean_val) else "N/A"
                    std_str = f"{std_val:.4f}" if isinstance(std_val, (int, float)) and not pd.isna(std_val) else "N/A"
                    min_str = f"{min_val:.4f}" if isinstance(min_val, (int, float)) and not pd.isna(min_val) else "N/A"
                    max_str = f"{max_val:.4f}" if isinstance(max_val, (int, float)) and not pd.isna(max_val) else "N/A"
                    latest_str = f"{latest_val:.4f}" if isinstance(latest_val, (int, float)) and not pd.isna(latest_val) else "N/A"

                    section += f"| {indicator} | {mean_str} | {std_str} | {min_str} | {max_str} | {latest_str} |\n"
        else:
            section += "指标摘要数据不可用。\n"
            
        section += "\n"
        return section
        
    def _create_percentile_analysis_section(self, results: Dict[str, Any]) -> str:
        """创建历史百分位分析部分"""
        section = "## 历史百分位分析\n\n"
        
        percentile_summary = results.get('percentile_summary', {})
        
        if percentile_summary:
            section += "### 最新指标历史百分位\n\n"
            section += "| 指标名称 | 历史百分位 | 水平评级 |\n"
            section += "|---------|------------|----------|\n"
            
            # 按百分位排序
            sorted_percentiles = sorted(percentile_summary.items(), 
                                      key=lambda x: x[1], reverse=True)
            
            for indicator, percentile in sorted_percentiles:
                level = self._classify_percentile_level(percentile)
                section += f"| {indicator} | {percentile:.1f}% | {level} |\n"
                
            section += "\n### 百分位分析说明\n\n"
            section += "- **极高 (90%+)**: 指标值处于历史最高水平\n"
            section += "- **高 (75-90%)**: 指标值处于历史较高水平\n"
            section += "- **中等偏高 (50-75%)**: 指标值处于历史中等偏高水平\n"
            section += "- **中等偏低 (25-50%)**: 指标值处于历史中等偏低水平\n"
            section += "- **低 (10-25%)**: 指标值处于历史较低水平\n"
            section += "- **极低 (0-10%)**: 指标值处于历史最低水平\n\n"
        else:
            section += "历史百分位数据不可用。\n\n"
            
        return section
        
    def _create_hhi_analysis_section(self, results: Dict[str, Any]) -> str:
        """创建HHI集中度分析部分"""
        section = "## HHI集中度分析\n\n"
        
        hhi_summary = results.get('hhi_summary', {})
        
        if hhi_summary:
            section += "### 集中度现状\n\n"
            latest_hhi = hhi_summary.get('latest_hhi', 'N/A')
            hhi_str = f"{latest_hhi:.4f}" if isinstance(latest_hhi, (int, float)) and not pd.isna(latest_hhi) else "N/A"
            section += f"- **最新HHI值**: {hhi_str}\n"
            section += f"- **集中度水平**: {hhi_summary.get('latest_level', 'N/A')}\n"
            section += f"- **最新日期**: {hhi_summary.get('latest_date', 'N/A')}\n\n"
            
            if 'low_threshold' in hhi_summary and 'high_threshold' in hhi_summary:
                section += "### 集中度阈值\n\n"
                section += f"- **低集中度阈值**: {hhi_summary['low_threshold']:.4f}\n"
                section += f"- **高集中度阈值**: {hhi_summary['high_threshold']:.4f}\n\n"
                
            if 'concentration_distribution' in hhi_summary:
                section += "### 集中度分布\n\n"
                total_periods = hhi_summary.get('total_periods', 0)
                for level, count in hhi_summary['concentration_distribution'].items():
                    percentage = (count / total_periods * 100) if total_periods > 0 else 0
                    section += f"- **{level}**: {count} 天 ({percentage:.1f}%)\n"
                section += "\n"
                
            section += "### HHI指标解释\n\n"
            section += "HHI (赫芬达尔-赫希曼指数) 用于衡量行业成交金额的集中度：\n"
            section += "- HHI值越高，表示成交金额越集中在少数行业\n"
            section += "- HHI值越低，表示成交金额在各行业间分布越均匀\n"
            section += "- 高集中度可能意味着市场热点集中，轮动速度较慢\n"
            section += "- 低集中度可能意味着资金分散，轮动更加活跃\n\n"
        else:
            section += "HHI集中度数据不可用。\n\n"
            
        return section
        
    def _create_performance_comparison_section(self, results: Dict[str, Any]) -> str:
        """创建行业表现对比部分"""
        section = "## 行业表现对比\n\n"
        
        # 这里可以从results中获取行业表现数据
        # 由于数据结构复杂，我们提供一个通用的描述
        section += "### 多时间段表现分析\n\n"
        section += "本分析涵盖了以下时间段的行业表现：\n"
        section += "- **本周表现**: 最近5个交易日的涨跌幅\n"
        section += "- **本月表现**: 当月至今的涨跌幅\n"
        section += "- **本年表现**: 年初至今的涨跌幅\n"
        
        if results.get('custom_start_date'):
            section += "- **自定义期间表现**: 指定起始日期至分析结束日期的涨跌幅\n"
            
        section += "\n详细的行业表现数据请参考单独的行业表现报告文件。\n\n"
        
        return section
        
    def _create_correlation_analysis_section(self, results: Dict[str, Any]) -> str:
        """创建相关性分析部分"""
        section = "## 相关性分析结论\n\n"
        
        correlation_summary = results.get('correlation_summary', {})
        
        if correlation_summary:
            high_corr = correlation_summary.get('high_correlation', [])
            low_corr = correlation_summary.get('low_correlation', [])
            
            section += "### 高相关性行业对\n\n"
            if high_corr:
                section += "以下行业对显示出较强的正相关性（相关系数 > 0.7）：\n\n"
                for i, (sector1, sector2, corr) in enumerate(high_corr[:5], 1):  # 显示前5个
                    section += f"{i}. **{sector1}** 与 **{sector2}**: {corr:.3f}\n"
                section += "\n"
            else:
                section += "未发现显著的高相关性行业对。\n\n"
                
            section += "### 低相关性行业对\n\n"
            if low_corr:
                section += "以下行业对显示出较强的负相关性（相关系数 < -0.7）：\n\n"
                for i, (sector1, sector2, corr) in enumerate(low_corr[:5], 1):  # 显示前5个
                    section += f"{i}. **{sector1}** 与 **{sector2}**: {corr:.3f}\n"
                section += "\n"
            else:
                section += "未发现显著的低相关性行业对。\n\n"
                
            section += "### 相关性分析意义\n\n"
            section += "- **高正相关**: 表示行业间存在同向变动趋势，可能受相似因素影响\n"
            section += "- **高负相关**: 表示行业间存在反向变动趋势，可能存在资金替代效应\n"
            section += "- **低相关性**: 表示行业间相对独立，有利于分散化投资\n\n"
        else:
            section += "相关性分析数据不可用。\n\n"
            
        return section
        
    def _create_conclusions_section(self, results: Dict[str, Any]) -> str:
        """创建主要发现和结论部分"""
        section = "## 主要发现和结论\n\n"
        
        section += "### 轮动速度评估\n\n"
        
        # 基于百分位数据的分析
        percentile_summary = results.get('percentile_summary', {})
        if percentile_summary:
            high_percentile_indicators = [k for k, v in percentile_summary.items() if v >= 75]
            low_percentile_indicators = [k for k, v in percentile_summary.items() if v <= 25]
            
            if high_percentile_indicators:
                section += f"**高活跃度指标**: {', '.join(high_percentile_indicators)} 当前处于历史较高水平，"
                section += "表明相应维度的轮动速度较快。\n\n"
                
            if low_percentile_indicators:
                section += f"**低活跃度指标**: {', '.join(low_percentile_indicators)} 当前处于历史较低水平，"
                section += "表明相应维度的轮动速度较慢。\n\n"
                
        # HHI分析结论
        hhi_summary = results.get('hhi_summary', {})
        if hhi_summary:
            latest_level = hhi_summary.get('latest_level', '')
            if latest_level == '高集中度':
                section += "**集中度分析**: 当前市场成交金额高度集中，资金主要流向少数行业，轮动相对缓慢。\n\n"
            elif latest_level == '低集中度':
                section += "**集中度分析**: 当前市场成交金额分散，资金在各行业间分布相对均匀，轮动较为活跃。\n\n"
            else:
                section += "**集中度分析**: 当前市场成交金额集中度处于中等水平。\n\n"
                
        section += "### 投资建议\n\n"
        section += "1. **轮动策略**: 根据各指标的历史百分位水平，调整轮动策略的频率和幅度\n"
        section += "2. **行业配置**: 关注相关性分析结果，合理配置不同相关性特征的行业\n"
        section += "3. **风险管理**: 在高轮动速度期间加强风险控制，在低轮动期间保持耐心\n"
        section += "4. **市场时机**: 结合集中度分析判断市场整体的轮动环境\n\n"
        
        return section
        
    def _create_technical_appendix(self) -> str:
        """创建技术说明附录"""
        section = "## 附录：技术说明\n\n"
        
        section += "### 指标计算方法\n\n"
        section += "1. **行业排名变化指标**: 基于行业涨跌幅排名计算每日变动绝对值之和\n"
        section += "2. **相对强度指标**: 行业指数与基准指数相对强度的每日横截面标准差\n"
        section += "3. **行业收益率离散度**: 每日所有行业收益率的横截面标准差\n"
        section += "4. **行业成交金额集中度**: 每日行业成交金额的赫芬达尔-赫希曼指数(HHI)\n"
        section += "5. **行业涨跌幅分布特征**: 每日所有行业涨跌幅的偏度和峰度\n"
        section += "6. **动量因子排名变化率**: 基于指定回溯期内的行业累计收益率排名变化\n\n"
        
        section += "### 历史百分位计算\n\n"
        section += f"- **计算窗口**: {self.config.indicator.percentile_window} 个交易日\n"
        section += "- **计算方法**: 滚动窗口内当前值的排名百分比\n"
        section += "- **更新频率**: 每个交易日更新\n\n"
        
        section += "### HHI集中度阈值\n\n"
        section += f"- **低集中度阈值**: 历史{self.config.indicator.hhi_low_threshold_percentile}%分位数\n"
        section += f"- **高集中度阈值**: 历史{self.config.indicator.hhi_high_threshold_percentile}%分位数\n"
        section += "- **阈值更新**: 基于历史数据动态计算\n\n"
        
        section += "### 数据来源和处理\n\n"
        section += "- **数据频率**: 日频数据\n"
        section += "- **数据清洗**: 包含缺失值处理和异常值检测\n"
        section += "- **基准指数**: " + self.config.data.benchmark_index + "\n\n"
        
        return section
        
    def _classify_percentile_level(self, percentile: float) -> str:
        """分类百分位水平"""
        if pd.isna(percentile):
            return "未知"
        elif percentile >= 90:
            return "极高"
        elif percentile >= 75:
            return "高"
        elif percentile >= 50:
            return "中等偏高"
        elif percentile >= 25:
            return "中等偏低"
        elif percentile >= 10:
            return "低"
        else:
            return "极低"
