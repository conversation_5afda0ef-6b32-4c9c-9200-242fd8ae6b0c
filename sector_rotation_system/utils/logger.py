"""
日志记录模块
提供统一的日志记录功能，支持不同级别的日志输出
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional


class LoggerManager:
    """日志管理器"""
    
    def __init__(self, log_dir: str = "logs", log_level: str = "INFO"):
        self.log_dir = Path(log_dir)
        self.log_level = log_level
        self._setup_logger()
    
    def _setup_logger(self) -> None:
        """设置日志配置"""
        # 创建日志目录
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 移除默认处理器
        logger.remove()
        
        # 添加控制台输出
        logger.add(
            sys.stdout,
            level=self.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>",
            colorize=True
        )
        
        # 添加文件输出
        logger.add(
            self.log_dir / "sector_rotation_{time:YYYY-MM-DD}.log",
            level=self.log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 添加错误日志文件
        logger.add(
            self.log_dir / "error_{time:YYYY-MM-DD}.log",
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="1 day",
            retention="90 days",
            compression="zip",
            encoding="utf-8"
        )
    
    def get_logger(self, name: Optional[str] = None):
        """获取日志记录器"""
        if name:
            return logger.bind(name=name)
        return logger


# 全局日志管理器实例
log_manager = LoggerManager()
system_logger = log_manager.get_logger("SectorRotationSystem")


def get_module_logger(module_name: str):
    """获取模块专用日志记录器"""
    return log_manager.get_logger(module_name)
