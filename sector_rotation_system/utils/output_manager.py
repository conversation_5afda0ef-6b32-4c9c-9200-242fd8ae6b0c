"""
输出管理器
负责管理按日期组织的输出文件结构
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Union

from config.settings import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("OutputManager")


class OutputManager:
    """输出文件管理器"""
    
    def __init__(self):
        self.storage_config = config_manager.get_storage_config()
        self.base_path = Path(self.storage_config.base_path)
        self.use_date_folders = self.storage_config.use_date_folders
        self.date_format = self.storage_config.date_folder_format
        
    def create_output_structure(self, run_timestamp: Optional[str] = None) -> Dict[str, str]:
        """
        创建输出目录结构
        
        Args:
            run_timestamp: 运行时间戳，如果为None则使用当前时间
            
        Returns:
            包含各类输出路径的字典
        """
        if run_timestamp is None:
            run_timestamp = datetime.now().strftime(self.date_format)
            
        if self.use_date_folders:
            # 创建日期文件夹
            run_folder = self.base_path / run_timestamp
        else:
            # 使用基础路径
            run_folder = self.base_path
            
        # 创建子目录结构
        subdirs = {
            'charts': run_folder / 'charts',
            'reports': run_folder / 'reports', 
            'tables': run_folder / 'tables',
            'data': run_folder / 'data',
            'logs': run_folder / 'logs'
        }
        
        # 创建所有目录
        for subdir_name, subdir_path in subdirs.items():
            subdir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {subdir_path}")
            
        # 返回路径字符串
        output_paths = {
            'base': str(run_folder),
            'charts': str(subdirs['charts']),
            'reports': str(subdirs['reports']),
            'tables': str(subdirs['tables']),
            'data': str(subdirs['data']),
            'logs': str(subdirs['logs']),
            'timestamp': run_timestamp
        }
        
        logger.info(f"Created output structure at: {run_folder}")
        return output_paths
        
    def create_latest_symlinks(self, run_paths: Dict[str, str]) -> None:
        """
        创建指向最新运行结果的符号链接
        
        Args:
            run_paths: 运行路径字典
        """
        if not self.use_date_folders:
            # 如果不使用日期文件夹，则不需要创建符号链接
            return
            
        latest_folder = self.base_path / 'latest'
        
        # 删除现有的latest文件夹
        if latest_folder.exists():
            if latest_folder.is_symlink():
                latest_folder.unlink()
            else:
                shutil.rmtree(latest_folder)
                
        # 创建新的符号链接
        try:
            latest_folder.symlink_to(Path(run_paths['base']).name)
            logger.info(f"Created latest symlink: {latest_folder} -> {run_paths['base']}")
        except OSError as e:
            # 在Windows上可能无法创建符号链接，改为复制
            logger.warning(f"Failed to create symlink, copying instead: {e}")
            shutil.copytree(run_paths['base'], latest_folder)
            
    def save_run_metadata(self, run_paths: Dict[str, str], 
                         metadata: Dict[str, any]) -> str:
        """
        保存运行元数据
        
        Args:
            run_paths: 运行路径字典
            metadata: 元数据字典
            
        Returns:
            元数据文件路径
        """
        import json
        
        metadata_file = Path(run_paths['base']) / 'run_metadata.json'
        
        # 添加路径信息到元数据
        metadata['output_paths'] = run_paths
        metadata['created_at'] = datetime.now().isoformat()
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False, default=str)
            
        logger.info(f"Saved run metadata to: {metadata_file}")
        return str(metadata_file)
        
    def get_available_runs(self) -> List[Dict[str, str]]:
        """
        获取可用的运行结果列表
        
        Returns:
            运行结果信息列表
        """
        if not self.base_path.exists():
            return []
            
        runs = []
        
        for item in self.base_path.iterdir():
            if item.is_dir() and item.name != 'latest':
                # 检查是否是日期格式的文件夹
                try:
                    # 尝试解析时间戳
                    timestamp = datetime.strptime(item.name, self.date_format)
                    
                    # 检查是否有元数据文件
                    metadata_file = item / 'run_metadata.json'
                    metadata = {}
                    
                    if metadata_file.exists():
                        try:
                            import json
                            with open(metadata_file, 'r', encoding='utf-8') as f:
                                metadata = json.load(f)
                        except Exception as e:
                            logger.warning(f"Failed to load metadata from {metadata_file}: {e}")
                            
                    runs.append({
                        'timestamp': item.name,
                        'path': str(item),
                        'created_at': timestamp.isoformat(),
                        'metadata': metadata
                    })
                    
                except ValueError:
                    # 不是日期格式的文件夹，跳过
                    continue
                    
        # 按时间戳排序（最新的在前）
        runs.sort(key=lambda x: x['timestamp'], reverse=True)
        
        logger.info(f"Found {len(runs)} available runs")
        return runs
        
    def cleanup_old_runs(self, keep_count: int = 10) -> int:
        """
        清理旧的运行结果，保留最新的N个
        
        Args:
            keep_count: 保留的运行结果数量
            
        Returns:
            删除的运行结果数量
        """
        runs = self.get_available_runs()
        
        if len(runs) <= keep_count:
            logger.info(f"Only {len(runs)} runs found, no cleanup needed")
            return 0
            
        # 删除超出保留数量的旧运行结果
        runs_to_delete = runs[keep_count:]
        deleted_count = 0
        
        for run in runs_to_delete:
            try:
                run_path = Path(run['path'])
                if run_path.exists():
                    shutil.rmtree(run_path)
                    deleted_count += 1
                    logger.info(f"Deleted old run: {run_path}")
            except Exception as e:
                logger.error(f"Failed to delete run {run['path']}: {e}")
                
        logger.info(f"Cleanup completed: deleted {deleted_count} old runs")
        return deleted_count
        
    def get_file_list(self, run_timestamp: str) -> Dict[str, List[str]]:
        """
        获取指定运行的文件列表
        
        Args:
            run_timestamp: 运行时间戳
            
        Returns:
            按类型分组的文件列表
        """
        if self.use_date_folders:
            run_folder = self.base_path / run_timestamp
        else:
            run_folder = self.base_path
            
        if not run_folder.exists():
            logger.warning(f"Run folder not found: {run_folder}")
            return {}
            
        file_lists = {}
        
        # 扫描各个子目录
        subdirs = ['charts', 'reports', 'tables', 'data', 'logs']
        
        for subdir in subdirs:
            subdir_path = run_folder / subdir
            if subdir_path.exists():
                files = []
                for file_path in subdir_path.rglob('*'):
                    if file_path.is_file():
                        files.append(str(file_path.relative_to(subdir_path)))
                file_lists[subdir] = sorted(files)
            else:
                file_lists[subdir] = []
                
        return file_lists
        
    def copy_to_archive(self, run_timestamp: str, archive_path: str) -> bool:
        """
        将运行结果复制到归档位置
        
        Args:
            run_timestamp: 运行时间戳
            archive_path: 归档路径
            
        Returns:
            是否成功
        """
        if self.use_date_folders:
            source_folder = self.base_path / run_timestamp
        else:
            source_folder = self.base_path
            
        if not source_folder.exists():
            logger.error(f"Source folder not found: {source_folder}")
            return False
            
        archive_folder = Path(archive_path) / run_timestamp
        
        try:
            shutil.copytree(source_folder, archive_folder)
            logger.info(f"Archived run to: {archive_folder}")
            return True
        except Exception as e:
            logger.error(f"Failed to archive run: {e}")
            return False
