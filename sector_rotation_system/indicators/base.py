"""
指标计算基础模块
定义指标计算的抽象基类和通用函数
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any
from abc import ABC, abstractmethod
from scipy import stats

from utils.logger import get_module_logger

logger = get_module_logger("IndicatorBase")


class BaseIndicator(ABC):
    """指标计算抽象基类"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        
    @abstractmethod
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """计算指标"""
        pass
    
    def validate_data(self, data: pd.DataFrame, required_columns: List[str]) -> bool:
        """验证输入数据"""
        missing_columns = set(required_columns) - set(data.columns)
        if missing_columns:
            logger.error(f"Missing required columns for {self.name}: {missing_columns}")
            return False
        return True
    
    def __str__(self) -> str:
        return f"{self.name}: {self.description}"


class CrossSectionalIndicator(BaseIndicator):
    """横截面指标基类"""
    
    def calculate_cross_sectional_std(self, data: pd.DataFrame, 
                                    value_column: str, 
                                    date_column: str = 'date') -> pd.Series:
        """计算横截面标准差"""
        return data.groupby(date_column)[value_column].std()
    
    def calculate_cross_sectional_mean(self, data: pd.DataFrame, 
                                     value_column: str, 
                                     date_column: str = 'date') -> pd.Series:
        """计算横截面均值"""
        return data.groupby(date_column)[value_column].mean()
    
    def calculate_cross_sectional_skewness(self, data: pd.DataFrame, 
                                         value_column: str, 
                                         date_column: str = 'date') -> pd.Series:
        """计算横截面偏度"""
        return data.groupby(date_column)[value_column].apply(stats.skew)
    
    def calculate_cross_sectional_kurtosis(self, data: pd.DataFrame, 
                                         value_column: str, 
                                         date_column: str = 'date') -> pd.Series:
        """计算横截面峰度"""
        return data.groupby(date_column)[value_column].apply(stats.kurtosis)


class TimeSeriesIndicator(BaseIndicator):
    """时间序列指标基类"""
    
    def calculate_returns(self, prices: pd.Series, periods: int = 1) -> pd.Series:
        """计算收益率"""
        return prices.pct_change(periods=periods)
    
    def calculate_cumulative_returns(self, returns: pd.Series, periods: int) -> pd.Series:
        """计算累计收益率"""
        return (1 + returns).rolling(window=periods).apply(lambda x: x.prod() - 1, raw=False)
    
    def calculate_rolling_std(self, data: pd.Series, window: int) -> pd.Series:
        """计算滚动标准差"""
        return data.rolling(window=window).std()
    
    def calculate_rolling_mean(self, data: pd.Series, window: int) -> pd.Series:
        """计算滚动均值"""
        return data.rolling(window=window).mean()


class RankingIndicator(BaseIndicator):
    """排名指标基类"""
    
    def calculate_ranks(self, data: pd.DataFrame, 
                       value_column: str, 
                       date_column: str = 'date',
                       ascending: bool = False) -> pd.DataFrame:
        """计算排名"""
        def rank_group(group):
            group['rank'] = group[value_column].rank(ascending=ascending, method='min')
            return group
        
        return data.groupby(date_column).apply(rank_group).reset_index(drop=True)
    
    def calculate_rank_changes(self, ranked_data: pd.DataFrame, 
                             rank_column: str = 'rank',
                             date_column: str = 'date',
                             sector_column: str = 'sector_code') -> pd.Series:
        """计算排名变化"""
        # 按行业分组，计算排名变化
        rank_changes = []
        
        for date in ranked_data[date_column].unique()[1:]:  # 从第二个日期开始
            current_data = ranked_data[ranked_data[date_column] == date]
            prev_date = ranked_data[ranked_data[date_column] < date][date_column].max()
            prev_data = ranked_data[ranked_data[date_column] == prev_date]
            
            # 合并当前和前一期数据
            merged = pd.merge(
                current_data[[sector_column, rank_column]], 
                prev_data[[sector_column, rank_column]], 
                on=sector_column, 
                suffixes=('_current', '_prev')
            )
            
            # 计算排名变化绝对值之和
            rank_change_sum = abs(merged[f'{rank_column}_current'] - merged[f'{rank_column}_prev']).sum()
            rank_changes.append({'date': date, 'rank_change': rank_change_sum})
        
        return pd.DataFrame(rank_changes).set_index('date')['rank_change']


class ConcentrationIndicator(BaseIndicator):
    """集中度指标基类"""
    
    def calculate_hhi(self, data: pd.DataFrame, 
                     value_column: str, 
                     date_column: str = 'date') -> pd.Series:
        """计算赫芬达尔-赫希曼指数 (HHI)"""
        def hhi_for_date(group):
            # 计算市场份额
            total = group[value_column].sum()
            if total == 0:
                return 0
            
            market_shares = group[value_column] / total
            # HHI = 各市场份额平方之和
            return (market_shares ** 2).sum()
        
        return data.groupby(date_column).apply(hhi_for_date)
    
    def calculate_concentration_ratio(self, data: pd.DataFrame, 
                                    value_column: str, 
                                    top_n: int = 4,
                                    date_column: str = 'date') -> pd.Series:
        """计算集中度比率 (CR_n)"""
        def cr_for_date(group):
            # 按值排序，取前n个
            sorted_values = group[value_column].sort_values(ascending=False)
            top_n_sum = sorted_values.head(top_n).sum()
            total = group[value_column].sum()
            
            if total == 0:
                return 0
            
            return top_n_sum / total
        
        return data.groupby(date_column).apply(cr_for_date)


class MomentumIndicator(TimeSeriesIndicator):
    """动量指标基类"""
    
    def calculate_momentum(self, prices: pd.DataFrame, 
                          lookback_period: int,
                          price_column: str = 'sector_close_price',
                          date_column: str = 'date',
                          sector_column: str = 'sector_code') -> pd.DataFrame:
        """计算动量"""
        momentum_data = []
        
        for sector in prices[sector_column].unique():
            sector_data = prices[prices[sector_column] == sector].copy()
            sector_data = sector_data.sort_values(date_column)
            
            # 计算动量（lookback_period期间的累计收益率）
            sector_data['momentum'] = sector_data[price_column].pct_change(periods=lookback_period)
            
            momentum_data.append(sector_data)
        
        return pd.concat(momentum_data, ignore_index=True)


# 通用工具函数
def calculate_cross_sectional_std_dev(data: pd.DataFrame, 
                                    value_column: str, 
                                    date_column: str = 'date') -> pd.Series:
    """通用横截面标准差计算函数"""
    return data.groupby(date_column)[value_column].std()


def calculate_momentum(prices: pd.Series, periods: int) -> pd.Series:
    """通用动量计算函数"""
    return prices.pct_change(periods=periods)


def calculate_rank_change(data: pd.DataFrame, 
                         value_column: str,
                         date_column: str = 'date',
                         group_column: str = 'sector_code') -> pd.Series:
    """通用排名变化计算函数"""
    # 计算排名
    data_with_ranks = data.copy()
    data_with_ranks['rank'] = data_with_ranks.groupby(date_column)[value_column].rank(ascending=False)
    
    # 计算排名变化
    rank_changes = []
    dates = sorted(data_with_ranks[date_column].unique())
    
    for i in range(1, len(dates)):
        current_date = dates[i]
        prev_date = dates[i-1]
        
        current_ranks = data_with_ranks[data_with_ranks[date_column] == current_date]
        prev_ranks = data_with_ranks[data_with_ranks[date_column] == prev_date]
        
        merged = pd.merge(
            current_ranks[[group_column, 'rank']], 
            prev_ranks[[group_column, 'rank']], 
            on=group_column, 
            suffixes=('_current', '_prev')
        )
        
        rank_change_sum = abs(merged['rank_current'] - merged['rank_prev']).sum()
        rank_changes.append({'date': current_date, 'rank_change': rank_change_sum})
    
    return pd.DataFrame(rank_changes).set_index('date')['rank_change']
