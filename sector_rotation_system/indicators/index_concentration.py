"""
指数成交额占比分析模块
计算指定指数占全市场成交额的比重
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from datetime import datetime

from config.settings import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("IndexConcentrationAnalyzer")


class IndexConcentrationAnalyzer:
    """指数成交额占比分析器"""
    
    def __init__(self):
        self.config = config_manager.get_data_config()
        self.viz_config = config_manager.get_visualization_config()
        self.index_list = self.config.index_list
        
    def calculate_index_concentration(self, data: pd.DataFrame,
                                    volume_column: str = 'sector_volume_amount') -> pd.DataFrame:
        """
        计算指数成交额占比
        
        Args:
            data: 包含行业成交金额数据的DataFrame
            volume_column: 成交金额列名
            
        Returns:
            包含指数占比数据的DataFrame
        """
        if data.empty:
            logger.error("Input data is empty")
            return pd.DataFrame()
            
        # 模拟指数成分股映射（实际应用中需要真实的成分股数据）
        # 这里使用简化的行业映射
        index_sector_mapping = self._get_index_sector_mapping()
        
        concentration_data = []
        
        for date, group in data.groupby('date'):
            total_market_volume = group[volume_column].sum()
            
            if total_market_volume > 0:
                date_result = {'date': date, 'total_market_volume': total_market_volume}
                
                # 计算每个指数的占比
                for index_code in self.index_list:
                    index_sectors = index_sector_mapping.get(index_code, [])
                    
                    # 计算该指数相关行业的成交额
                    index_volume = group[group['sector_code'].isin(index_sectors)][volume_column].sum()
                    index_ratio = index_volume / total_market_volume
                    
                    date_result[f'{index_code}_volume'] = index_volume
                    date_result[f'{index_code}_ratio'] = index_ratio
                    
                concentration_data.append(date_result)
                
        concentration_df = pd.DataFrame(concentration_data)
        
        if not concentration_df.empty:
            logger.info(f"Calculated index concentration for {len(concentration_df)} trading days")
        else:
            logger.warning("No valid concentration data calculated")
            
        return concentration_df
        
    def _get_index_sector_mapping(self) -> Dict[str, List[str]]:
        """
        获取指数与行业的映射关系
        
        注意：这是一个简化的映射，实际应用中需要使用真实的成分股数据
        
        Returns:
            指数代码到行业列表的映射
        """
        # 简化的行业映射示例
        mapping = {
            "000300.SH": ["金融", "工业", "消费", "科技"],  # 沪深300：大盘蓝筹
            "000905.SH": ["材料", "医药", "能源", "消费"],  # 中证500：中盘股
            "000852.SH": ["科技", "医药", "消费", "材料"]   # 中证1000：小盘股
        }
        
        # 如果配置中有其他指数，添加默认映射
        for index_code in self.index_list:
            if index_code not in mapping:
                mapping[index_code] = ["工业", "消费"]  # 默认映射
                
        return mapping
        
    def plot_index_concentration_trend(self, concentration_data: pd.DataFrame,
                                     output_dir: str = "output/charts") -> str:
        """
        绘制指数成交额占比趋势图
        
        Args:
            concentration_data: 指数占比数据
            output_dir: 输出目录
            
        Returns:
            保存的文件路径
        """
        if concentration_data.empty:
            logger.error("Concentration data is empty")
            return ""
            
        # 设置绘图样式
        plt.style.use(self.viz_config.style)
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, ax = plt.subplots(figsize=self.viz_config.figure_size)
        
        # 绘制每个指数的占比趋势
        colors = plt.cm.Set1(np.linspace(0, 1, len(self.index_list)))
        
        for i, index_code in enumerate(self.index_list):
            ratio_column = f'{index_code}_ratio'
            if ratio_column in concentration_data.columns:
                ax.plot(concentration_data['date'], 
                       concentration_data[ratio_column],
                       linewidth=2, 
                       marker='o', 
                       markersize=3,
                       color=colors[i],
                       label=self._get_index_name(index_code))
                
        # 设置图表属性
        ax.set_title('指数成交额占比趋势分析', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('成交额占比', fontsize=12)
        ax.legend(loc='best')
        ax.grid(True, alpha=0.3)
        
        # 格式化y轴为百分比
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.1%}'))
        
        # 添加最新值标注
        if not concentration_data.empty:
            latest_date = concentration_data['date'].iloc[-1]
            for index_code in self.index_list:
                ratio_column = f'{index_code}_ratio'
                if ratio_column in concentration_data.columns:
                    latest_ratio = concentration_data[ratio_column].iloc[-1]
                    if pd.notna(latest_ratio):
                        ax.annotate(f'{latest_ratio:.1%}',
                                   xy=(latest_date, latest_ratio),
                                   xytext=(5, 5), textcoords='offset points',
                                   fontsize=8,
                                   ha='left')
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"index_concentration_trend_{timestamp}"
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        file_path = output_path / f"{filename}.png"
        
        fig.savefig(file_path, dpi=self.viz_config.dpi, bbox_inches='tight')
        plt.close(fig)
        
        logger.info(f"Index concentration trend chart saved to {file_path}")
        return str(file_path)
        
    def plot_index_concentration_stacked(self, concentration_data: pd.DataFrame,
                                       output_dir: str = "output/charts") -> str:
        """
        绘制指数成交额占比堆叠图
        
        Args:
            concentration_data: 指数占比数据
            output_dir: 输出目录
            
        Returns:
            保存的文件路径
        """
        if concentration_data.empty:
            logger.error("Concentration data is empty")
            return ""
            
        # 设置绘图样式
        plt.style.use(self.viz_config.style)
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, ax = plt.subplots(figsize=self.viz_config.figure_size)
        
        # 准备堆叠数据
        ratio_columns = [f'{index_code}_ratio' for index_code in self.index_list 
                        if f'{index_code}_ratio' in concentration_data.columns]
        
        if not ratio_columns:
            logger.error("No valid ratio columns found")
            return ""
            
        # 计算其他部分（未被指数覆盖的部分）
        total_index_ratio = concentration_data[ratio_columns].sum(axis=1)
        other_ratio = 1 - total_index_ratio
        
        # 创建堆叠数据
        stack_data = []
        labels = []
        
        for index_code in self.index_list:
            ratio_column = f'{index_code}_ratio'
            if ratio_column in concentration_data.columns:
                stack_data.append(concentration_data[ratio_column])
                labels.append(self._get_index_name(index_code))
                
        # 添加其他部分
        stack_data.append(other_ratio)
        labels.append('其他')
        
        # 绘制堆叠面积图
        colors = plt.cm.Set3(np.linspace(0, 1, len(stack_data)))
        ax.stackplot(concentration_data['date'], *stack_data, 
                    labels=labels, colors=colors, alpha=0.8)
        
        # 设置图表属性
        ax.set_title('指数成交额占比分布（堆叠图）', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('成交额占比', fontsize=12)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
        
        # 格式化y轴为百分比
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0%}'))
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"index_concentration_stacked_{timestamp}"
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        file_path = output_path / f"{filename}.png"
        
        fig.savefig(file_path, dpi=self.viz_config.dpi, bbox_inches='tight')
        plt.close(fig)
        
        logger.info(f"Index concentration stacked chart saved to {file_path}")
        return str(file_path)
        
    def generate_concentration_summary(self, concentration_data: pd.DataFrame) -> Dict[str, any]:
        """
        生成指数占比分析摘要
        
        Args:
            concentration_data: 指数占比数据
            
        Returns:
            摘要字典
        """
        if concentration_data.empty:
            return {}
            
        summary = {
            'total_periods': len(concentration_data),
            'analysis_start_date': concentration_data['date'].min(),
            'analysis_end_date': concentration_data['date'].max(),
            'index_statistics': {}
        }
        
        # 计算每个指数的统计信息
        for index_code in self.index_list:
            ratio_column = f'{index_code}_ratio'
            if ratio_column in concentration_data.columns:
                ratios = concentration_data[ratio_column].dropna()
                
                if len(ratios) > 0:
                    summary['index_statistics'][index_code] = {
                        'name': self._get_index_name(index_code),
                        'mean_ratio': ratios.mean(),
                        'std_ratio': ratios.std(),
                        'min_ratio': ratios.min(),
                        'max_ratio': ratios.max(),
                        'latest_ratio': ratios.iloc[-1] if len(ratios) > 0 else np.nan
                    }
                    
        logger.info("Generated index concentration summary")
        return summary
        
    def _get_index_name(self, index_code: str) -> str:
        """获取指数中文名称"""
        name_mapping = {
            "000300.SH": "沪深300",
            "000905.SH": "中证500", 
            "000852.SH": "中证1000"
        }
        return name_mapping.get(index_code, index_code)
        
    def save_concentration_data(self, concentration_data: pd.DataFrame,
                              output_dir: str, filename: str) -> str:
        """
        保存指数占比数据
        
        Args:
            concentration_data: 指数占比数据
            output_dir: 输出目录
            filename: 文件名
            
        Returns:
            保存的文件路径
        """
        if concentration_data.empty:
            logger.warning("Concentration data is empty, nothing to save")
            return ""
            
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存为CSV
        csv_path = output_path / f"{filename}.csv"
        concentration_data.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        # 保存为Excel（如果需要）
        excel_path = output_path / f"{filename}.xlsx"
        try:
            concentration_data.to_excel(excel_path, index=False, engine='openpyxl')
            logger.info(f"Index concentration data saved to {excel_path}")
        except ImportError:
            logger.warning("openpyxl not available, Excel file not saved")
            
        logger.info(f"Index concentration data saved to {csv_path}")
        return str(csv_path)
