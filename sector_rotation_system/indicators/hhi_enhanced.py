"""
HHI增强模块
为赫芬达尔-赫希曼指数添加集中度阈值分析功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from datetime import datetime

from config.settings import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("HHIEnhanced")


class HHIEnhancedAnalyzer:
    """HHI增强分析器"""
    
    def __init__(self):
        self.config = config_manager.get_indicator_config()
        self.viz_config = config_manager.get_visualization_config()
        self.low_threshold_pct = self.config.hhi_low_threshold_percentile
        self.high_threshold_pct = self.config.hhi_high_threshold_percentile
        
    def calculate_hhi_with_thresholds(self, data: pd.DataFrame,
                                    volume_column: str = 'sector_volume_amount') -> pd.DataFrame:
        """
        计算HHI并添加集中度阈值分析
        
        Args:
            data: 包含成交金额数据的DataFrame
            volume_column: 成交金额列名
            
        Returns:
            包含HHI和阈值信息的DataFrame
        """
        if data.empty:
            logger.error("Input data is empty")
            return pd.DataFrame()
            
        # 计算每日HHI
        hhi_data = []
        
        for date, group in data.groupby('date'):
            total_volume = group[volume_column].sum()
            
            if total_volume > 0:
                # 计算各行业占比
                ratios = group[volume_column] / total_volume
                # 计算HHI
                hhi = (ratios ** 2).sum()
                
                hhi_data.append({
                    'date': date,
                    'hhi': hhi,
                    'total_volume': total_volume,
                    'sector_count': len(group)
                })
                
        hhi_df = pd.DataFrame(hhi_data)
        
        if hhi_df.empty:
            logger.error("No valid HHI data calculated")
            return pd.DataFrame()
            
        # 计算历史阈值
        hhi_values = hhi_df['hhi'].dropna()
        
        if len(hhi_values) > 0:
            low_threshold = np.percentile(hhi_values, self.low_threshold_pct)
            high_threshold = np.percentile(hhi_values, self.high_threshold_pct)
            
            # 添加阈值信息
            hhi_df['low_threshold'] = low_threshold
            hhi_df['high_threshold'] = high_threshold
            
            # 分类集中度水平
            hhi_df['concentration_level'] = hhi_df['hhi'].apply(
                lambda x: self._classify_concentration_level(x, low_threshold, high_threshold)
            )
            
            logger.info(f"Calculated HHI with thresholds: low={low_threshold:.4f}, high={high_threshold:.4f}")
        else:
            logger.warning("No valid HHI values for threshold calculation")
            
        return hhi_df
        
    def _classify_concentration_level(self, hhi_value: float, 
                                    low_threshold: float, 
                                    high_threshold: float) -> str:
        """
        分类集中度水平
        
        Args:
            hhi_value: HHI值
            low_threshold: 低集中度阈值
            high_threshold: 高集中度阈值
            
        Returns:
            集中度水平字符串
        """
        if pd.isna(hhi_value):
            return "未知"
        elif hhi_value >= high_threshold:
            return "高集中度"
        elif hhi_value <= low_threshold:
            return "低集中度"
        else:
            return "中等集中度"
            
    def plot_hhi_with_thresholds(self, hhi_data: pd.DataFrame,
                               output_dir: str = "output/charts") -> str:
        """
        绘制带阈值参考线的HHI图表
        
        Args:
            hhi_data: 包含HHI和阈值数据的DataFrame
            output_dir: 输出目录
            
        Returns:
            保存的文件路径
        """
        if hhi_data.empty:
            logger.error("HHI data is empty")
            return ""
            
        # 设置绘图样式
        plt.style.use(self.viz_config.style)
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, ax = plt.subplots(figsize=self.viz_config.figure_size)
        
        # 绘制HHI时间序列
        ax.plot(hhi_data['date'], hhi_data['hhi'], 
               linewidth=2, color='navy', label='HHI指数')
        
        # 添加阈值参考线
        if 'low_threshold' in hhi_data.columns and 'high_threshold' in hhi_data.columns:
            low_threshold = hhi_data['low_threshold'].iloc[0]
            high_threshold = hhi_data['high_threshold'].iloc[0]
            
            ax.axhline(y=low_threshold, color='green', linestyle='--', 
                      alpha=0.7, label=f'低集中度阈值 ({self.low_threshold_pct}%分位)')
            ax.axhline(y=high_threshold, color='red', linestyle='--', 
                      alpha=0.7, label=f'高集中度阈值 ({self.high_threshold_pct}%分位)')
            
            # 添加集中度区间背景色
            ax.fill_between(hhi_data['date'], 0, low_threshold, 
                           alpha=0.1, color='green', label='低集中度区间')
            ax.fill_between(hhi_data['date'], high_threshold, hhi_data['hhi'].max() * 1.1, 
                           alpha=0.1, color='red', label='高集中度区间')
            
        # 设置图表属性
        ax.set_title('行业成交金额集中度分析 (HHI)', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('HHI指数', fontsize=12)
        ax.legend(loc='best')
        ax.grid(True, alpha=0.3)
        
        # 添加最新值注释
        if not hhi_data.empty:
            latest_hhi = hhi_data['hhi'].iloc[-1]
            latest_date = hhi_data['date'].iloc[-1]
            latest_level = hhi_data['concentration_level'].iloc[-1] if 'concentration_level' in hhi_data.columns else "未知"
            
            ax.annotate(f'最新HHI: {latest_hhi:.4f}\n集中度: {latest_level}',
                       xy=(latest_date, latest_hhi),
                       xytext=(10, 10), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"hhi_enhanced_analysis_{timestamp}"
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        file_path = output_path / f"{filename}.png"
        
        fig.savefig(file_path, dpi=self.viz_config.dpi, bbox_inches='tight')
        plt.close(fig)
        
        logger.info(f"Enhanced HHI chart saved to {file_path}")
        return str(file_path)
        
    def generate_concentration_summary(self, hhi_data: pd.DataFrame) -> Dict[str, any]:
        """
        生成集中度分析摘要
        
        Args:
            hhi_data: HHI数据
            
        Returns:
            集中度分析摘要字典
        """
        if hhi_data.empty:
            return {}
            
        summary = {}
        
        # 基本统计
        hhi_values = hhi_data['hhi'].dropna()
        if len(hhi_values) > 0:
            summary['total_periods'] = len(hhi_values)
            summary['mean_hhi'] = hhi_values.mean()
            summary['std_hhi'] = hhi_values.std()
            summary['min_hhi'] = hhi_values.min()
            summary['max_hhi'] = hhi_values.max()
            
            # 阈值信息
            if 'low_threshold' in hhi_data.columns and 'high_threshold' in hhi_data.columns:
                summary['low_threshold'] = hhi_data['low_threshold'].iloc[0]
                summary['high_threshold'] = hhi_data['high_threshold'].iloc[0]
                
                # 集中度水平分布
                if 'concentration_level' in hhi_data.columns:
                    level_counts = hhi_data['concentration_level'].value_counts()
                    summary['concentration_distribution'] = level_counts.to_dict()
                    
                    # 计算各水平占比
                    total_count = len(hhi_data)
                    summary['concentration_percentages'] = {
                        level: count / total_count * 100 
                        for level, count in level_counts.items()
                    }
            
            # 最新状态
            if not hhi_data.empty:
                summary['latest_hhi'] = hhi_data['hhi'].iloc[-1]
                summary['latest_date'] = hhi_data['date'].iloc[-1]
                summary['latest_level'] = hhi_data['concentration_level'].iloc[-1] if 'concentration_level' in hhi_data.columns else "未知"
                
        logger.info("Generated concentration summary")
        return summary
        
    def create_concentration_report(self, hhi_data: pd.DataFrame) -> str:
        """
        创建集中度分析报告
        
        Args:
            hhi_data: HHI数据
            
        Returns:
            Markdown格式的报告内容
        """
        summary = self.generate_concentration_summary(hhi_data)
        
        if not summary:
            return "## 集中度分析报告\n\n数据不足，无法生成报告。\n"
            
        report = "## 行业成交金额集中度分析报告\n\n"
        
        # 基本信息
        report += "### 基本统计信息\n\n"
        report += f"- 分析期间: {summary.get('total_periods', 0)} 个交易日\n"
        report += f"- 平均HHI: {summary.get('mean_hhi', 0):.4f}\n"
        report += f"- HHI标准差: {summary.get('std_hhi', 0):.4f}\n"
        report += f"- HHI最小值: {summary.get('min_hhi', 0):.4f}\n"
        report += f"- HHI最大值: {summary.get('max_hhi', 0):.4f}\n\n"
        
        # 阈值信息
        if 'low_threshold' in summary and 'high_threshold' in summary:
            report += "### 集中度阈值\n\n"
            report += f"- 低集中度阈值 ({self.low_threshold_pct}%分位): {summary['low_threshold']:.4f}\n"
            report += f"- 高集中度阈值 ({self.high_threshold_pct}%分位): {summary['high_threshold']:.4f}\n\n"
            
        # 集中度分布
        if 'concentration_distribution' in summary:
            report += "### 集中度水平分布\n\n"
            for level, count in summary['concentration_distribution'].items():
                percentage = summary['concentration_percentages'].get(level, 0)
                report += f"- {level}: {count} 天 ({percentage:.1f}%)\n"
            report += "\n"
            
        # 最新状态
        if 'latest_hhi' in summary:
            report += "### 最新状态\n\n"
            report += f"- 最新日期: {summary['latest_date']}\n"
            report += f"- 最新HHI: {summary['latest_hhi']:.4f}\n"
            report += f"- 集中度水平: {summary['latest_level']}\n\n"
            
        return report
