"""
模块2：指标计算引擎
开发一个核心模块，用于计算所有已定义的行业轮动速度指标
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any
from scipy import stats

from config.settings import config_manager
from utils.logger import get_module_logger
from indicators.base import (
    BaseIndicator, CrossSectionalIndicator, TimeSeriesIndicator,
    RankingIndicator, ConcentrationIndicator, MomentumIndicator
)

logger = get_module_logger("IndicatorCalculator")


class SectorRankingChangeIndicator(RankingIndicator):
    """行业排名变化指标"""

    def __init__(self):
        super().__init__(
            name="sector_ranking_change",
            description="基于行业涨跌幅排名计算每日变动绝对值之和"
        )

    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """计算行业排名变化指标"""
        required_columns = ['date', 'sector_code', 'sector_close_price']
        if not self.validate_data(data, required_columns):
            return pd.Series()

        # 计算日收益率
        data_with_returns = data.copy()
        data_with_returns = data_with_returns.sort_values(['sector_code', 'date'])
        data_with_returns['daily_return'] = data_with_returns.groupby('sector_code')['sector_close_price'].pct_change()

        # 移除第一天的数据（没有收益率）
        data_with_returns = data_with_returns.dropna(subset=['daily_return'])

        # 计算排名变化
        return self.calculate_rank_changes(
            self.calculate_ranks(data_with_returns, 'daily_return', ascending=False),
            rank_column='rank'
        )


class RelativeStrengthIndicator(CrossSectionalIndicator):
    """相对强度指标"""

    def __init__(self):
        super().__init__(
            name="relative_strength",
            description="计算行业指数与基准指数相对强度的每日横截面标准差"
        )

    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """计算相对强度指标"""
        required_columns = ['date', 'sector_code', 'sector_close_price', 'benchmark_price']
        if not self.validate_data(data, required_columns):
            return pd.Series()

        # 计算相对强度
        data_with_rs = data.copy()
        data_with_rs['relative_strength'] = data_with_rs['sector_close_price'] / data_with_rs['benchmark_price']

        # 计算横截面标准差
        return self.calculate_cross_sectional_std(data_with_rs, 'relative_strength')


class SectorReturnDispersionIndicator(CrossSectionalIndicator):
    """行业收益率离散度指标"""

    def __init__(self):
        super().__init__(
            name="sector_return_dispersion",
            description="计算每日所有行业收益率的横截面标准差"
        )

    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """计算行业收益率离散度指标"""
        required_columns = ['date', 'sector_code', 'sector_close_price']
        if not self.validate_data(data, required_columns):
            return pd.Series()

        # 计算日收益率
        data_with_returns = data.copy()
        data_with_returns = data_with_returns.sort_values(['sector_code', 'date'])
        data_with_returns['daily_return'] = data_with_returns.groupby('sector_code')['sector_close_price'].pct_change()

        # 移除第一天的数据
        data_with_returns = data_with_returns.dropna(subset=['daily_return'])

        # 计算横截面标准差
        return self.calculate_cross_sectional_std(data_with_returns, 'daily_return')


class VolumeConcentrationIndicator(ConcentrationIndicator):
    """行业成交金额集中度指标"""

    def __init__(self):
        super().__init__(
            name="volume_concentration",
            description="计算每日行业成交金额的赫芬达尔-赫希曼指数 (HHI)"
        )

    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """计算成交金额集中度指标"""
        required_columns = ['date', 'sector_code', 'sector_volume_amount']
        if not self.validate_data(data, required_columns):
            return pd.Series()

        return self.calculate_hhi(data, 'sector_volume_amount')


class ReturnDistributionIndicator(CrossSectionalIndicator):
    """行业涨跌幅分布特征指标"""

    def __init__(self):
        super().__init__(
            name="return_distribution",
            description="计算每日所有行业涨跌幅的偏度和峰度"
        )

    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """计算涨跌幅分布特征指标"""
        required_columns = ['date', 'sector_code', 'sector_close_price']
        if not self.validate_data(data, required_columns):
            return pd.DataFrame()

        # 计算日收益率
        data_with_returns = data.copy()
        data_with_returns = data_with_returns.sort_values(['sector_code', 'date'])
        data_with_returns['daily_return'] = data_with_returns.groupby('sector_code')['sector_close_price'].pct_change()

        # 移除第一天的数据
        data_with_returns = data_with_returns.dropna(subset=['daily_return'])

        # 计算偏度和峰度
        skewness = self.calculate_cross_sectional_skewness(data_with_returns, 'daily_return')
        kurtosis = self.calculate_cross_sectional_kurtosis(data_with_returns, 'daily_return')

        result = pd.DataFrame({
            'return_skewness': skewness,
            'return_kurtosis': kurtosis
        })

        return result


class MomentumRankingChangeIndicator(MomentumIndicator, RankingIndicator):
    """动量因子排名变化率指标"""

    def __init__(self):
        # 调用 BaseIndicator 的初始化
        BaseIndicator.__init__(
            self,
            name="momentum_ranking_change",
            description="基于指定回溯期内的行业累计收益率排名计算每日变动绝对值之和"
        )

    def calculate(self, data: pd.DataFrame, lookback_period: int = 20, **kwargs) -> pd.Series:
        """计算动量排名变化率指标"""
        required_columns = ['date', 'sector_code', 'sector_close_price']
        if not self.validate_data(data, required_columns):
            return pd.Series()

        # 计算动量
        momentum_data = self.calculate_momentum(data, lookback_period)

        # 移除缺失值
        momentum_data = momentum_data.dropna(subset=['momentum'])

        # 计算排名变化
        return self.calculate_rank_changes(
            self.calculate_ranks(momentum_data, 'momentum', ascending=False),
            rank_column='rank'
        )


class IndicatorCalculationEngine:
    """指标计算引擎主类"""

    def __init__(self):
        self.config = config_manager.get_indicator_config()
        self.indicators = self._initialize_indicators()

    def _initialize_indicators(self) -> Dict[str, BaseIndicator]:
        """初始化所有指标"""
        indicators = {
            'sector_ranking_change': SectorRankingChangeIndicator(),
            'relative_strength': RelativeStrengthIndicator(),
            'sector_return_dispersion': SectorReturnDispersionIndicator(),
            'volume_concentration': VolumeConcentrationIndicator(),
            'return_distribution': ReturnDistributionIndicator(),
            'momentum_ranking_change': MomentumRankingChangeIndicator()
        }

        logger.info(f"Initialized {len(indicators)} indicators")
        return indicators

    def calculate_all_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算所有指标"""
        logger.info("Starting calculation of all indicators")

        if data.empty:
            logger.warning("Input data is empty")
            return pd.DataFrame()

        # 验证输入数据
        required_columns = ['date', 'sector_code', 'sector_close_price', 'sector_volume_amount', 'benchmark_price']
        missing_columns = set(required_columns) - set(data.columns)
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            return pd.DataFrame()

        results = {}

        # 计算各项指标
        try:
            # 1. 行业排名变化指标
            results['sector_ranking_change'] = self.indicators['sector_ranking_change'].calculate(data)

            # 2. 相对强度指标
            results['relative_strength_dispersion'] = self.indicators['relative_strength'].calculate(data)

            # 3. 行业收益率离散度
            results['sector_return_dispersion'] = self.indicators['sector_return_dispersion'].calculate(data)

            # 4. 成交金额HHI
            results['volume_hhi'] = self.indicators['volume_concentration'].calculate(data)

            # 5. 涨跌幅分布特征
            distribution_results = self.indicators['return_distribution'].calculate(data)
            if not distribution_results.empty:
                results['return_skewness'] = distribution_results['return_skewness']
                results['return_kurtosis'] = distribution_results['return_kurtosis']

            # 6. 动量排名变化率
            results['momentum_ranking_change'] = self.indicators['momentum_ranking_change'].calculate(
                data,
                lookback_period=self.config.momentum_lookback_period
            )

        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return pd.DataFrame()

        # 合并结果
        final_results = self._merge_results(results)

        logger.info(f"Indicator calculation completed. Result shape: {final_results.shape}")
        return final_results

    def calculate_single_indicator(self, indicator_name: str, data: pd.DataFrame, **kwargs) -> Union[pd.Series, pd.DataFrame]:
        """计算单个指标"""
        if indicator_name not in self.indicators:
            logger.error(f"Unknown indicator: {indicator_name}")
            return pd.Series()

        logger.info(f"Calculating indicator: {indicator_name}")

        try:
            if indicator_name == 'momentum_ranking_change':
                kwargs.setdefault('lookback_period', self.config.momentum_lookback_period)

            result = self.indicators[indicator_name].calculate(data, **kwargs)
            logger.info(f"Indicator {indicator_name} calculated successfully")
            return result

        except Exception as e:
            logger.error(f"Error calculating indicator {indicator_name}: {e}")
            return pd.Series()

    def _merge_results(self, results: Dict[str, Union[pd.Series, pd.DataFrame]]) -> pd.DataFrame:
        """合并所有指标结果"""
        if not results:
            return pd.DataFrame()

        # 获取所有日期的并集
        all_dates = set()
        for result in results.values():
            if isinstance(result, pd.Series):
                all_dates.update(result.index)
            elif isinstance(result, pd.DataFrame):
                all_dates.update(result.index)

        all_dates = sorted(list(all_dates))

        # 创建结果DataFrame
        final_df = pd.DataFrame(index=all_dates)
        final_df.index.name = 'date'

        # 添加各项指标
        for indicator_name, result in results.items():
            if isinstance(result, pd.Series):
                final_df[indicator_name] = result
            elif isinstance(result, pd.DataFrame):
                for col in result.columns:
                    final_df[f"{indicator_name}_{col}"] = result[col]

        # 重置索引，将日期作为列
        final_df = final_df.reset_index()

        return final_df

    def get_indicator_descriptions(self) -> Dict[str, str]:
        """获取所有指标的描述"""
        descriptions = {}
        for name, indicator in self.indicators.items():
            descriptions[name] = indicator.description
        return descriptions

    def validate_input_data(self, data: pd.DataFrame) -> bool:
        """验证输入数据的完整性"""
        required_columns = ['date', 'sector_code', 'sector_close_price', 'sector_volume_amount', 'benchmark_price']

        # 检查必需列
        missing_columns = set(required_columns) - set(data.columns)
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            return False

        # 检查数据量
        if len(data) < self.config.min_data_points:
            logger.error(f"Insufficient data points: {len(data)} < {self.config.min_data_points}")
            return False

        # 检查日期范围
        unique_dates = data['date'].nunique()
        if unique_dates < 2:
            logger.error(f"Insufficient date range: {unique_dates} unique dates")
            return False

        # 检查行业数量
        unique_sectors = data['sector_code'].nunique()
        if unique_sectors < 2:
            logger.error(f"Insufficient sectors: {unique_sectors} unique sectors")
            return False

        logger.info(f"Data validation passed: {len(data)} records, {unique_dates} dates, {unique_sectors} sectors")
        return True
