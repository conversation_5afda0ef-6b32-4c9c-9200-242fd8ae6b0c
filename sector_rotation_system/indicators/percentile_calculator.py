"""
历史百分位计算模块
为指标添加历史百分位计算功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from config.settings import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("PercentileCalculator")


class PercentileCalculator:
    """历史百分位计算器"""
    
    def __init__(self):
        self.config = config_manager.get_indicator_config()
        self.window = self.config.percentile_window
        
    def calculate_rolling_percentiles(self, data: pd.DataFrame,
                                    indicator_columns: List[str] = None) -> pd.DataFrame:
        """
        计算滚动历史百分位

        Args:
            data: 包含指标数据的DataFrame，必须包含date列
            indicator_columns: 要计算百分位的指标列，如果为None则自动检测

        Returns:
            包含百分位信息的DataFrame
        """
        if data.empty:
            logger.error("Input data is empty")
            return pd.DataFrame()

        if 'date' not in data.columns:
            logger.error("Data must contain 'date' column")
            return pd.DataFrame()

        # 确保数据按日期排序
        data = data.sort_values('date').copy()

        # 自动检测指标列
        if indicator_columns is None:
            numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()
            indicator_columns = [col for col in numeric_columns if col != 'date']

        if not indicator_columns:
            logger.warning("No indicator columns found")
            return data.copy()

        result_data = data.copy()

        # 动态调整窗口大小
        data_length = len(data)
        effective_window = min(self.window, data_length)

        # 如果数据太少，使用最小窗口
        if effective_window < 10:
            effective_window = max(10, data_length // 2)

        logger.info(f"Using effective window size: {effective_window} (original: {self.window}, data length: {data_length})")

        # 为每个指标计算历史百分位
        for col in indicator_columns:
            if col not in data.columns:
                logger.warning(f"Column {col} not found in data")
                continue

            percentile_col = f"{col}_percentile"
            result_data[percentile_col] = self._calculate_single_indicator_percentile(
                data[col], effective_window
            )

            logger.info(f"Calculated percentiles for {col}")

        return result_data
        
    def _calculate_single_indicator_percentile(self, series: pd.Series, 
                                             window: int) -> pd.Series:
        """
        计算单个指标的滚动百分位
        
        Args:
            series: 指标数据序列
            window: 滚动窗口大小
            
        Returns:
            百分位序列
        """
        percentiles = []
        
        for i in range(len(series)):
            if i < window - 1:
                # 数据不足时使用NaN
                percentiles.append(np.nan)
            else:
                # 获取历史窗口数据
                historical_data = series.iloc[max(0, i - window + 1):i + 1]
                current_value = series.iloc[i]
                
                if pd.isna(current_value) or len(historical_data.dropna()) == 0:
                    percentiles.append(np.nan)
                else:
                    # 计算当前值在历史数据中的百分位
                    percentile = (historical_data <= current_value).sum() / len(historical_data) * 100
                    percentiles.append(percentile)
                    
        return pd.Series(percentiles, index=series.index)
        
    def get_latest_percentiles(self, data: pd.DataFrame, 
                             indicator_columns: List[str] = None) -> Dict[str, float]:
        """
        获取最新的百分位数值
        
        Args:
            data: 包含百分位数据的DataFrame
            indicator_columns: 指标列名列表
            
        Returns:
            最新百分位字典
        """
        if data.empty:
            return {}
            
        if indicator_columns is None:
            # 查找所有百分位列
            percentile_columns = [col for col in data.columns if col.endswith('_percentile')]
        else:
            percentile_columns = [f"{col}_percentile" for col in indicator_columns 
                                if f"{col}_percentile" in data.columns]
            
        latest_percentiles = {}
        
        for col in percentile_columns:
            latest_value = data[col].iloc[-1] if not data[col].empty else np.nan
            if pd.notna(latest_value):
                # 移除_percentile后缀获取原指标名
                indicator_name = col.replace('_percentile', '')
                latest_percentiles[indicator_name] = latest_value
                
        return latest_percentiles
        
    def classify_percentile_level(self, percentile: float) -> str:
        """
        根据百分位数值分类水平
        
        Args:
            percentile: 百分位数值 (0-100)
            
        Returns:
            水平分类字符串
        """
        if pd.isna(percentile):
            return "未知"
        elif percentile >= 90:
            return "极高"
        elif percentile >= 75:
            return "高"
        elif percentile >= 50:
            return "中等偏高"
        elif percentile >= 25:
            return "中等偏低"
        elif percentile >= 10:
            return "低"
        else:
            return "极低"
            
    def generate_percentile_summary(self, data: pd.DataFrame,
                                  indicator_columns: List[str] = None) -> pd.DataFrame:
        """
        生成百分位汇总表
        
        Args:
            data: 包含百分位数据的DataFrame
            indicator_columns: 指标列名列表
            
        Returns:
            百分位汇总DataFrame
        """
        latest_percentiles = self.get_latest_percentiles(data, indicator_columns)
        
        if not latest_percentiles:
            logger.warning("No percentile data found")
            return pd.DataFrame()
            
        summary_data = []
        
        for indicator, percentile in latest_percentiles.items():
            # 获取最新指标值
            latest_value = data[indicator].iloc[-1] if indicator in data.columns else np.nan
            
            summary_data.append({
                'indicator': indicator,
                'latest_value': latest_value,
                'percentile': percentile,
                'level': self.classify_percentile_level(percentile),
                'description': self._get_percentile_description(indicator, percentile)
            })
            
        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values('percentile', ascending=False)
        
        logger.info(f"Generated percentile summary for {len(summary_df)} indicators")
        return summary_df
        
    def _get_percentile_description(self, indicator: str, percentile: float) -> str:
        """
        获取百分位描述
        
        Args:
            indicator: 指标名称
            percentile: 百分位数值
            
        Returns:
            描述字符串
        """
        if pd.isna(percentile):
            return "数据不足"
            
        level = self.classify_percentile_level(percentile)
        
        descriptions = {
            "极高": f"当前{indicator}处于历史{percentile:.1f}%分位，属于极高水平",
            "高": f"当前{indicator}处于历史{percentile:.1f}%分位，属于较高水平",
            "中等偏高": f"当前{indicator}处于历史{percentile:.1f}%分位，属于中等偏高水平",
            "中等偏低": f"当前{indicator}处于历史{percentile:.1f}%分位，属于中等偏低水平",
            "低": f"当前{indicator}处于历史{percentile:.1f}%分位，属于较低水平",
            "极低": f"当前{indicator}处于历史{percentile:.1f}%分位，属于极低水平"
        }
        
        return descriptions.get(level, f"当前{indicator}处于历史{percentile:.1f}%分位")
        
    def add_percentile_annotations_to_chart_data(self, data: pd.DataFrame,
                                               indicator_column: str) -> Dict[str, any]:
        """
        为图表添加百分位注释数据
        
        Args:
            data: 包含指标和百分位数据的DataFrame
            indicator_column: 指标列名
            
        Returns:
            包含注释信息的字典
        """
        percentile_column = f"{indicator_column}_percentile"
        
        if percentile_column not in data.columns:
            logger.warning(f"Percentile column {percentile_column} not found")
            return {}
            
        # 获取最新数据
        latest_value = data[indicator_column].iloc[-1]
        latest_percentile = data[percentile_column].iloc[-1]
        latest_date = data['date'].iloc[-1] if 'date' in data.columns else None
        
        annotation_data = {
            'latest_value': latest_value,
            'latest_percentile': latest_percentile,
            'latest_date': latest_date,
            'level': self.classify_percentile_level(latest_percentile),
            'annotation_text': f"最新值: {latest_value:.4f}\n历史百分位: {latest_percentile:.1f}%"
        }
        
        return annotation_data
