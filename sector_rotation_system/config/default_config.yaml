indicator:
  momentum_lookback_period: 20
  ranking_window: 5
  min_data_points: 30
  # 历史百分位计算参数
  percentile_window: 252  # 一年交易日
  # HHI集中度阈值参数
  hhi_low_threshold_percentile: 25
  hhi_high_threshold_percentile: 75

data:
  benchmark_index: "000300.SH"
  benchmark_name: "沪深300"  # 基准指数名称
  data_source: "csv"
  storage_format: "parquet"
  date_format: "%Y-%m-%d"
  # 数据文件配置
  unified_data_file: "data/raw/unified_sector_data.csv"  # 统一数据文件路径
  use_unified_file: true  # 是否使用统一数据文件
  # 指数列表配置
  index_list:
    - "000300.SH"  # 沪深300
    - "000905.SH"  # 中证500
    - "000852.SH"  # 中证1000

visualization:
  figure_size: [12, 8]
  dpi: 300
  style: "seaborn-v0_8"
  color_palette: "husl"
  # 移除多指标对比分析
  enable_multi_indicator_comparison: false
  # 新增可视化选项
  top_sectors_count: 10  # 成交额占比前十行业
  correlation_threshold_high: 0.7
  correlation_threshold_low: -0.7

storage:
  base_path: "data/output"
  backup_enabled: true
  compression: "snappy"
  # 按日期组织输出
  use_date_folders: true
  date_folder_format: "%Y%m%d_%H%M%S"

# 新增报告配置
reporting:
  # 时间段配置
  time_periods:
    weekly: 5      # 最近5个交易日
    monthly: 22    # 当月交易日（近似）
    yearly: 252    # 当年交易日（近似）
  # 表格排序方式
  sort_by: "weekly"  # 按本周涨跌幅排序
