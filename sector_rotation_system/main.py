"""
股票行业轮动速度量化系统主程序
整合所有模块，提供完整的分析流程
"""

import argparse
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import pandas as pd

from config.settings import config_manager
from data.preprocessor import DataPreprocessor
from data.storage import DataStorageManager
from indicators.calculator import IndicatorCalculationEngine
from indicators.percentile_calculator import PercentileCalculator
from indicators.hhi_enhanced import HHIEnhancedAnalyzer
from indicators.index_concentration import IndexConcentrationAnalyzer
from visualization.plotter import VisualizationManager
from visualization.enhanced_plotter import ConcentrationPlotter, CorrelationPlotter
from reporting.sector_performance import SectorPerformanceReporter
from reporting.table_generator import TableGenerator
from reporting.comprehensive_report import ComprehensiveReporter
from utils.logger import system_logger
from utils.output_manager import OutputManager


class SectorRotationAnalysisSystem:
    """行业轮动分析系统主类"""
    
    def __init__(self):
        self.config = config_manager.config
        self.preprocessor = DataPreprocessor()
        self.storage_manager = DataStorageManager()
        self.indicator_engine = IndicatorCalculationEngine()
        self.visualization_manager = VisualizationManager()

        # 新增模块
        self.percentile_calculator = PercentileCalculator()
        self.hhi_analyzer = HHIEnhancedAnalyzer()
        self.index_analyzer = IndexConcentrationAnalyzer()
        self.concentration_plotter = ConcentrationPlotter()
        self.correlation_plotter = CorrelationPlotter()
        self.performance_reporter = SectorPerformanceReporter()
        self.table_generator = TableGenerator()
        self.comprehensive_reporter = ComprehensiveReporter()
        self.output_manager = OutputManager()

        system_logger.info("Sector Rotation Analysis System initialized with enhanced features")
    
    def run_full_analysis(self, start_date: str, end_date: str,
                         output_prefix: str = "sector_rotation",
                         custom_start_date: Optional[str] = None) -> Dict[str, Any]:
        """运行完整分析流程（增强版）"""
        system_logger.info(f"Starting enhanced full analysis from {start_date} to {end_date}")

        try:
            # 0. 创建输出目录结构
            system_logger.info("Step 0: Creating output structure")
            output_paths = self.output_manager.create_output_structure()

            # 1. 数据预处理
            system_logger.info("Step 1: Data preprocessing")
            raw_data = self.preprocessor.load_and_preprocess(start_date, end_date)

            if raw_data.empty:
                system_logger.error("No data loaded. Analysis cannot proceed.")
                return {"success": False, "error": "No data available"}

            system_logger.info(f"Loaded {len(raw_data)} records with daily_return field")

            # 2. 指标计算
            system_logger.info("Step 2: Indicator calculation")
            if not self.indicator_engine.validate_input_data(raw_data):
                system_logger.error("Data validation failed")
                return {"success": False, "error": "Data validation failed"}

            indicators_data = self.indicator_engine.calculate_all_indicators(raw_data)

            if indicators_data.empty:
                system_logger.error("Indicator calculation failed")
                return {"success": False, "error": "Indicator calculation failed"}

            system_logger.info(f"Calculated indicators for {len(indicators_data)} periods")

            # 3. 历史百分位计算
            system_logger.info("Step 3: Historical percentile calculation")
            indicators_with_percentiles = self.percentile_calculator.calculate_rolling_percentiles(
                indicators_data
            )

            # 4. HHI增强分析
            system_logger.info("Step 4: Enhanced HHI analysis")
            hhi_enhanced_data = self.hhi_analyzer.calculate_hhi_with_thresholds(raw_data)

            # 5. 行业表现报告
            system_logger.info("Step 5: Sector performance reporting")
            performance_table = self.performance_reporter.generate_sector_performance_table(
                raw_data, end_date, custom_start_date
            )

            # 6. 相关性分析
            system_logger.info("Step 6: Correlation analysis")
            correlation_matrix = self.table_generator.create_correlation_table(raw_data)
            correlation_pairs = self.table_generator.identify_high_correlation_pairs(correlation_matrix)

            # 7. 指数占比分析
            system_logger.info("Step 7: Index concentration analysis")
            index_concentration_data = self.index_analyzer.calculate_index_concentration(raw_data)

            # 8. 数据存储
            system_logger.info("Step 8: Data storage")
            date_range = f"{start_date}_{end_date}"
            storage_success = self.storage_manager.save_indicators(indicators_with_percentiles, date_range)

            # 保存表格数据
            if not performance_table.empty:
                self.performance_reporter.save_performance_table(
                    performance_table, output_paths['tables'], f"sector_performance_{output_paths['timestamp']}"
                )

            if not correlation_matrix.empty:
                self.table_generator.save_table_to_multiple_formats(
                    correlation_matrix, output_paths['tables'], f"correlation_matrix_{output_paths['timestamp']}"
                )

            # 保存指数占比数据
            if not index_concentration_data.empty:
                self.index_analyzer.save_concentration_data(
                    index_concentration_data, output_paths['tables'], f"index_concentration_{output_paths['timestamp']}"
                )

            # 9. 增强可视化分析
            system_logger.info("Step 9: Enhanced visualization")
            visualization_files = {}

            # 原有可视化（移除多指标对比）
            if not self.config.visualization.enable_multi_indicator_comparison:
                # 仅生成单个指标图表（含历史百分位标注）
                for col in indicators_with_percentiles.select_dtypes(include=['number']).columns:
                    if col != 'date' and not col.endswith('_percentile'):
                        fig = self.visualization_manager.static_plotter.plot_single_indicator(
                            indicators_with_percentiles, col,
                            title=f'{col} 时间序列分析（含历史百分位）',
                            add_percentile_annotation=True
                        )
                        filename = f"{output_prefix}_{col}_{output_paths['timestamp']}"
                        file_path = self.visualization_manager.static_plotter.save_figure(
                            fig, filename, output_paths['charts']
                        )
                        visualization_files[f"single_{col}"] = file_path

            # 新增可视化
            # 成交金额集中度可视化
            if not raw_data.empty:
                stacked_area_path = self.concentration_plotter.plot_stacked_area_chart(
                    raw_data, output_paths['charts']
                )
                if stacked_area_path:
                    visualization_files["concentration_stacked"] = stacked_area_path

                top_sectors_path = self.concentration_plotter.plot_top_sectors_trend(
                    raw_data, self.config.visualization.top_sectors_count, output_paths['charts']
                )
                if top_sectors_path:
                    visualization_files["top_sectors_trend"] = top_sectors_path

            # 相关性热力图
            if not correlation_matrix.empty:
                correlation_heatmap_path = self.correlation_plotter.plot_correlation_heatmap(
                    correlation_matrix, output_paths['charts']
                )
                if correlation_heatmap_path:
                    visualization_files["correlation_heatmap"] = correlation_heatmap_path

            # HHI增强图表
            if not hhi_enhanced_data.empty:
                hhi_enhanced_path = self.hhi_analyzer.plot_hhi_with_thresholds(
                    hhi_enhanced_data, output_paths['charts']
                )
                if hhi_enhanced_path:
                    visualization_files["hhi_enhanced"] = hhi_enhanced_path

            # 指数占比分析图表
            if not index_concentration_data.empty:
                # 指数占比趋势图
                index_trend_path = self.index_analyzer.plot_index_concentration_trend(
                    index_concentration_data, output_paths['charts']
                )
                if index_trend_path:
                    visualization_files["index_concentration_trend"] = index_trend_path

                # 指数占比堆叠图
                index_stacked_path = self.index_analyzer.plot_index_concentration_stacked(
                    index_concentration_data, output_paths['charts']
                )
                if index_stacked_path:
                    visualization_files["index_concentration_stacked"] = index_stacked_path

            # 10. 生成报告
            system_logger.info("Step 10: Report generation")
            reports = {}

            # 行业表现Markdown报告
            if not performance_table.empty:
                performance_markdown = self.performance_reporter.generate_markdown_table(performance_table)
                performance_report_path = self.visualization_manager.report_generator.save_report(
                    performance_markdown, f"sector_performance_{output_paths['timestamp']}", output_paths['reports']
                )
                reports["sector_performance"] = performance_report_path

            # HHI集中度报告
            if not hhi_enhanced_data.empty:
                hhi_report = self.hhi_analyzer.create_concentration_report(hhi_enhanced_data)
                hhi_report_path = self.visualization_manager.report_generator.save_report(
                    hhi_report, f"hhi_concentration_{output_paths['timestamp']}", output_paths['reports']
                )
                reports["hhi_concentration"] = hhi_report_path

            # 综合分析报告
            comprehensive_results = {
                "data_records": len(raw_data),
                "indicator_periods": len(indicators_data),
                "date_range": date_range,
                "custom_start_date": custom_start_date,
                "indicators_summary": self._get_indicators_summary(indicators_with_percentiles),
                "percentile_summary": self.percentile_calculator.get_latest_percentiles(indicators_with_percentiles),
                "correlation_summary": correlation_pairs,
                "hhi_summary": self.hhi_analyzer.generate_concentration_summary(hhi_enhanced_data) if not hhi_enhanced_data.empty else {},
                "index_summary": self.index_analyzer.generate_concentration_summary(index_concentration_data) if not index_concentration_data.empty else {},
                "visualization_files": visualization_files,
                "reports": reports
            }

            comprehensive_report_path = self.comprehensive_reporter.generate_comprehensive_report(
                comprehensive_results, output_paths['reports'], output_paths['timestamp']
            )
            reports["comprehensive_analysis"] = comprehensive_report_path

            # 11. 创建最新结果链接
            self.output_manager.create_latest_symlinks(output_paths)

            # 12. 保存运行元数据
            metadata = {
                "start_date": start_date,
                "end_date": end_date,
                "custom_start_date": custom_start_date,
                "data_records": len(raw_data),
                "indicator_periods": len(indicators_data),
                "performance_sectors": len(performance_table) if not performance_table.empty else 0,
                "correlation_sectors": len(correlation_matrix) if not correlation_matrix.empty else 0,
                "high_correlation_pairs": len(correlation_pairs.get("high_correlation", [])),
                "low_correlation_pairs": len(correlation_pairs.get("low_correlation", [])),
                "index_concentration_periods": len(index_concentration_data) if not index_concentration_data.empty else 0
            }

            metadata_path = self.output_manager.save_run_metadata(output_paths, metadata)

            # 13. 汇总结果
            results = {
                "success": True,
                "output_paths": output_paths,
                "data_records": len(raw_data),
                "indicator_periods": len(indicators_data),
                "date_range": date_range,
                "storage_success": storage_success,
                "visualization_files": visualization_files,
                "reports": reports,
                "metadata_path": metadata_path,
                "indicators_summary": self._get_indicators_summary(indicators_with_percentiles),
                "percentile_summary": self.percentile_calculator.get_latest_percentiles(indicators_with_percentiles),
                "correlation_summary": correlation_pairs,
                "hhi_summary": self.hhi_analyzer.generate_concentration_summary(hhi_enhanced_data) if not hhi_enhanced_data.empty else {},
                "index_summary": self.index_analyzer.generate_concentration_summary(index_concentration_data) if not index_concentration_data.empty else {}
            }

            system_logger.info("Enhanced full analysis completed successfully")
            return results

        except Exception as e:
            system_logger.error(f"Enhanced analysis failed: {e}")
            return {"success": False, "error": str(e)}
    
    def run_indicators_only(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """仅运行指标计算"""
        system_logger.info(f"Running indicators calculation from {start_date} to {end_date}")
        
        try:
            # 数据预处理
            raw_data = self.preprocessor.load_and_preprocess(start_date, end_date)
            
            if raw_data.empty:
                return {"success": False, "error": "No data available"}
            
            # 指标计算
            indicators_data = self.indicator_engine.calculate_all_indicators(raw_data)
            
            if indicators_data.empty:
                return {"success": False, "error": "Indicator calculation failed"}
            
            # 保存结果
            date_range = f"{start_date}_{end_date}"
            storage_success = self.storage_manager.save_indicators(indicators_data, date_range)
            
            return {
                "success": True,
                "data_records": len(raw_data),
                "indicator_periods": len(indicators_data),
                "date_range": date_range,
                "storage_success": storage_success,
                "indicators_data": indicators_data
            }
            
        except Exception as e:
            system_logger.error(f"Indicators calculation failed: {e}")
            return {"success": False, "error": str(e)}
    
    def run_visualization_only(self, date_range: str, output_prefix: str = "visualization") -> Dict[str, Any]:
        """仅运行可视化分析"""
        system_logger.info(f"Running visualization for date range: {date_range}")
        
        try:
            # 加载已保存的指标数据
            indicators_data = self.storage_manager.load_indicators(date_range)
            
            if indicators_data.empty:
                return {"success": False, "error": f"No indicators data found for {date_range}"}
            
            # 生成可视化
            visualization_results = self.visualization_manager.create_comprehensive_analysis(
                indicators_data,
                output_prefix=output_prefix,
                include_interactive=True
            )
            
            return {
                "success": True,
                "indicator_periods": len(indicators_data),
                "visualization_files": visualization_results
            }
            
        except Exception as e:
            system_logger.error(f"Visualization failed: {e}")
            return {"success": False, "error": str(e)}
    
    def get_available_data(self) -> Dict[str, Any]:
        """获取可用数据列表"""
        return self.storage_manager.get_available_data()
    
    def _get_indicators_summary(self, indicators_data: pd.DataFrame) -> Dict[str, Any]:
        """获取指标摘要统计"""
        if indicators_data.empty:
            return {}
        
        numeric_columns = indicators_data.select_dtypes(include=['number']).columns
        summary = {}
        
        for col in numeric_columns:
            if col != 'date':
                series = indicators_data[col].dropna()
                if not series.empty:
                    summary[col] = {
                        "mean": float(series.mean()),
                        "std": float(series.std()),
                        "min": float(series.min()),
                        "max": float(series.max()),
                        "count": int(len(series))
                    }
                    
                    # 添加百分位数信息
                    summary[col]["percentiles"] = {
                        "25%": float(series.quantile(0.25)),
                        "50%": float(series.quantile(0.50)),
                        "75%": float(series.quantile(0.75))
                    }
                    
                    # 为HHI指标添加阈值信息
                    if col == 'volume_hhi' and hasattr(indicators_data[col], 'attrs') and 'thresholds' in indicators_data[col].attrs:
                        summary[col]["thresholds"] = indicators_data[col].attrs['thresholds']
        
        return summary


def create_sample_data():
    """创建示例数据用于测试"""
    import numpy as np
    from datetime import datetime, timedelta
    
    # 创建示例数据目录
    data_dir = Path("data/raw")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成示例数据
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    dates = pd.date_range(start_date, end_date, freq='D')
    sectors = ['金融', '科技', '医药', '消费', '能源', '工业', '材料', '公用事业']
    
    # 行业价格数据
    sector_prices = []
    base_prices = {sector: 100 + i * 10 for i, sector in enumerate(sectors)}
    
    for date in dates:
        for sector in sectors:
            # 模拟价格随机游走
            price_change = np.random.randn() * 0.02  # 2%日波动
            base_prices[sector] *= (1 + price_change)
            
            sector_prices.append({
                'date': date,
                'sector_code': sector,
                'close_price': base_prices[sector]
            })
    
    # 行业成交金额数据
    sector_volumes = []
    for date in dates:
        total_volume = 50000000000 + np.random.randn() * 10000000000  # 500亿基础成交额
        
        for sector in sectors:
            # 随机分配成交金额
            volume_share = np.random.rand() * 0.3 + 0.05  # 5%-35%的份额
            volume = total_volume * volume_share
            
            sector_volumes.append({
                'date': date,
                'sector_code': sector,
                'volume_amount': max(volume, 1000000)  # 最小100万
            })
    
    # 基准指数数据
    benchmark_prices = []
    benchmark_price = 3000
    
    for date in dates:
        benchmark_change = np.random.randn() * 0.015  # 1.5%日波动
        benchmark_price *= (1 + benchmark_change)
        
        benchmark_prices.append({
            'date': date,
            'close_price': benchmark_price
        })
    
    # 保存数据
    pd.DataFrame(sector_prices).to_csv(data_dir / "sector_prices.csv", index=False)
    pd.DataFrame(sector_volumes).to_csv(data_dir / "sector_volumes.csv", index=False)
    pd.DataFrame(benchmark_prices).to_csv(data_dir / "benchmark_prices.csv", index=False)
    
    system_logger.info("Sample data created successfully")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="股票行业轮动速度量化系统")
    parser.add_argument("--mode", choices=["full", "indicators", "visualization", "sample"], 
                       default="full", help="运行模式")
    parser.add_argument("--start-date", type=str, help="开始日期 (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, help="结束日期 (YYYY-MM-DD)")
    parser.add_argument("--custom-start-date", type=str, help="自定义时间段起始日期 (YYYY-MM-DD)")
    parser.add_argument("--date-range", type=str, help="日期范围标识符 (用于可视化模式)")
    parser.add_argument("--output-prefix", type=str, default="sector_rotation",
                       help="输出文件前缀")
    parser.add_argument("--create-sample", action="store_true", help="创建示例数据")
    parser.add_argument("--cleanup-old", type=int, help="清理旧的运行结果，保留最新N个")
    
    args = parser.parse_args()
    
    # 创建示例数据
    if args.create_sample or args.mode == "sample":
        create_sample_data()
        if args.mode == "sample":
            return
    
    # 初始化系统
    system = SectorRotationAnalysisSystem()

    # 清理旧运行结果
    if args.cleanup_old:
        deleted_count = system.output_manager.cleanup_old_runs(args.cleanup_old)
        print(f"Cleaned up {deleted_count} old runs, keeping latest {args.cleanup_old}")
        # 如果只是清理，则退出
        return

    if args.mode == "full":
        if not args.start_date or not args.end_date:
            print("Full analysis mode requires --start-date and --end-date")
            sys.exit(1)

        results = system.run_full_analysis(
            args.start_date,
            args.end_date,
            args.output_prefix,
            args.custom_start_date
        )

    elif args.mode == "indicators":
        if not args.start_date or not args.end_date:
            print("Indicators mode requires --start-date and --end-date")
            sys.exit(1)

        results = system.run_indicators_only(args.start_date, args.end_date)

    elif args.mode == "visualization":
        if not args.date_range:
            print("Visualization mode requires --date-range")
            sys.exit(1)

        results = system.run_visualization_only(args.date_range, args.output_prefix)

    # 输出结果
    if results["success"]:
        print("Enhanced analysis completed successfully!")
        print("\n=== 分析结果摘要 ===")
        print(f"数据记录数: {results.get('data_records', 0)}")
        print(f"指标计算期间数: {results.get('indicator_periods', 0)}")

        if 'output_paths' in results:
            print(f"\n输出目录: {results['output_paths']['base']}")
            print(f"图表文件: {len(results.get('visualization_files', {}))}")
            print(f"报告文件: {len(results.get('reports', {}))}")

        if 'percentile_summary' in results and results['percentile_summary']:
            print(f"\n最新百分位摘要:")
            for indicator, percentile in results['percentile_summary'].items():
                print(f"  {indicator}: {percentile:.1f}%")

        if 'correlation_summary' in results:
            corr_summary = results['correlation_summary']
            print(f"\n相关性分析:")
            print(f"  高相关性行业对: {len(corr_summary.get('high_correlation', []))}")
            print(f"  低相关性行业对: {len(corr_summary.get('low_correlation', []))}")

        if 'hhi_summary' in results and results['hhi_summary']:
            hhi_summary = results['hhi_summary']
            print(f"\nHHI集中度分析:")
            print(f"  最新HHI: {hhi_summary.get('latest_hhi', 'N/A'):.4f}")
            print(f"  集中度水平: {hhi_summary.get('latest_level', 'N/A')}")

    else:
        print(f"Analysis failed: {results.get('error', 'Unknown error')}")
        sys.exit(1)


if __name__ == "__main__":
    main()
