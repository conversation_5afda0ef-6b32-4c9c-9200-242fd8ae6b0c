"""
模块3：结果存储与管理
用于高效存储计算出的指标数据并管理其检索
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any
from pathlib import Path
from abc import ABC, abstractmethod
import json
from datetime import datetime

from config.settings import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("DataStorage")


class StorageBackend(ABC):
    """存储后端抽象基类"""

    @abstractmethod
    def save(self, data: pd.DataFrame, identifier: str, **kwargs) -> bool:
        """保存数据"""
        pass

    @abstractmethod
    def load(self, identifier: str, **kwargs) -> pd.DataFrame:
        """加载数据"""
        pass

    @abstractmethod
    def exists(self, identifier: str) -> bool:
        """检查数据是否存在"""
        pass

    @abstractmethod
    def delete(self, identifier: str) -> bool:
        """删除数据"""
        pass

    @abstractmethod
    def list_identifiers(self) -> List[str]:
        """列出所有标识符"""
        pass


class ParquetStorageBackend(StorageBackend):
    """Parquet文件存储后端"""

    def __init__(self, base_path: str = "data/output"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)

    def save(self, data: pd.DataFrame, identifier: str, **kwargs) -> bool:
        """保存数据到Parquet文件"""
        try:
            file_path = self.base_path / f"{identifier}.parquet"
            compression = kwargs.get('compression', 'snappy')
            data.to_parquet(file_path, compression=compression, index=False)
            logger.info(f"Data saved to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to save data to {identifier}: {e}")
            return False

    def load(self, identifier: str, **kwargs) -> pd.DataFrame:
        """从Parquet文件加载数据"""
        try:
            file_path = self.base_path / f"{identifier}.parquet"
            if not file_path.exists():
                logger.warning(f"File not found: {file_path}")
                return pd.DataFrame()

            data = pd.read_parquet(file_path)
            logger.info(f"Data loaded from {file_path}")
            return data
        except Exception as e:
            logger.error(f"Failed to load data from {identifier}: {e}")
            return pd.DataFrame()

    def exists(self, identifier: str) -> bool:
        """检查文件是否存在"""
        file_path = self.base_path / f"{identifier}.parquet"
        return file_path.exists()

    def delete(self, identifier: str) -> bool:
        """删除文件"""
        try:
            file_path = self.base_path / f"{identifier}.parquet"
            if file_path.exists():
                file_path.unlink()
                logger.info(f"File deleted: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete {identifier}: {e}")
            return False

    def list_identifiers(self) -> List[str]:
        """列出所有Parquet文件标识符"""
        parquet_files = list(self.base_path.glob("*.parquet"))
        return [f.stem for f in parquet_files]


class CSVStorageBackend(StorageBackend):
    """CSV文件存储后端"""

    def __init__(self, base_path: str = "data/output"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)

    def save(self, data: pd.DataFrame, identifier: str, **kwargs) -> bool:
        """保存数据到CSV文件"""
        try:
            file_path = self.base_path / f"{identifier}.csv"
            data.to_csv(file_path, index=False, encoding='utf-8')
            logger.info(f"Data saved to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to save data to {identifier}: {e}")
            return False

    def load(self, identifier: str, **kwargs) -> pd.DataFrame:
        """从CSV文件加载数据"""
        try:
            file_path = self.base_path / f"{identifier}.csv"
            if not file_path.exists():
                logger.warning(f"File not found: {file_path}")
                return pd.DataFrame()

            data = pd.read_csv(file_path)
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
            logger.info(f"Data loaded from {file_path}")
            return data
        except Exception as e:
            logger.error(f"Failed to load data from {identifier}: {e}")
            return pd.DataFrame()

    def exists(self, identifier: str) -> bool:
        """检查文件是否存在"""
        file_path = self.base_path / f"{identifier}.csv"
        return file_path.exists()

    def delete(self, identifier: str) -> bool:
        """删除文件"""
        try:
            file_path = self.base_path / f"{identifier}.csv"
            if file_path.exists():
                file_path.unlink()
                logger.info(f"File deleted: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete {identifier}: {e}")
            return False

    def list_identifiers(self) -> List[str]:
        """列出所有CSV文件标识符"""
        csv_files = list(self.base_path.glob("*.csv"))
        return [f.stem for f in csv_files]


class MetadataManager:
    """元数据管理器"""

    def __init__(self, metadata_path: str = "data/output/metadata.json"):
        self.metadata_path = Path(metadata_path)
        self.metadata_path.parent.mkdir(parents=True, exist_ok=True)
        self._metadata = self._load_metadata()

    def _load_metadata(self) -> Dict[str, Any]:
        """加载元数据"""
        if self.metadata_path.exists():
            try:
                with open(self.metadata_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Failed to load metadata: {e}")
                return {}
        return {}

    def _save_metadata(self) -> None:
        """保存元数据"""
        try:
            with open(self.metadata_path, 'w', encoding='utf-8') as f:
                json.dump(self._metadata, f, indent=2, ensure_ascii=False, default=str)
        except Exception as e:
            logger.error(f"Failed to save metadata: {e}")

    def add_metadata(self, identifier: str, metadata: Dict[str, Any]) -> None:
        """添加元数据"""
        self._metadata[identifier] = {
            **metadata,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        self._save_metadata()

    def get_metadata(self, identifier: str) -> Dict[str, Any]:
        """获取元数据"""
        return self._metadata.get(identifier, {})

    def update_metadata(self, identifier: str, metadata: Dict[str, Any]) -> None:
        """更新元数据"""
        if identifier in self._metadata:
            self._metadata[identifier].update(metadata)
            self._metadata[identifier]['updated_at'] = datetime.now().isoformat()
            self._save_metadata()

    def remove_metadata(self, identifier: str) -> None:
        """移除元数据"""
        if identifier in self._metadata:
            del self._metadata[identifier]
            self._save_metadata()


class DataStorageManager:
    """数据存储管理器"""

    def __init__(self, storage_backend: Optional[StorageBackend] = None):
        self.config = config_manager.get_storage_config()
        self.storage_backend = storage_backend or self._create_default_backend()
        self.metadata_manager = MetadataManager()

    def _create_default_backend(self) -> StorageBackend:
        """创建默认存储后端"""
        storage_format = config_manager.get_data_config().storage_format
        base_path = self.config.base_path

        if storage_format.lower() == "parquet":
            return ParquetStorageBackend(base_path)
        elif storage_format.lower() == "csv":
            return CSVStorageBackend(base_path)
        else:
            logger.warning(f"Unknown storage format: {storage_format}, using Parquet")
            return ParquetStorageBackend(base_path)

    def save_indicators(self, data: pd.DataFrame, date_range: str = None) -> bool:
        """保存指标数据"""
        identifier = f"indicators_{date_range or 'latest'}"

        # 保存数据
        success = self.storage_backend.save(
            data,
            identifier,
            compression=self.config.compression
        )

        if success:
            # 保存元数据
            metadata = {
                'data_type': 'indicators',
                'shape': data.shape,
                'columns': list(data.columns),
                'date_range': date_range,
                'start_date': data['date'].min().isoformat() if 'date' in data.columns else None,
                'end_date': data['date'].max().isoformat() if 'date' in data.columns else None
            }
            self.metadata_manager.add_metadata(identifier, metadata)

            # 创建备份
            if self.config.backup_enabled:
                backup_identifier = f"{identifier}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                self.storage_backend.save(data, backup_identifier)

        return success

    def load_indicators(self, date_range: str = None,
                       start_date: str = None,
                       end_date: str = None,
                       columns: List[str] = None) -> pd.DataFrame:
        """加载指标数据"""
        identifier = f"indicators_{date_range or 'latest'}"

        data = self.storage_backend.load(identifier)

        if data.empty:
            logger.warning(f"No data found for identifier: {identifier}")
            return data

        # 按日期范围过滤
        if start_date or end_date:
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
                if start_date:
                    data = data[data['date'] >= pd.to_datetime(start_date)]
                if end_date:
                    data = data[data['date'] <= pd.to_datetime(end_date)]

        # 按列过滤
        if columns:
            available_columns = [col for col in columns if col in data.columns]
            if 'date' not in available_columns and 'date' in data.columns:
                available_columns.insert(0, 'date')
            data = data[available_columns]

        logger.info(f"Loaded indicators data. Shape: {data.shape}")
        return data

    def append_indicators(self, new_data: pd.DataFrame, date_range: str = None) -> bool:
        """追加指标数据"""
        identifier = f"indicators_{date_range or 'latest'}"

        # 加载现有数据
        existing_data = self.storage_backend.load(identifier)

        if not existing_data.empty:
            # 合并数据
            combined_data = pd.concat([existing_data, new_data], ignore_index=True)

            # 去重并排序
            if 'date' in combined_data.columns:
                combined_data = combined_data.drop_duplicates(subset=['date'])
                combined_data = combined_data.sort_values('date').reset_index(drop=True)
        else:
            combined_data = new_data

        return self.save_indicators(combined_data, date_range)

    def get_available_data(self) -> Dict[str, Dict[str, Any]]:
        """获取可用数据列表"""
        identifiers = self.storage_backend.list_identifiers()
        available_data = {}

        for identifier in identifiers:
            metadata = self.metadata_manager.get_metadata(identifier)
            available_data[identifier] = metadata

        return available_data

    def delete_data(self, identifier: str) -> bool:
        """删除数据"""
        success = self.storage_backend.delete(identifier)
        if success:
            self.metadata_manager.remove_metadata(identifier)
        return success
