#!/usr/bin/env python3
"""
数据文件合并脚本
将benchmark_prices、sector_prices、sector_volumes三个独立文件合并为统一数据文件
"""

import pandas as pd
import numpy as np
from pathlib import Path

def merge_data_files():
    """合并数据文件"""
    
    # 读取原始数据文件
    raw_dir = Path("data/raw")
    
    # 读取行业价格数据
    sector_prices = pd.read_csv(raw_dir / "sector_prices.csv")
    print(f"Loaded sector prices: {sector_prices.shape}")
    
    # 读取行业成交量数据
    sector_volumes = pd.read_csv(raw_dir / "sector_volumes.csv")
    print(f"Loaded sector volumes: {sector_volumes.shape}")
    
    # 读取基准指数价格数据
    benchmark_prices = pd.read_csv(raw_dir / "benchmark_prices.csv")
    print(f"Loaded benchmark prices: {benchmark_prices.shape}")
    
    # 合并行业价格和成交量数据
    sector_data = pd.merge(
        sector_prices,
        sector_volumes,
        on=['date', 'sector_code'],
        how='inner'
    )
    
    # 重命名列以符合统一格式
    sector_data = sector_data.rename(columns={
        'close_price': 'sector_close_price',
        'volume_amount': 'sector_volume_amount'
    })
    
    # 添加基准指数价格
    benchmark_prices = benchmark_prices.rename(columns={'close_price': 'benchmark_price'})
    unified_data = pd.merge(
        sector_data,
        benchmark_prices,
        on='date',
        how='inner'
    )
    
    # 计算daily_return
    unified_data = unified_data.sort_values(['sector_code', 'date'])
    unified_data['daily_return'] = unified_data.groupby('sector_code')['sector_close_price'].pct_change()
    
    # 重新排列列的顺序
    column_order = [
        'date', 
        'sector_code', 
        'sector_close_price', 
        'sector_volume_amount', 
        'benchmark_price', 
        'daily_return'
    ]
    unified_data = unified_data[column_order]
    
    # 保存统一数据文件
    output_file = raw_dir / "unified_sector_data.csv"
    unified_data.to_csv(output_file, index=False)
    
    print(f"Unified data saved to: {output_file}")
    print(f"Unified data shape: {unified_data.shape}")
    print(f"Columns: {list(unified_data.columns)}")
    print(f"Date range: {unified_data['date'].min()} to {unified_data['date'].max()}")
    print(f"Sectors: {sorted(unified_data['sector_code'].unique())}")
    
    # 显示前几行数据
    print("\nFirst 10 rows:")
    print(unified_data.head(10))
    
    return unified_data

if __name__ == "__main__":
    merge_data_files()
