"""
模块1：原始数据输入与预处理
负责接收原始市场数据并将其转换为干净、标准化格式
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Tuple
from pathlib import Path
from abc import ABC, abstractmethod

from config.settings import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("DataPreprocessor")


class DataSource(ABC):
    """数据源抽象基类"""

    @abstractmethod
    def load_sector_prices(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载行业指数价格数据"""
        pass

    @abstractmethod
    def load_sector_volumes(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载行业成交金额数据"""
        pass

    @abstractmethod
    def load_benchmark_prices(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载基准指数价格数据"""
        pass


class CSVDataSource(DataSource):
    """CSV文件数据源"""

    def __init__(self, data_dir: str = "data/raw"):
        self.data_dir = Path(data_dir)

    def load_sector_prices(self, start_date: str, end_date: str) -> pd.DataFrame:
        """从CSV文件加载行业指数价格数据"""
        file_path = self.data_dir / "sector_prices.csv"
        if not file_path.exists():
            logger.warning(f"Sector prices file not found: {file_path}")
            return pd.DataFrame()

        df = pd.read_csv(file_path)
        df['date'] = pd.to_datetime(df['date'])
        return df[(df['date'] >= start_date) & (df['date'] <= end_date)]

    def load_sector_volumes(self, start_date: str, end_date: str) -> pd.DataFrame:
        """从CSV文件加载行业成交金额数据"""
        file_path = self.data_dir / "sector_volumes.csv"
        if not file_path.exists():
            logger.warning(f"Sector volumes file not found: {file_path}")
            return pd.DataFrame()

        df = pd.read_csv(file_path)
        df['date'] = pd.to_datetime(df['date'])
        return df[(df['date'] >= start_date) & (df['date'] <= end_date)]

    def load_benchmark_prices(self, start_date: str, end_date: str) -> pd.DataFrame:
        """从CSV文件加载基准指数价格数据"""
        file_path = self.data_dir / "benchmark_prices.csv"
        if not file_path.exists():
            logger.warning(f"Benchmark prices file not found: {file_path}")
            return pd.DataFrame()

        df = pd.read_csv(file_path)
        df['date'] = pd.to_datetime(df['date'])
        return df[(df['date'] >= start_date) & (df['date'] <= end_date)]


class DataValidator:
    """数据验证器"""

    @staticmethod
    def validate_data_completeness(df: pd.DataFrame, required_columns: List[str]) -> bool:
        """验证数据完整性"""
        missing_columns = set(required_columns) - set(df.columns)
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            return False
        return True

    @staticmethod
    def validate_date_range(df: pd.DataFrame, start_date: str, end_date: str) -> bool:
        """验证日期范围"""
        if df.empty:
            logger.warning("DataFrame is empty")
            return False

        df_start = df['date'].min()
        df_end = df['date'].max()

        if df_start > pd.to_datetime(start_date):
            logger.warning(f"Data starts later than requested: {df_start} > {start_date}")

        if df_end < pd.to_datetime(end_date):
            logger.warning(f"Data ends earlier than requested: {df_end} < {end_date}")

        return True

    @staticmethod
    def check_missing_values(df: pd.DataFrame) -> Dict[str, int]:
        """检查缺失值"""
        missing_counts = df.isnull().sum()
        missing_dict = missing_counts[missing_counts > 0].to_dict()

        if missing_dict:
            logger.warning(f"Missing values found: {missing_dict}")

        return missing_dict


class DataCleaner:
    """数据清洗器"""

    @staticmethod
    def handle_missing_values(df: pd.DataFrame, method: str = "forward_fill") -> pd.DataFrame:
        """处理缺失值"""
        df_cleaned = df.copy()

        if method == "forward_fill":
            df_cleaned = df_cleaned.ffill()
        elif method == "backward_fill":
            df_cleaned = df_cleaned.bfill()
        elif method == "interpolate":
            numeric_columns = df_cleaned.select_dtypes(include=[np.number]).columns
            df_cleaned[numeric_columns] = df_cleaned[numeric_columns].interpolate()
        elif method == "drop":
            df_cleaned = df_cleaned.dropna()

        logger.info(f"Missing values handled using method: {method}")
        return df_cleaned

    @staticmethod
    def remove_outliers(df: pd.DataFrame, columns: List[str], method: str = "iqr",
                       threshold: float = 1.5) -> pd.DataFrame:
        """移除异常值"""
        df_cleaned = df.copy()

        for col in columns:
            if col not in df_cleaned.columns:
                continue

            if method == "iqr":
                Q1 = df_cleaned[col].quantile(0.25)
                Q3 = df_cleaned[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - threshold * IQR
                upper_bound = Q3 + threshold * IQR

                outliers_mask = (df_cleaned[col] < lower_bound) | (df_cleaned[col] > upper_bound)
                outliers_count = outliers_mask.sum()

                if outliers_count > 0:
                    logger.info(f"Removed {outliers_count} outliers from column {col}")
                    df_cleaned = df_cleaned[~outliers_mask]

        return df_cleaned


class DataPreprocessor:
    """数据预处理器主类"""

    def __init__(self, data_source: Optional[DataSource] = None):
        self.config = config_manager.get_data_config()
        self.data_source = data_source or CSVDataSource()
        self.validator = DataValidator()
        self.cleaner = DataCleaner()

    def load_and_preprocess(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载并预处理所有数据"""
        logger.info(f"Loading data from {start_date} to {end_date}")

        # 检查是否使用统一数据文件
        if self.config.use_unified_file:
            logger.info("Using unified data file")
            merged_data = self._load_unified_data(start_date, end_date)
        else:
            logger.info("Using separate data files")
            # 加载原始数据
            sector_prices = self.data_source.load_sector_prices(start_date, end_date)
            sector_volumes = self.data_source.load_sector_volumes(start_date, end_date)
            benchmark_prices = self.data_source.load_benchmark_prices(start_date, end_date)

            # 验证数据
            self._validate_raw_data(sector_prices, sector_volumes, benchmark_prices)

            # 合并数据
            merged_data = self._merge_data(sector_prices, sector_volumes, benchmark_prices)

        # 清洗数据
        cleaned_data = self._clean_data(merged_data)

        # 标准化数据
        standardized_data = self._standardize_data(cleaned_data)

        logger.info(f"Data preprocessing completed. Final shape: {standardized_data.shape}")
        return standardized_data

    def _load_unified_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载统一数据文件"""
        file_path = Path(self.config.unified_data_file)

        if not file_path.exists():
            logger.error(f"Unified data file not found: {file_path}")
            return pd.DataFrame()

        # 读取统一数据文件
        df = pd.read_csv(file_path)
        logger.info(f"Loaded unified data file: {file_path}, shape: {df.shape}")

        # 转换日期格式
        df['date'] = pd.to_datetime(df['date'])

        # 筛选日期范围
        df = df[(df['date'] >= start_date) & (df['date'] <= end_date)]

        # 验证统一数据文件的格式
        required_columns = ['date', 'sector_code', 'sector_close_price',
                          'sector_volume_amount', 'benchmark_price', 'daily_return']
        missing_columns = set(required_columns) - set(df.columns)

        if missing_columns:
            logger.error(f"Missing required columns in unified data file: {missing_columns}")
            return pd.DataFrame()

        logger.info(f"Unified data loaded successfully. Shape after date filtering: {df.shape}")
        return df

    def _validate_raw_data(self, sector_prices: pd.DataFrame,
                          sector_volumes: pd.DataFrame,
                          benchmark_prices: pd.DataFrame) -> None:
        """验证原始数据"""
        # 验证行业价格数据
        required_price_columns = ['date', 'sector_code', 'close_price']
        self.validator.validate_data_completeness(sector_prices, required_price_columns)

        # 验证行业成交金额数据
        required_volume_columns = ['date', 'sector_code', 'volume_amount']
        self.validator.validate_data_completeness(sector_volumes, required_volume_columns)

        # 验证基准指数数据
        required_benchmark_columns = ['date', 'close_price']
        self.validator.validate_data_completeness(benchmark_prices, required_benchmark_columns)

    def _merge_data(self, sector_prices: pd.DataFrame,
                   sector_volumes: pd.DataFrame,
                   benchmark_prices: pd.DataFrame) -> pd.DataFrame:
        """合并数据并添加daily_return字段"""
        # 合并行业价格和成交金额
        sector_data = pd.merge(
            sector_prices,
            sector_volumes,
            on=['date', 'sector_code'],
            how='inner'
        )

        # 添加基准指数数据
        benchmark_prices = benchmark_prices.rename(columns={'close_price': 'benchmark_price'})
        merged_data = pd.merge(
            sector_data,
            benchmark_prices,
            on='date',
            how='inner'
        )

        # 检查是否已包含daily_return字段（由外部数据输入）
        merged_data = merged_data.sort_values(['sector_code', 'date'])

        # 如果外部数据没有提供daily_return，则计算它
        if 'daily_return' not in merged_data.columns:
            logger.info("daily_return field not found in input data, calculating it...")
            merged_data['daily_return'] = merged_data.groupby('sector_code')['close_price'].pct_change()
        else:
            logger.info("daily_return field found in input data, using external values")

        logger.info(f"Data merged successfully with daily_return field. Shape: {merged_data.shape}")
        return merged_data

    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗数据"""
        # 检查缺失值
        missing_values = self.validator.check_missing_values(df)

        # 处理缺失值
        if missing_values:
            df = self.cleaner.handle_missing_values(df, method="forward_fill")

        # 移除异常值
        numeric_columns = ['close_price', 'volume_amount', 'benchmark_price']
        df = self.cleaner.remove_outliers(df, numeric_columns)

        return df

    def _standardize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化数据格式"""
        # 确保日期格式正确
        df['date'] = pd.to_datetime(df['date'])

        # 排序
        df = df.sort_values(['date', 'sector_code']).reset_index(drop=True)

        # 重命名列以符合标准格式（仅当使用分离文件时）
        if not self.config.use_unified_file:
            column_mapping = {
                'close_price': 'sector_close_price',
                'volume_amount': 'sector_volume_amount'
            }
            df = df.rename(columns=column_mapping)

        return df
