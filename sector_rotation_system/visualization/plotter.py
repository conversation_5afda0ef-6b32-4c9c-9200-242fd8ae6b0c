"""
模块4：可视化与报告
创建一个模块，专门用于生成请求的时间序列可视化图表和潜在的摘要报告
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Optional, Union, Tuple, Any
from pathlib import Path
from datetime import datetime

from config.settings import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("Visualization")


class BasePlotter:
    """基础绘图类"""

    def __init__(self):
        self.config = config_manager.get_visualization_config()
        self._setup_style()

    def _setup_style(self):
        """设置绘图样式"""
        plt.style.use(self.config.style)
        sns.set_palette(self.config.color_palette)

        # 设置中文字体支持
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

    def save_figure(self, fig, filename: str, output_dir: str = "output/charts") -> str:
        """保存图表"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        file_path = output_path / f"{filename}.png"
        fig.savefig(file_path, dpi=self.config.dpi, bbox_inches='tight')
        logger.info(f"Chart saved to {file_path}")
        return str(file_path)


class TimeSeriesPlotter(BasePlotter):
    """时间序列绘图器"""

    def plot_single_indicator(self, data: pd.DataFrame,
                            indicator_column: str,
                            title: str = None,
                            date_column: str = 'date',
                            add_percentile_annotation: bool = True) -> plt.Figure:
        """绘制单个指标的时间序列图（增强版，含最新值和历史百分位标注）"""
        if data.empty or indicator_column not in data.columns:
            logger.error(f"Invalid data or column: {indicator_column}")
            return plt.figure()

        fig, ax = plt.subplots(figsize=self.config.figure_size)

        # 确保日期列是datetime类型
        if date_column in data.columns:
            data[date_column] = pd.to_datetime(data[date_column])
            x_data = data[date_column]
        else:
            x_data = data.index

        y_data = data[indicator_column]

        # 绘制线图
        ax.plot(x_data, y_data, linewidth=2, marker='o', markersize=4, color='blue')

        # 添加最新值和历史百分位标注
        if add_percentile_annotation and not data.empty:
            latest_value = y_data.iloc[-1]
            latest_x = x_data.iloc[-1] if hasattr(x_data, 'iloc') else x_data[-1]

            # 检查是否有对应的百分位列
            percentile_column = f"{indicator_column}_percentile"
            if percentile_column in data.columns:
                latest_percentile = data[percentile_column].iloc[-1]
                if pd.notna(latest_percentile):
                    annotation_text = f"最新值: {latest_value:.4f}\n历史百分位: {latest_percentile:.1f}%"
                else:
                    annotation_text = f"最新值: {latest_value:.4f}\n历史百分位: N/A"
            else:
                annotation_text = f"最新值: {latest_value:.4f}"

            # 计算标注位置，避免与图表线条重叠
            y_range = y_data.max() - y_data.min()
            y_offset = y_range * 0.1  # 向上偏移10%的数据范围

            # 添加标注
            ax.annotate(annotation_text,
                       xy=(latest_x, latest_value),
                       xytext=(20, 20), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8, edgecolor='black'),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.1', color='black'),
                       fontsize=10,
                       ha='left',
                       va='bottom')

        # 设置标题和标签
        ax.set_title(title or f'{indicator_column} 时间序列', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel(indicator_column, fontsize=12)

        # 格式化x轴
        ax.tick_params(axis='x', rotation=45)

        # 添加网格
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        return fig

    def plot_multiple_indicators(self, data: pd.DataFrame,
                               indicator_columns: List[str],
                               title: str = None,
                               date_column: str = 'date',
                               normalize: bool = False) -> plt.Figure:
        """绘制多个指标的时间序列图"""
        if data.empty:
            logger.error("Data is empty")
            return plt.figure()

        # 验证列存在
        valid_columns = [col for col in indicator_columns if col in data.columns]
        if not valid_columns:
            logger.error(f"No valid columns found: {indicator_columns}")
            return plt.figure()

        fig, ax = plt.subplots(figsize=self.config.figure_size)

        # 确保日期列是datetime类型
        if date_column in data.columns:
            data[date_column] = pd.to_datetime(data[date_column])
            x_data = data[date_column]
        else:
            x_data = data.index

        # 绘制每个指标
        for i, column in enumerate(valid_columns):
            y_data = data[column]

            # 标准化处理
            if normalize:
                y_data = (y_data - y_data.mean()) / y_data.std()

            ax.plot(x_data, y_data, linewidth=2, marker='o', markersize=3,
                   label=column, alpha=0.8)

        # 设置标题和标签
        ax.set_title(title or '多指标时间序列对比', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('指标值' + (' (标准化)' if normalize else ''), fontsize=12)

        # 添加图例
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        # 格式化x轴
        ax.tick_params(axis='x', rotation=45)

        # 添加网格
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        return fig




class InteractivePlotter:
    """交互式绘图器（使用Plotly）"""

    def __init__(self):
        self.config = config_manager.get_visualization_config()

    def plot_interactive_timeseries(self, data: pd.DataFrame,
                                  indicator_columns: List[str],
                                  title: str = None,
                                  date_column: str = 'date') -> go.Figure:
        """创建交互式时间序列图"""
        if data.empty:
            logger.error("Data is empty")
            return go.Figure()

        # 验证列存在
        valid_columns = [col for col in indicator_columns if col in data.columns]
        if not valid_columns:
            logger.error(f"No valid columns found: {indicator_columns}")
            return go.Figure()

        fig = go.Figure()

        # 确保日期列是datetime类型
        if date_column in data.columns:
            data[date_column] = pd.to_datetime(data[date_column])
            x_data = data[date_column]
        else:
            x_data = data.index

        # 添加每个指标的轨迹
        for column in valid_columns:
            fig.add_trace(go.Scatter(
                x=x_data,
                y=data[column],
                mode='lines+markers',
                name=column,
                line=dict(width=2),
                marker=dict(size=4)
            ))

        # 更新布局
        fig.update_layout(
            title=title or '行业轮动指标时间序列',
            xaxis_title='日期',
            yaxis_title='指标值',
            hovermode='x unified',
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )

        return fig

    def plot_dashboard(self, data: pd.DataFrame,
                      indicator_columns: List[str],
                      title: str = None) -> go.Figure:
        """创建仪表板样式的多子图"""
        if data.empty:
            logger.error("Data is empty")
            return go.Figure()

        # 验证列存在
        valid_columns = [col for col in indicator_columns if col in data.columns]
        if not valid_columns:
            logger.error(f"No valid columns found: {indicator_columns}")
            return go.Figure()

        # 计算子图布局
        n_indicators = len(valid_columns)
        cols = min(2, n_indicators)
        rows = (n_indicators + cols - 1) // cols

        # 创建子图
        fig = make_subplots(
            rows=rows, cols=cols,
            subplot_titles=valid_columns,
            vertical_spacing=0.08,
            horizontal_spacing=0.1
        )

        # 确保日期列是datetime类型
        date_column = 'date'
        if date_column in data.columns:
            data[date_column] = pd.to_datetime(data[date_column])
            x_data = data[date_column]
        else:
            x_data = data.index

        # 添加每个指标到对应子图
        for i, column in enumerate(valid_columns):
            row = i // cols + 1
            col = i % cols + 1

            fig.add_trace(
                go.Scatter(
                    x=x_data,
                    y=data[column],
                    mode='lines+markers',
                    name=column,
                    line=dict(width=2),
                    marker=dict(size=3),
                    showlegend=False
                ),
                row=row, col=col
            )

        # 更新布局
        fig.update_layout(
            title=title or '行业轮动指标仪表板',
            height=300 * rows,
            showlegend=False
        )

        return fig

    def save_html(self, fig: go.Figure, filename: str, output_dir: str = "output/charts") -> str:
        """保存交互式图表为HTML"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        file_path = output_path / f"{filename}.html"
        fig.write_html(str(file_path))
        logger.info(f"Interactive chart saved to {file_path}")
        return str(file_path)


class ReportGenerator:
    """报告生成器"""

    def __init__(self):
        self.config = config_manager.get_visualization_config()

    def generate_summary_report(self, data: pd.DataFrame,
                              indicator_columns: List[str],
                              date_column: str = 'date') -> str:
        """生成摘要报告"""
        if data.empty:
            return "数据为空，无法生成报告。"

        # 验证列存在
        valid_columns = [col for col in indicator_columns if col in data.columns]
        if not valid_columns:
            return f"未找到有效的指标列: {indicator_columns}"

        report_lines = []
        report_lines.append("# 行业轮动速度量化分析报告")
        report_lines.append("")

        # 基本信息
        if date_column in data.columns:
            data[date_column] = pd.to_datetime(data[date_column])
            start_date = data[date_column].min().strftime('%Y-%m-%d')
            end_date = data[date_column].max().strftime('%Y-%m-%d')
            report_lines.append(f"## 分析期间: {start_date} 至 {end_date}")
        else:
            report_lines.append(f"## 数据记录数: {len(data)}")

        report_lines.append("")

        # 指标统计摘要
        report_lines.append("## 指标统计摘要")
        report_lines.append("")

        stats_df = data[valid_columns].describe()

        for column in valid_columns:
            if column in stats_df.columns:
                stats = stats_df[column]
                report_lines.append(f"### {column}")
                report_lines.append(f"- 均值: {stats['mean']:.4f}")
                report_lines.append(f"- 标准差: {stats['std']:.4f}")
                report_lines.append(f"- 最小值: {stats['min']:.4f}")
                report_lines.append(f"- 最大值: {stats['max']:.4f}")
                report_lines.append(f"- 中位数: {stats['50%']:.4f}")
                report_lines.append("")

        # 相关性分析
        if len(valid_columns) > 1:
            report_lines.append("## 指标相关性分析")
            report_lines.append("")

            corr_matrix = data[valid_columns].corr()

            # 找出高相关性的指标对
            high_corr_pairs = []
            for i in range(len(valid_columns)):
                for j in range(i+1, len(valid_columns)):
                    corr_value = corr_matrix.iloc[i, j]
                    if abs(corr_value) > 0.7:  # 高相关性阈值
                        high_corr_pairs.append((valid_columns[i], valid_columns[j], corr_value))

            if high_corr_pairs:
                report_lines.append("### 高相关性指标对 (|相关系数| > 0.7):")
                for col1, col2, corr in high_corr_pairs:
                    report_lines.append(f"- {col1} 与 {col2}: {corr:.4f}")
            else:
                report_lines.append("### 未发现高相关性指标对 (|相关系数| > 0.7)")

            report_lines.append("")

        # 趋势分析
        report_lines.append("## 趋势分析")
        report_lines.append("")

        for column in valid_columns:
            if column in data.columns:
                values = data[column].dropna()
                if len(values) > 1:
                    # 计算趋势（简单线性回归斜率）
                    x = np.arange(len(values))
                    slope = np.polyfit(x, values, 1)[0]

                    trend_desc = "上升" if slope > 0 else "下降" if slope < 0 else "平稳"
                    report_lines.append(f"### {column}: {trend_desc}趋势 (斜率: {slope:.6f})")

        report_lines.append("")

        # 生成时间
        report_lines.append(f"---")
        report_lines.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        return "\n".join(report_lines)

    def save_report(self, report_content: str, filename: str, output_dir: str = "output/reports") -> str:
        """保存报告到文件"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        file_path = output_path / f"{filename}.md"

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"Report saved to {file_path}")
        return str(file_path)


class VisualizationManager:
    """可视化管理器主类"""

    def __init__(self):
        self.static_plotter = TimeSeriesPlotter()
        self.interactive_plotter = InteractivePlotter()
        self.report_generator = ReportGenerator()

    def create_comprehensive_analysis(self, data: pd.DataFrame,
                                    indicator_columns: List[str] = None,
                                    output_prefix: str = "sector_rotation_analysis",
                                    include_interactive: bool = True) -> Dict[str, str]:
        """创建综合分析（包含所有图表和报告）"""
        if data.empty:
            logger.error("Data is empty")
            return {}

        # 如果未指定指标列，使用所有数值列（除了日期）
        if indicator_columns is None:
            numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()
            indicator_columns = [col for col in numeric_columns if col != 'date']

        # 验证列存在
        valid_columns = [col for col in indicator_columns if col in data.columns]
        if not valid_columns:
            logger.error(f"No valid indicator columns found: {indicator_columns}")
            return {}

        results = {}
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        try:
            # 1. 生成单个指标图表
            for column in valid_columns:
                fig = self.static_plotter.plot_single_indicator(
                    data, column, title=f'{column} 时间序列分析'
                )
                filename = f"{output_prefix}_{column}_{timestamp}"
                file_path = self.static_plotter.save_figure(fig, filename)
                results[f"single_{column}"] = file_path
                plt.close(fig)

            # 2. 生成多指标对比图
            if len(valid_columns) > 1:
                fig = self.static_plotter.plot_multiple_indicators(
                    data, valid_columns, title='行业轮动指标对比分析'
                )
                filename = f"{output_prefix}_comparison_{timestamp}"
                file_path = self.static_plotter.save_figure(fig, filename)
                results["comparison"] = file_path
                plt.close(fig)

                # 3. 生成标准化对比图
                fig = self.static_plotter.plot_multiple_indicators(
                    data, valid_columns, title='行业轮动指标对比分析（标准化）', normalize=True
                )
                filename = f"{output_prefix}_comparison_normalized_{timestamp}"
                file_path = self.static_plotter.save_figure(fig, filename)
                results["comparison_normalized"] = file_path
                plt.close(fig)

                # 相关性热力图功能已移至enhanced_plotter模块

            # 5. 生成交互式图表
            if include_interactive:
                interactive_fig = self.interactive_plotter.plot_interactive_timeseries(
                    data, valid_columns, title='行业轮动指标交互式分析'
                )
                filename = f"{output_prefix}_interactive_{timestamp}"
                file_path = self.interactive_plotter.save_html(interactive_fig, filename)
                results["interactive"] = file_path

                # 6. 生成仪表板
                dashboard_fig = self.interactive_plotter.plot_dashboard(
                    data, valid_columns, title='行业轮动指标仪表板'
                )
                filename = f"{output_prefix}_dashboard_{timestamp}"
                file_path = self.interactive_plotter.save_html(dashboard_fig, filename)
                results["dashboard"] = file_path

            # 7. 生成分析报告
            report_content = self.report_generator.generate_summary_report(data, valid_columns)
            filename = f"{output_prefix}_report_{timestamp}"
            file_path = self.report_generator.save_report(report_content, filename)
            results["report"] = file_path

            logger.info(f"Comprehensive analysis completed. Generated {len(results)} files.")

        except Exception as e:
            logger.error(f"Error creating comprehensive analysis: {e}")

        return results

    def quick_plot(self, data: pd.DataFrame,
                  indicator_columns: List[str] = None,
                  plot_type: str = "timeseries") -> Optional[str]:
        """快速绘图"""
        if data.empty:
            logger.error("Data is empty")
            return None

        if indicator_columns is None:
            numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()
            indicator_columns = [col for col in numeric_columns if col != 'date']

        valid_columns = [col for col in indicator_columns if col in data.columns]
        if not valid_columns:
            logger.error(f"No valid indicator columns found: {indicator_columns}")
            return None

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        try:
            if plot_type == "timeseries":
                if len(valid_columns) == 1:
                    fig = self.static_plotter.plot_single_indicator(data, valid_columns[0])
                else:
                    fig = self.static_plotter.plot_multiple_indicators(data, valid_columns)

                filename = f"quick_plot_{timestamp}"
                file_path = self.static_plotter.save_figure(fig, filename)
                plt.close(fig)
                return file_path

            elif plot_type == "correlation" and len(valid_columns) > 1:
                # 相关性热力图功能已移至enhanced_plotter模块
                logger.warning("Correlation heatmap moved to enhanced_plotter module")
                return None

            elif plot_type == "interactive":
                fig = self.interactive_plotter.plot_interactive_timeseries(data, valid_columns)
                filename = f"quick_interactive_{timestamp}"
                file_path = self.interactive_plotter.save_html(fig, filename)
                return file_path

        except Exception as e:
            logger.error(f"Error in quick plot: {e}")

        return None
