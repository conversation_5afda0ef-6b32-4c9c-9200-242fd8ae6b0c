"""
增强可视化模块
提供新增的可视化功能，包括集中度分析、相关性分析等
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from datetime import datetime

from config.settings import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("EnhancedPlotter")


class ConcentrationPlotter:
    """成交金额集中度可视化器"""
    
    def __init__(self):
        self.config = config_manager.get_visualization_config()
        self._setup_style()
        
    def _setup_style(self):
        """设置绘图样式"""
        plt.style.use(self.config.style)
        sns.set_palette(self.config.color_palette)
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
    def plot_stacked_area_chart(self, data: pd.DataFrame, 
                              output_dir: str = "output/charts") -> str:
        """
        绘制堆叠面积图：展示所有行业成交额占比随时间的变化趋势
        
        Args:
            data: 包含日期、行业代码、成交金额的数据
            output_dir: 输出目录
            
        Returns:
            保存的文件路径
        """
        if data.empty:
            logger.error("Input data is empty")
            return ""
            
        # 计算每日各行业成交额占比
        daily_ratios = []
        
        for date, group in data.groupby('date'):
            total_volume = group['sector_volume_amount'].sum()
            
            if total_volume > 0:
                for _, row in group.iterrows():
                    daily_ratios.append({
                        'date': date,
                        'sector_code': row['sector_code'],
                        'volume_ratio': row['sector_volume_amount'] / total_volume
                    })
                    
        ratio_df = pd.DataFrame(daily_ratios)
        
        if ratio_df.empty:
            logger.error("No valid ratio data for plotting")
            return ""
            
        # 透视表：日期为索引，行业为列
        pivot_ratios = ratio_df.pivot_table(
            index='date',
            columns='sector_code',
            values='volume_ratio',
            fill_value=0
        )
        
        # 创建堆叠面积图
        fig, ax = plt.subplots(figsize=self.config.figure_size)
        
        # 使用不同颜色
        colors = plt.cm.Set3(np.linspace(0, 1, len(pivot_ratios.columns)))
        
        ax.stackplot(pivot_ratios.index, 
                    *[pivot_ratios[col] for col in pivot_ratios.columns],
                    labels=pivot_ratios.columns,
                    colors=colors,
                    alpha=0.8)
        
        ax.set_title('行业成交额占比变化趋势（堆叠面积图）', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('成交额占比', fontsize=12)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
        
        # 格式化y轴为百分比
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.1%}'))
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"sector_concentration_stacked_area_{timestamp}"
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        file_path = output_path / f"{filename}.png"
        
        fig.savefig(file_path, dpi=self.config.dpi, bbox_inches='tight')
        plt.close(fig)
        
        logger.info(f"Stacked area chart saved to {file_path}")
        return str(file_path)
        
    def plot_top_sectors_trend(self, data: pd.DataFrame, 
                             top_n: int = 10,
                             output_dir: str = "output/charts") -> str:
        """
        绘制折线图：仅展示成交额占比排名前十的行业变化趋势
        
        Args:
            data: 包含日期、行业代码、成交金额的数据
            top_n: 显示前N个行业
            output_dir: 输出目录
            
        Returns:
            保存的文件路径
        """
        if data.empty:
            logger.error("Input data is empty")
            return ""
            
        # 计算每日各行业成交额占比
        daily_ratios = []
        
        for date, group in data.groupby('date'):
            total_volume = group['sector_volume_amount'].sum()
            
            if total_volume > 0:
                for _, row in group.iterrows():
                    daily_ratios.append({
                        'date': date,
                        'sector_code': row['sector_code'],
                        'volume_ratio': row['sector_volume_amount'] / total_volume
                    })
                    
        ratio_df = pd.DataFrame(daily_ratios)
        
        if ratio_df.empty:
            logger.error("No valid ratio data for plotting")
            return ""
            
        # 计算平均占比，选择前N个行业
        avg_ratios = ratio_df.groupby('sector_code')['volume_ratio'].mean()
        top_sectors = avg_ratios.nlargest(top_n).index.tolist()
        
        # 筛选前N个行业的数据
        top_sector_data = ratio_df[ratio_df['sector_code'].isin(top_sectors)]
        
        # 透视表
        pivot_ratios = top_sector_data.pivot_table(
            index='date',
            columns='sector_code',
            values='volume_ratio',
            fill_value=0
        )
        
        # 创建折线图
        fig, ax = plt.subplots(figsize=self.config.figure_size)
        
        for sector in pivot_ratios.columns:
            ax.plot(pivot_ratios.index, pivot_ratios[sector], 
                   label=sector, linewidth=2, marker='o', markersize=3)
        
        ax.set_title(f'成交额占比前{top_n}行业变化趋势', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('成交额占比', fontsize=12)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
        
        # 格式化y轴为百分比
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.1%}'))
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"top_{top_n}_sectors_trend_{timestamp}"
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        file_path = output_path / f"{filename}.png"
        
        fig.savefig(file_path, dpi=self.config.dpi, bbox_inches='tight')
        plt.close(fig)
        
        logger.info(f"Top sectors trend chart saved to {file_path}")
        return str(file_path)


class CorrelationPlotter:
    """相关性分析可视化器"""
    
    def __init__(self):
        self.config = config_manager.get_visualization_config()
        self._setup_style()
        
    def _setup_style(self):
        """设置绘图样式"""
        plt.style.use(self.config.style)
        sns.set_palette(self.config.color_palette)
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
    def plot_correlation_heatmap(self, correlation_matrix: pd.DataFrame,
                               output_dir: str = "output/charts") -> str:
        """
        绘制行业相关性热力图
        
        Args:
            correlation_matrix: 相关性矩阵
            output_dir: 输出目录
            
        Returns:
            保存的文件路径
        """
        if correlation_matrix.empty:
            logger.error("Correlation matrix is empty")
            return ""
            
        # 创建热力图
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 创建掩码，隐藏上三角部分
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        
        # 绘制热力图
        sns.heatmap(correlation_matrix,
                   mask=mask,
                   annot=True,
                   cmap='RdBu_r',
                   center=0,
                   square=True,
                   fmt='.2f',
                   cbar_kws={"shrink": .8},
                   ax=ax)
        
        ax.set_title('行业收益率相关性分析', fontsize=14, fontweight='bold')
        
        # 突出显示高相关性和低相关性
        threshold_high = self.config.correlation_threshold_high
        threshold_low = self.config.correlation_threshold_low
        
        # 添加文本说明
        ax.text(0.02, 0.98, f'高相关性阈值: {threshold_high}', 
               transform=ax.transAxes, fontsize=10, 
               verticalalignment='top', bbox=dict(boxstyle='round', facecolor='red', alpha=0.3))
        ax.text(0.02, 0.93, f'低相关性阈值: {threshold_low}', 
               transform=ax.transAxes, fontsize=10, 
               verticalalignment='top', bbox=dict(boxstyle='round', facecolor='blue', alpha=0.3))
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"sector_correlation_heatmap_{timestamp}"
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        file_path = output_path / f"{filename}.png"
        
        fig.savefig(file_path, dpi=self.config.dpi, bbox_inches='tight')
        plt.close(fig)
        
        logger.info(f"Correlation heatmap saved to {file_path}")
        return str(file_path)
