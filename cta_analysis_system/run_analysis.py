#!/usr/bin/env python3
"""
CTA策略分析系统主启动脚本

简化的启动脚本，提供命令行接口运行CTA分析
"""

import sys
import argparse
import logging
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from comprehensive_analysis import CTAComprehensiveAnalyzer


def setup_logging(verbose: bool = False):
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'cta_analysis_{datetime.now().strftime("%Y%m%d")}.log')
        ]
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='CTA策略分析系统 - 专业的量化交易策略分析平台',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_analysis.py                    # 运行完整分析
  python run_analysis.py --verbose         # 详细日志输出
  python run_analysis.py --validate-only   # 仅验证系统
  python run_analysis.py --test            # 运行测试套件

输出文件:
  - Excel报告: reports/cta_analysis_report_YYYYMMDD_HHMMSS.xlsx
  - PNG仪表板: output/cta_analysis/dashboard/cta_dashboard_YYYYMMDD_HHMMSS.png
  - HTML仪表板: output/cta_analysis/dashboard/cta_dashboard_YYYYMMDD_HHMMSS.html
  - Markdown报告: reports/cta_analysis_report_YYYYMMDD_HHMMSS.md
        """
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='启用详细日志输出'
    )
    
    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='仅验证系统配置和数据，不运行分析'
    )
    
    parser.add_argument(
        '--test',
        action='store_true',
        help='运行测试套件'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='指定配置文件路径（可选）'
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 启动CTA策略分析系统")
    logger.info("=" * 60)
    
    try:
        if args.test:
            # 运行测试套件
            logger.info("🧪 运行测试套件...")
            import subprocess
            result = subprocess.run([sys.executable, 'tests/run_all_tests.py'], 
                                  capture_output=True, text=True)
            
            print(result.stdout)
            if result.stderr:
                print("错误输出:", result.stderr)
            
            return result.returncode
        
        elif args.validate_only:
            # 仅验证系统
            logger.info("🔍 验证系统配置和数据...")
            import subprocess
            result = subprocess.run([sys.executable, 'validate_system.py'], 
                                  capture_output=True, text=True)
            
            print(result.stdout)
            if result.stderr:
                print("错误输出:", result.stderr)
            
            return result.returncode
        
        else:
            # 运行完整分析
            logger.info("📊 运行完整CTA策略分析...")
            
            # 初始化分析器
            analyzer = CTAComprehensiveAnalyzer()
            
            # 运行分析
            success = analyzer.run_comprehensive_analysis()
            
            if success:
                logger.info("✅ 分析完成成功！")
                
                # 显示生成的文件
                generated_files = analyzer.analysis_results.get('generated_files', [])
                if generated_files:
                    logger.info(f"\n📋 生成的报告文件 ({len(generated_files)}个):")
                    for file_type, file_path in generated_files:
                        logger.info(f"   📄 {file_type}: {file_path}")
                
                # 显示分析摘要
                if hasattr(analyzer, 'analysis_results'):
                    summary = analyzer.generate_execution_summary()
                    logger.info(f"\n📊 分析摘要:")
                    logger.info(f"   处理记录数: {summary['data_processed']['cta_records']:,}")
                    logger.info(f"   分析时间范围: {summary['data_processed']['date_range']}")
                    logger.info(f"   发现问题数: {summary['issues_found']}")
                    logger.info(f"   解决方案数: {summary['resolutions_applied']}")
                
                logger.info("\n🎉 CTA策略分析完成！请查看生成的报告文件。")
                return 0
            else:
                logger.error("❌ 分析过程中发生错误")
                
                # 显示错误信息
                if hasattr(analyzer, 'issues_found') and analyzer.issues_found:
                    logger.error("发现的问题:")
                    for i, issue in enumerate(analyzer.issues_found, 1):
                        logger.error(f"   {i}. {issue}")
                
                return 1
    
    except KeyboardInterrupt:
        logger.info("\n⏹️ 用户中断分析")
        return 1
    
    except Exception as e:
        logger.error(f"❌ 系统错误: {e}")
        if args.verbose:
            import traceback
            logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
