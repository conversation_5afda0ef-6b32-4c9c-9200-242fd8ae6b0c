#!/usr/bin/env python3
"""
CTA策略综合分析脚本 - 2025年度数据分析

执行完整的CTA策略分析，包括所有增强功能：
1. 数据加载和质量检查
2. 策略-持仓映射配置
3. 风险指标验证
4. 日历期间时间窗口分析
5. 每日表现摘要整合
6. 多格式输出生成
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import logging
import warnings
import traceback

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 忽略警告
warnings.filterwarnings('ignore')

class CTAComprehensiveAnalyzer:
    """CTA策略综合分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.config = None
        self.cta_data = None
        self.position_data = None
        self.analysis_results = {}
        self.issues_found = []
        self.resolutions_applied = []
        
    def load_configuration(self):
        """加载配置"""
        try:
            from config.settings import config_manager
            self.config = config_manager.get_config()
            logger.info("✅ Configuration loaded successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to load configuration: {e}")
            self.issues_found.append(f"Configuration loading error: {e}")
            return False
    
    def load_and_validate_data(self):
        """加载和验证数据"""
        logger.info("🔍 Loading and validating CTA data...")
        
        # 加载CTA数据
        try:
            # 尝试不同的编码方式
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            cta_data = None
            
            for encoding in encodings:
                try:
                    cta_data = pd.read_csv('processed_cta_data.csv', encoding=encoding)
                    logger.info(f"✅ CTA data loaded with {encoding} encoding")
                    break
                except UnicodeDecodeError:
                    continue
            
            if cta_data is None:
                raise Exception("Failed to load CTA data with any encoding")
            
            self.cta_data = cta_data
            logger.info(f"📊 CTA data shape: {cta_data.shape}")
            logger.info(f"📅 Date range: {cta_data['trade_date'].min()} to {cta_data['trade_date'].max()}")
            
        except Exception as e:
            logger.error(f"❌ Failed to load CTA data: {e}")
            self.issues_found.append(f"CTA data loading error: {e}")
            return False
        
        # 加载持仓数据
        try:
            position_data = pd.read_csv('strategy_position.csv', encoding='utf-8-sig')
            self.position_data = position_data
            logger.info(f"✅ Position data loaded successfully")
            logger.info(f"📊 Position data shape: {position_data.shape}")
            logger.info(f"📅 Date range: {position_data['date'].min()} to {position_data['date'].max()}")
            
        except Exception as e:
            logger.error(f"❌ Failed to load position data: {e}")
            self.issues_found.append(f"Position data loading error: {e}")
            return False
        
        return self.validate_data_quality()
    
    def validate_data_quality(self):
        """验证数据质量"""
        logger.info("🔍 Validating data quality...")
        
        issues_fixed = 0
        
        # 1. 处理日期格式
        try:
            # CTA数据日期处理
            if self.cta_data['trade_date'].dtype == 'object':
                self.cta_data['trade_date'] = pd.to_datetime(self.cta_data['trade_date'])
                logger.info("✅ CTA data dates converted to datetime")
                issues_fixed += 1
            
            # 持仓数据日期处理
            if self.position_data['date'].dtype == 'object':
                self.position_data['date'] = pd.to_datetime(self.position_data['date'])
                logger.info("✅ Position data dates converted to datetime")
                issues_fixed += 1
                
        except Exception as e:
            logger.error(f"❌ Date conversion error: {e}")
            self.issues_found.append(f"Date conversion error: {e}")
            return False
        
        # 2. 检查必需字段
        required_cta_fields = ['trade_date', 'profit_loss_amount', 'strategy_category']
        missing_fields = [field for field in required_cta_fields if field not in self.cta_data.columns]
        
        if missing_fields:
            logger.error(f"❌ Missing required CTA fields: {missing_fields}")
            self.issues_found.append(f"Missing CTA fields: {missing_fields}")
            return False
        
        # 3. 处理缺失值
        before_na = self.cta_data.isna().sum().sum()
        self.cta_data = self.cta_data.fillna(0)
        after_na = self.cta_data.isna().sum().sum()
        
        if before_na > 0:
            logger.info(f"✅ Filled {before_na} missing values in CTA data")
            issues_fixed += 1
        
        # 4. 数据类型验证
        try:
            self.cta_data['profit_loss_amount'] = pd.to_numeric(self.cta_data['profit_loss_amount'], errors='coerce')
            self.cta_data = self.cta_data.dropna(subset=['profit_loss_amount'])
            logger.info("✅ Numeric data types validated")
            issues_fixed += 1
        except Exception as e:
            logger.error(f"❌ Data type validation error: {e}")
            self.issues_found.append(f"Data type validation error: {e}")
        
        # 5. 过滤2025年数据（如果有足够的数据）
        start_date = pd.Timestamp('2025-01-01')
        latest_date = None

        if len(self.cta_data) > 0 and 'trade_date' in self.cta_data.columns:
            try:
                # 确保日期列是datetime类型
                if self.cta_data['trade_date'].dtype == 'object':
                    self.cta_data['trade_date'] = pd.to_datetime(self.cta_data['trade_date'], errors='coerce')

                latest_date = self.cta_data['trade_date'].max()

                # 只有当最新日期确实是2025年或之后才进行过滤
                if pd.notna(latest_date) and isinstance(latest_date, pd.Timestamp) and latest_date >= start_date:
                    self.cta_data = self.cta_data[self.cta_data['trade_date'] >= start_date]
                    logger.info(f"✅ Filtered CTA data from {start_date.date()} to {latest_date.date()}")
            except Exception as e:
                logger.warning(f"⚠️ Could not filter CTA data by date: {e}")

        if len(self.position_data) > 0 and 'date' in self.position_data.columns:
            try:
                position_latest = self.position_data['date'].max()
                # 只有当数据中有2025年数据才进行过滤
                if pd.notna(position_latest) and position_latest >= start_date:
                    self.position_data = self.position_data[self.position_data['date'] >= start_date]
                    logger.info(f"✅ Filtered position data from {start_date.date()}")
            except Exception as e:
                logger.warning(f"⚠️ Could not filter position data by date: {e}")

        logger.info(f"📊 Final CTA data shape: {self.cta_data.shape}")
        logger.info(f"📊 Final position data shape: {self.position_data.shape}")
        
        if issues_fixed > 0:
            self.resolutions_applied.append(f"Fixed {issues_fixed} data quality issues")
        
        return True
    
    def run_performance_analysis(self):
        """运行性能分析"""
        logger.info("📈 Running performance analysis...")
        
        try:
            from analysis.performance_analyzer import StrategyPerformanceAnalyzer
            
            analyzer = StrategyPerformanceAnalyzer(self.config)
            
            # 策略类别性能分析
            strategy_results = analyzer.analyze_strategy_category_performance(
                self.cta_data, self.position_data
            )
            
            # 时间窗口性能分析
            time_window_results = analyzer.calculate_time_window_performance(self.cta_data)
            
            self.analysis_results['performance'] = {
                'strategy_category': strategy_results,
                'time_window_performance': time_window_results
            }
            
            logger.info("✅ Performance analysis completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Performance analysis failed: {e}")
            logger.error(traceback.format_exc())
            self.issues_found.append(f"Performance analysis error: {e}")
            return False
    
    def run_risk_analysis(self):
        """运行风险分析"""
        logger.info("⚠️ Running risk analysis...")

        try:
            from analysis.risk_analyzer import RiskAnalyzer

            analyzer = RiskAnalyzer(self.config)

            # 检查是否有性能分析结果
            if 'performance' not in self.analysis_results:
                logger.warning("⚠️ No performance results available for risk analysis")
                return False

            # 综合风险分析（包括验证）
            risk_results = analyzer.run_comprehensive_risk_analysis(
                self.analysis_results['performance']
            )
            
            self.analysis_results['risk'] = risk_results
            
            # 检查风险验证结果
            if 'risk_validation' in risk_results:
                validation = risk_results['risk_validation']['validation_summary']
                logger.info(f"✅ Risk validation: {validation['valid_strategies']}/{validation['total_strategies']} strategies passed")
                
                if validation['warnings']:
                    logger.warning(f"⚠️ {len(validation['warnings'])} risk warnings found")
                if validation['errors']:
                    logger.error(f"❌ {len(validation['errors'])} risk errors found")
            
            logger.info("✅ Risk analysis completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Risk analysis failed: {e}")
            logger.error(traceback.format_exc())
            self.issues_found.append(f"Risk analysis error: {e}")
            return False
    
    def run_contribution_analysis(self):
        """运行贡献分析"""
        logger.info("📊 Running contribution analysis...")
        
        try:
            from analysis.contribution_analyzer import ContributionAnalyzer
            
            analyzer = ContributionAnalyzer(self.config)
            
            # 时间窗口贡献分析
            contribution_results = analyzer.calculate_time_window_contributions(self.cta_data)
            
            self.analysis_results['contribution'] = contribution_results
            
            logger.info("✅ Contribution analysis completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Contribution analysis failed: {e}")
            logger.error(traceback.format_exc())
            self.issues_found.append(f"Contribution analysis error: {e}")
            return False

    def generate_reports(self):
        """生成报告"""
        logger.info("📋 Generating comprehensive reports...")

        generated_files = []

        try:
            # 1. Excel报告
            from reporting.excel_reporter import ExcelReporter

            excel_reporter = ExcelReporter(self.config)
            excel_file = excel_reporter.generate_comprehensive_report(
                performance_results=self.analysis_results.get('performance', {}),
                risk_results=self.analysis_results.get('risk', {}),
                contribution_results=self.analysis_results.get('contribution', {})
            )

            if excel_file:
                generated_files.append(('Excel Report', excel_file))
                logger.info(f"✅ Excel report generated: {excel_file}")

        except Exception as e:
            logger.error(f"❌ Excel report generation failed: {e}")
            self.issues_found.append(f"Excel report error: {e}")

        try:
            # 2. 仪表板生成
            from dashboard.dashboard_generator import DashboardGenerator

            dashboard_generator = DashboardGenerator(self.config)
            dashboard_files = dashboard_generator.generate_comprehensive_dashboard(
                performance_results=self.analysis_results.get('performance', {}),
                risk_results=self.analysis_results.get('risk', {}),
                contribution_results=self.analysis_results.get('contribution', {})
            )

            for file_type, file_path in dashboard_files.items():
                if file_path:
                    generated_files.append((file_type.title(), file_path))
                    logger.info(f"✅ {file_type} dashboard generated: {file_path}")

        except Exception as e:
            logger.error(f"❌ Dashboard generation failed: {e}")
            self.issues_found.append(f"Dashboard generation error: {e}")

        try:
            # 3. Markdown报告
            from reporting.markdown_reporter import MarkdownReporter

            markdown_reporter = MarkdownReporter(self.config)
            markdown_file = markdown_reporter.generate_comprehensive_report(
                performance_results=self.analysis_results.get('performance', {}),
                risk_results=self.analysis_results.get('risk', {}),
                contribution_results=self.analysis_results.get('contribution', {})
            )

            if markdown_file:
                generated_files.append(('Markdown Report', markdown_file))
                logger.info(f"✅ Markdown report generated: {markdown_file}")

        except Exception as e:
            logger.error(f"❌ Markdown report generation failed: {e}")
            self.issues_found.append(f"Markdown report error: {e}")

        self.analysis_results['generated_files'] = generated_files
        return len(generated_files) > 0

    def validate_enhancements(self):
        """验证增强功能"""
        logger.info("🔍 Validating system enhancements...")

        validation_results = {
            'calendar_time_windows': False,
            'risk_validation': False,
            'return_rate_calculations': False,
            'daily_performance_summary': False
        }

        try:
            # 1. 验证日历时间窗口
            if ('performance' in self.analysis_results and
                'time_window_performance' in self.analysis_results['performance']):
                time_windows = self.analysis_results['performance']['time_window_performance']

                for window_name, data in time_windows.items():
                    if data.get('calculation_method') == 'calendar':
                        validation_results['calendar_time_windows'] = True
                        break

                if validation_results['calendar_time_windows']:
                    logger.info("✅ Calendar-based time windows validated")
                else:
                    logger.warning("⚠️ Calendar time windows not detected")

            # 2. 验证风险指标验证
            if ('risk' in self.analysis_results and
                'risk_validation' in self.analysis_results['risk']):
                validation_summary = self.analysis_results['risk']['risk_validation']['validation_summary']
                if validation_summary['total_strategies'] > 0:
                    validation_results['risk_validation'] = True
                    logger.info("✅ Risk metrics validation working")
                else:
                    logger.warning("⚠️ No risk validation data found")

            # 3. 验证收益率计算
            if ('performance' in self.analysis_results and
                'strategy_category' in self.analysis_results['performance']):
                for strategy, data in self.analysis_results['performance']['strategy_category'].items():
                    if 'returns' in data and len(data['returns']) > 0:
                        validation_results['return_rate_calculations'] = True
                        logger.info("✅ Return rate calculations validated")
                        break

            # 4. 验证每日表现摘要
            generated_files = self.analysis_results.get('generated_files', [])
            for file_type, file_path in generated_files:
                if 'excel' in file_type.lower() or 'dashboard' in file_type.lower():
                    validation_results['daily_performance_summary'] = True
                    logger.info("✅ Daily performance summary in outputs")
                    break

        except Exception as e:
            logger.error(f"❌ Enhancement validation failed: {e}")

        return validation_results

    def generate_execution_summary(self):
        """生成执行摘要"""
        logger.info("📝 Generating execution summary...")

        summary = {
            'execution_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data_processed': {
                'cta_records': len(self.cta_data) if self.cta_data is not None else 0,
                'position_records': len(self.position_data) if self.position_data is not None else 0,
                'date_range': f"{self.cta_data['trade_date'].min()} to {self.cta_data['trade_date'].max()}" if self.cta_data is not None else "N/A"
            },
            'analysis_completed': {
                'performance_analysis': 'performance' in self.analysis_results,
                'risk_analysis': 'risk' in self.analysis_results,
                'contribution_analysis': 'contribution' in self.analysis_results
            },
            'outputs_generated': len(self.analysis_results.get('generated_files', [])),
            'issues_found': len(self.issues_found),
            'resolutions_applied': len(self.resolutions_applied)
        }

        return summary

    def run_comprehensive_analysis(self):
        """运行完整分析"""
        logger.info("🚀 Starting comprehensive CTA analysis...")
        logger.info("=" * 80)

        success = True

        # 1. 加载配置
        if not self.load_configuration():
            return False

        # 2. 加载和验证数据
        if not self.load_and_validate_data():
            return False

        # 3. 运行性能分析
        if not self.run_performance_analysis():
            success = False

        # 4. 运行风险分析
        if not self.run_risk_analysis():
            success = False

        # 5. 运行贡献分析
        if not self.run_contribution_analysis():
            success = False

        # 6. 生成报告
        if not self.generate_reports():
            success = False

        # 7. 验证增强功能
        validation_results = self.validate_enhancements()

        # 8. 生成执行摘要
        summary = self.generate_execution_summary()

        # 输出最终结果
        logger.info("=" * 80)
        logger.info("🎯 COMPREHENSIVE ANALYSIS COMPLETED")
        logger.info("=" * 80)

        logger.info(f"📊 Data Processed:")
        logger.info(f"   CTA Records: {summary['data_processed']['cta_records']:,}")
        logger.info(f"   Position Records: {summary['data_processed']['position_records']:,}")
        logger.info(f"   Date Range: {summary['data_processed']['date_range']}")

        logger.info(f"\n📈 Analysis Modules:")
        for module, completed in summary['analysis_completed'].items():
            status = "✅" if completed else "❌"
            logger.info(f"   {status} {module.replace('_', ' ').title()}")

        logger.info(f"\n📋 Outputs Generated: {summary['outputs_generated']}")
        for file_type, file_path in self.analysis_results.get('generated_files', []):
            logger.info(f"   📄 {file_type}: {file_path}")

        logger.info(f"\n🔍 Enhancement Validation:")
        for enhancement, validated in validation_results.items():
            status = "✅" if validated else "❌"
            logger.info(f"   {status} {enhancement.replace('_', ' ').title()}")

        if self.issues_found:
            logger.info(f"\n⚠️ Issues Found ({len(self.issues_found)}):")
            for i, issue in enumerate(self.issues_found, 1):
                logger.info(f"   {i}. {issue}")

        if self.resolutions_applied:
            logger.info(f"\n🔧 Resolutions Applied ({len(self.resolutions_applied)}):")
            for i, resolution in enumerate(self.resolutions_applied, 1):
                logger.info(f"   {i}. {resolution}")

        logger.info(f"\n🎉 Analysis {'COMPLETED SUCCESSFULLY' if success else 'COMPLETED WITH ISSUES'}")
        logger.info("=" * 80)

        return success


def main():
    """主函数"""
    analyzer = CTAComprehensiveAnalyzer()
    success = analyzer.run_comprehensive_analysis()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
