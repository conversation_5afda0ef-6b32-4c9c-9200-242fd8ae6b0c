"""
CTA分析可视化图表生成模块
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
if current_dir.name != 'cta_analysis_system':
    project_root = current_dir.parent
else:
    project_root = current_dir
sys.path.insert(0, str(project_root))


import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import logging
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from config.settings import CTAConfig
from visualization.drawdown_charts import DrawdownChartGenerator

logger = logging.getLogger(__name__)


class CTAChartGenerator:
    """CTA图表生成器"""
    
    def __init__(self, config: CTAConfig, output_dir: str = "charts"):
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 初始化回撤图表生成器
        self.drawdown_chart_generator = DrawdownChartGenerator(config)

        # 设置matplotlib样式
        plt.style.use(self.config.visualization.style)

        # 设置中文字体支持
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 颜色配置
        self.colors = self.config.visualization.colors

        # 图表配置
        self.figure_size = self.config.visualization.figure_size
        self.dpi = self.config.visualization.dpi
        
    def create_cumulative_returns_chart(self, performance_data: Dict[str, Dict[str, Any]], 
                                      title: str = "策略累积收益曲线") -> str:
        """创建累积收益曲线图"""
        logger.info("Creating cumulative returns chart...")
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figure_size,
                                      gridspec_kw={'height_ratios': [3, 1]})
        
        # 主图：累积收益曲线
        for strategy_name, data in performance_data.items():
            if 'cumulative_returns' in data and not data['cumulative_returns'].empty:
                cumulative_returns = data['cumulative_returns']
                dates = cumulative_returns.index
                
                # 绘制累积收益曲线
                color = self.colors['profit'] if data.get('total_pnl', 0) > 0 else self.colors['loss']
                ax1.plot(dates, cumulative_returns * 100, label=strategy_name, 
                        linewidth=2, color=color)
                
                # 绘制回撤区域
                if 'max_drawdown' in data and 'drawdown_series' in data['max_drawdown']:
                    drawdown = data['max_drawdown']['drawdown_series'] * 100
                    ax2.fill_between(dates, drawdown, 0, alpha=0.3, color=color)
        
        # 设置主图
        ax1.set_title(title, fontsize=16, fontweight='bold')
        ax1.set_ylabel('累积收益率 (%)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend(loc='upper left')
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 设置回撤图
        ax2.set_title('回撤曲线', fontsize=12)
        ax2.set_ylabel('回撤 (%)', fontsize=10)
        ax2.set_xlabel('日期', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # 格式化x轴日期
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        filename = f"cumulative_returns_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Cumulative returns chart saved: {filepath}")
        return str(filepath)
    
    def create_waterfall_chart(self, contribution_data: Dict[str, Any],
                             title: str = "策略收益贡献瀑布图") -> str:
        """创建收益贡献瀑布图"""
        logger.info("Creating waterfall chart...")

        # 检查数据结构，支持两种格式
        if "strategy_contributions" in contribution_data:
            strategy_contribs = contribution_data["strategy_contributions"]
        else:
            logger.warning("No strategy contributions data found")
            return ""
        
        # 准备数据
        strategies = list(strategy_contribs.keys())
        values = [strategy_contribs[s]["pnl"] for s in strategies]
        
        # 排序（按贡献大小）
        sorted_data = sorted(zip(strategies, values), key=lambda x: x[1], reverse=True)
        strategies, values = zip(*sorted_data)
        
        # 创建图表
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 计算累积位置
        cumulative = [0]
        for i, value in enumerate(values):
            cumulative.append(cumulative[-1] + value)
        
        # 绘制瀑布图
        for i, (strategy, value) in enumerate(zip(strategies, values)):
            color = self.colors['profit'] if value > 0 else self.colors['loss']
            
            # 绘制柱子
            ax.bar(i, abs(value), bottom=min(cumulative[i], cumulative[i+1]), 
                  color=color, alpha=0.7, edgecolor='black', linewidth=1)
            
            # 添加数值标签
            label_y = cumulative[i] + value/2
            ax.text(i, label_y, f'{value:.0f}', ha='center', va='center', 
                   fontweight='bold', fontsize=10)
            
            # 绘制连接线
            if i < len(values) - 1:
                ax.plot([i+0.4, i+0.6], [cumulative[i+1], cumulative[i+1]], 
                       'k--', alpha=0.5)
        
        # 绘制总计柱
        total_value = sum(values)
        total_color = self.colors['profit'] if total_value > 0 else self.colors['loss']
        ax.bar(len(strategies), abs(total_value), color=total_color, alpha=0.9, 
              edgecolor='black', linewidth=2, label=f'总计: {total_value:.0f}')
        
        # 设置图表
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.set_ylabel('收益贡献', fontsize=12)
        ax.set_xlabel('策略', fontsize=12)
        
        # 设置x轴标签
        x_labels = list(strategies) + ['总计']
        ax.set_xticks(range(len(x_labels)))
        ax.set_xticklabels(x_labels, rotation=45, ha='right')
        
        ax.grid(True, alpha=0.3, axis='y')
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.8)
        
        plt.tight_layout()
        
        # 保存图表
        filename = f"waterfall_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Waterfall chart saved: {filepath}")
        return str(filepath)
    
    def create_risk_return_scatter(self, performance_data: Dict[str, Dict[str, Any]], 
                                 title: str = "风险收益散点图") -> str:
        """创建风险收益散点图"""
        logger.info("Creating risk-return scatter plot...")
        
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 准备数据
        strategies = []
        annual_returns = []
        annual_volatilities = []
        sharpe_ratios = []
        
        for strategy_name, data in performance_data.items():
            if 'annual_return' in data and 'annual_volatility' in data:
                strategies.append(strategy_name)
                annual_returns.append(data['annual_return'] * 100)  # 转换为百分比
                annual_volatilities.append(data['annual_volatility'] * 100)
                sharpe_ratios.append(data.get('sharpe_ratio', 0))
        
        if not strategies:
            logger.warning("No data available for risk-return scatter plot")
            return ""
        
        # 创建散点图
        scatter = ax.scatter(annual_volatilities, annual_returns, 
                           s=[abs(sr) * 100 + 50 for sr in sharpe_ratios],  # 点大小基于夏普比率
                           c=sharpe_ratios, cmap='RdYlGn', alpha=0.7, 
                           edgecolors='black', linewidth=1)
        
        # 添加策略标签
        for i, strategy in enumerate(strategies):
            ax.annotate(strategy, (annual_volatilities[i], annual_returns[i]), 
                       xytext=(5, 5), textcoords='offset points', fontsize=10)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('夏普比率', fontsize=12)
        
        # 设置图表
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.set_xlabel('年化波动率 (%)', fontsize=12)
        ax.set_ylabel('年化收益率 (%)', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 添加象限线
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)
        
        plt.tight_layout()
        
        # 保存图表
        filename = f"risk_return_scatter_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Risk-return scatter plot saved: {filepath}")
        return str(filepath)
    
    def create_correlation_heatmap(self, correlation_matrix: pd.DataFrame, 
                                 title: str = "策略相关性热力图") -> str:
        """创建相关性热力图"""
        logger.info("Creating correlation heatmap...")
        
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 创建热力图
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))  # 只显示下三角
        
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='RdYlBu_r', 
                   center=0, square=True, linewidths=0.5, cbar_kws={"shrink": .8},
                   fmt='.2f', ax=ax)
        
        ax.set_title(title, fontsize=16, fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图表
        filename = f"correlation_heatmap_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Correlation heatmap saved: {filepath}")
        return str(filepath)
    
    def create_monthly_returns_heatmap(self, performance_data: Dict[str, Dict[str, Any]], 
                                     strategy_name: str, title: str = None) -> str:
        """创建月度收益热力图"""
        logger.info(f"Creating monthly returns heatmap for {strategy_name}...")
        
        if strategy_name not in performance_data or 'daily_pnl' not in performance_data[strategy_name]:
            logger.warning(f"No daily PnL data found for {strategy_name}")
            return ""
        
        daily_pnl = performance_data[strategy_name]['daily_pnl']
        
        # 转换为月度收益
        monthly_returns = daily_pnl.resample('M').sum()
        
        # 创建年月矩阵
        monthly_data = []
        years = []
        months = ['1月', '2月', '3月', '4月', '5月', '6月', 
                 '7月', '8月', '9月', '10月', '11月', '12月']
        
        for year in monthly_returns.index.year.unique():
            year_data = []
            years.append(year)
            
            for month in range(1, 13):
                try:
                    value = monthly_returns[
                        (monthly_returns.index.year == year) & 
                        (monthly_returns.index.month == month)
                    ].iloc[0]
                except:
                    value = np.nan
                year_data.append(value)
            
            monthly_data.append(year_data)
        
        # 创建DataFrame
        heatmap_data = pd.DataFrame(monthly_data, index=years, columns=months)
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, max(6, len(years) * 0.5)))
        
        # 创建热力图
        sns.heatmap(heatmap_data, annot=True, fmt='.0f', cmap='RdYlGn', 
                   center=0, cbar_kws={"label": "月度收益"}, ax=ax)
        
        if title is None:
            title = f"{strategy_name} 月度收益热力图"
        
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.set_xlabel('月份', fontsize=12)
        ax.set_ylabel('年份', fontsize=12)
        
        plt.tight_layout()
        
        # 保存图表
        filename = f"monthly_returns_heatmap_{strategy_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Monthly returns heatmap saved: {filepath}")
        return str(filepath)
    
    def create_rolling_metrics_chart(self, performance_data: Dict[str, Dict[str, Any]], 
                                   title: str = "滚动指标图") -> str:
        """创建滚动指标图"""
        logger.info("Creating rolling metrics chart...")
        
        fig, axes = plt.subplots(3, 1, figsize=(self.figure_size[0], 
                                               self.figure_size[1] * 1.5))
        
        for strategy_name, data in performance_data.items():
            if 'rolling_metrics' not in data:
                continue
            
            rolling_metrics = data['rolling_metrics']
            
            # 滚动收益率
            if 'rolling_return' in rolling_metrics:
                axes[0].plot(rolling_metrics['rolling_return'].index, 
                           rolling_metrics['rolling_return'] * 100, 
                           label=strategy_name, linewidth=2)
            
            # 滚动波动率
            if 'rolling_volatility' in rolling_metrics:
                axes[1].plot(rolling_metrics['rolling_volatility'].index, 
                           rolling_metrics['rolling_volatility'] * 100, 
                           label=strategy_name, linewidth=2)
            
            # 滚动夏普比率
            if 'rolling_sharpe' in rolling_metrics:
                axes[2].plot(rolling_metrics['rolling_sharpe'].index, 
                           rolling_metrics['rolling_sharpe'], 
                           label=strategy_name, linewidth=2)
        
        # 设置子图
        axes[0].set_title('滚动年化收益率', fontsize=14)
        axes[0].set_ylabel('收益率 (%)', fontsize=12)
        axes[0].grid(True, alpha=0.3)
        axes[0].legend()
        
        axes[1].set_title('滚动年化波动率', fontsize=14)
        axes[1].set_ylabel('波动率 (%)', fontsize=12)
        axes[1].grid(True, alpha=0.3)
        axes[1].legend()
        
        axes[2].set_title('滚动夏普比率', fontsize=14)
        axes[2].set_ylabel('夏普比率', fontsize=12)
        axes[2].set_xlabel('日期', fontsize=12)
        axes[2].grid(True, alpha=0.3)
        axes[2].legend()
        axes[2].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 格式化x轴
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存图表
        filename = f"rolling_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Rolling metrics chart saved: {filepath}")
        return str(filepath)

    def create_symbol_performance_bar_chart(self, symbol_performance_data: Dict[str, Dict[str, Any]],
                                           window_name: str = "daily",
                                           title: str = None) -> str:
        """创建品种表现排序条形图"""
        logger.info(f"Creating symbol performance bar chart for {window_name}...")

        if window_name not in symbol_performance_data:
            logger.warning(f"No data found for window: {window_name}")
            return ""

        window_data = symbol_performance_data[window_name]

        if title is None:
            title = f"{window_name.title()}品种表现排序"

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(title, fontsize=16, fontweight='bold')

        # 1. 整体品种表现
        if "overall" in window_data and window_data["overall"]:
            ax = axes[0, 0]
            self._plot_symbol_bar_chart(window_data["overall"], ax, "整体品种表现", "total_pnl")

        # 2. 按策略分组的最佳品种
        if "by_strategy" in window_data and window_data["by_strategy"]:
            ax = axes[0, 1]
            self._plot_strategy_best_symbols(window_data["by_strategy"], ax, "各策略最佳品种")

        # 3. 按频率分组的品种表现
        if "by_frequency" in window_data and window_data["by_frequency"]:
            ax = axes[1, 0]
            self._plot_frequency_symbols(window_data["by_frequency"], ax, "各频率品种表现")

        # 4. 按行业分组的品种表现
        if "by_industry" in window_data and window_data["by_industry"]:
            ax = axes[1, 1]
            self._plot_industry_symbols(window_data["by_industry"], ax, "各行业品种表现")

        plt.tight_layout()

        # 保存图表
        filename = f"symbol_performance_bar_{window_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        logger.info(f"Symbol performance bar chart saved: {filepath}")
        return str(filepath)

    def _plot_symbol_bar_chart(self, symbol_data: Dict[str, Dict[str, Any]], ax, title: str, metric: str = "total_pnl"):
        """绘制品种条形图"""
        if not symbol_data:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        # 准备数据
        symbols = list(symbol_data.keys())
        values = [symbol_data[symbol].get(metric, 0) for symbol in symbols]

        # 排序
        sorted_data = sorted(zip(symbols, values), key=lambda x: x[1], reverse=True)
        symbols, values = zip(*sorted_data)

        # 限制显示数量
        max_display = 10
        if len(symbols) > max_display:
            symbols = symbols[:max_display]
            values = values[:max_display]

        # 颜色设置
        colors = [self.colors['profit'] if v > 0 else self.colors['loss'] for v in values]

        # 绘制条形图
        bars = ax.barh(range(len(symbols)), values, color=colors, alpha=0.7)

        # 设置标签
        ax.set_yticks(range(len(symbols)))
        ax.set_yticklabels(symbols)
        ax.set_xlabel('收益')
        ax.set_title(title, fontsize=12, fontweight='bold')

        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, values)):
            ax.text(bar.get_width() + (max(values) - min(values)) * 0.01,
                   bar.get_y() + bar.get_height()/2,
                   f'{value:.0f}', ha='left', va='center', fontsize=9)

        ax.grid(True, alpha=0.3, axis='x')
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)

    def _plot_strategy_best_symbols(self, strategy_data: Dict[str, Dict[str, Dict[str, Any]]], ax, title: str):
        """绘制各策略最佳品种"""
        if not strategy_data:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        best_symbols = []
        strategies = []
        values = []

        for strategy, symbols in strategy_data.items():
            if symbols:
                # 找到该策略下表现最好的品种
                best_symbol = max(symbols.items(), key=lambda x: x[1].get('total_pnl', 0))
                best_symbols.append(f"{strategy}\n{best_symbol[0]}")
                strategies.append(strategy)
                values.append(best_symbol[1].get('total_pnl', 0))

        if not values:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        # 颜色设置
        colors = [self.colors['profit'] if v > 0 else self.colors['loss'] for v in values]

        # 绘制条形图
        bars = ax.bar(range(len(best_symbols)), values, color=colors, alpha=0.7)

        # 设置标签
        ax.set_xticks(range(len(best_symbols)))
        ax.set_xticklabels(best_symbols, rotation=45, ha='right')
        ax.set_ylabel('收益')
        ax.set_title(title, fontsize=12, fontweight='bold')

        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2, height + (max(values) - min(values)) * 0.01,
                   f'{value:.0f}', ha='center', va='bottom', fontsize=9)

        ax.grid(True, alpha=0.3, axis='y')
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)

    def _plot_frequency_symbols(self, freq_data: Dict[str, Dict[str, Dict[str, Any]]], ax, title: str):
        """绘制各频率品种表现"""
        if not freq_data:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        # 计算各频率的总收益
        freq_totals = {}
        for freq, symbols in freq_data.items():
            total = sum(symbol_info.get('total_pnl', 0) for symbol_info in symbols.values())
            freq_totals[freq] = total

        if not freq_totals:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        # 排序
        sorted_freqs = sorted(freq_totals.items(), key=lambda x: x[1], reverse=True)
        freqs, values = zip(*sorted_freqs)

        # 颜色设置
        colors = [self.colors['profit'] if v > 0 else self.colors['loss'] for v in values]

        # 绘制条形图
        bars = ax.bar(range(len(freqs)), values, color=colors, alpha=0.7)

        # 设置标签
        ax.set_xticks(range(len(freqs)))
        ax.set_xticklabels(freqs, rotation=45, ha='right')
        ax.set_ylabel('总收益')
        ax.set_title(title, fontsize=12, fontweight='bold')

        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2, height + (max(values) - min(values)) * 0.01,
                   f'{value:.0f}', ha='center', va='bottom', fontsize=9)

        ax.grid(True, alpha=0.3, axis='y')
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)

    def _plot_industry_symbols(self, industry_data: Dict[str, Dict[str, Dict[str, Any]]], ax, title: str):
        """绘制各行业品种表现"""
        if not industry_data:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        # 计算各行业的总收益
        industry_totals = {}
        for industry, symbols in industry_data.items():
            total = sum(symbol_info.get('total_pnl', 0) for symbol_info in symbols.values())
            industry_totals[industry] = total

        if not industry_totals:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        # 排序
        sorted_industries = sorted(industry_totals.items(), key=lambda x: x[1], reverse=True)
        industries, values = zip(*sorted_industries)

        # 颜色设置
        colors = [self.colors['profit'] if v > 0 else self.colors['loss'] for v in values]

        # 绘制条形图
        bars = ax.bar(range(len(industries)), values, color=colors, alpha=0.7)

        # 设置标签
        ax.set_xticks(range(len(industries)))
        ax.set_xticklabels(industries, rotation=45, ha='right')
        ax.set_ylabel('总收益')
        ax.set_title(title, fontsize=12, fontweight='bold')

        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2, height + (max(values) - min(values)) * 0.01,
                   f'{value:.0f}', ha='center', va='bottom', fontsize=9)

        ax.grid(True, alpha=0.3, axis='y')
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)

    def create_symbol_contribution_heatmap(self, symbol_performance_data: Dict[str, Dict[str, Any]],
                                         window_name: str = "daily",
                                         title: str = None) -> str:
        """创建品种收益贡献热力图（策略 vs 品种）"""
        logger.info(f"Creating symbol contribution heatmap for {window_name}...")

        if window_name not in symbol_performance_data:
            logger.warning(f"No data found for window: {window_name}")
            return ""

        window_data = symbol_performance_data[window_name]

        if "by_strategy" not in window_data or not window_data["by_strategy"]:
            logger.warning("No strategy-symbol data found")
            return ""

        if title is None:
            title = f"{window_name.title()}品种收益贡献热力图"

        # 准备数据矩阵
        strategy_symbol_data = window_data["by_strategy"]

        # 获取所有策略和品种
        all_strategies = list(strategy_symbol_data.keys())
        all_symbols = set()
        for symbols in strategy_symbol_data.values():
            all_symbols.update(symbols.keys())
        all_symbols = sorted(list(all_symbols))

        # 创建数据矩阵
        matrix_data = []
        for strategy in all_strategies:
            row = []
            for symbol in all_symbols:
                if symbol in strategy_symbol_data[strategy]:
                    pnl = strategy_symbol_data[strategy][symbol].get('total_pnl', 0)
                    row.append(pnl)
                else:
                    row.append(0)
            matrix_data.append(row)

        # 创建DataFrame
        heatmap_df = pd.DataFrame(matrix_data, index=all_strategies, columns=all_symbols)

        # 创建图表
        fig, ax = plt.subplots(figsize=(max(12, len(all_symbols) * 0.8), max(8, len(all_strategies) * 0.8)))

        # 创建热力图
        sns.heatmap(heatmap_df, annot=True, fmt='.0f', cmap='RdYlGn',
                   center=0, cbar_kws={"label": "收益贡献"}, ax=ax)

        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.set_xlabel('品种类别', fontsize=12)
        ax.set_ylabel('策略类别', fontsize=12)

        # 旋转x轴标签
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

        plt.tight_layout()

        # 保存图表
        filename = f"symbol_contribution_heatmap_{window_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        logger.info(f"Symbol contribution heatmap saved: {filepath}")
        return str(filepath)

    def create_symbol_risk_return_scatter(self, symbol_performance_data: Dict[str, Dict[str, Any]],
                                        window_name: str = "daily",
                                        title: str = None) -> str:
        """创建品种风险收益散点图（按不同层级着色）"""
        logger.info(f"Creating symbol risk-return scatter for {window_name}...")

        if window_name not in symbol_performance_data:
            logger.warning(f"No data found for window: {window_name}")
            return ""

        window_data = symbol_performance_data[window_name]

        if title is None:
            title = f"{window_name.title()}品种风险收益散点图"

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(title, fontsize=16, fontweight='bold')

        # 1. 整体品种散点图
        if "overall" in window_data and window_data["overall"]:
            ax = axes[0, 0]
            self._plot_symbol_scatter(window_data["overall"], ax, "整体品种风险收益", color_by="performance")

        # 2. 按策略着色的散点图
        if "by_strategy" in window_data and window_data["by_strategy"]:
            ax = axes[0, 1]
            self._plot_symbol_scatter_by_strategy(window_data["by_strategy"], ax, "按策略分组")

        # 3. 按频率着色的散点图
        if "by_frequency" in window_data and window_data["by_frequency"]:
            ax = axes[1, 0]
            self._plot_symbol_scatter_by_frequency(window_data["by_frequency"], ax, "按频率分组")

        # 4. 按行业着色的散点图
        if "by_industry" in window_data and window_data["by_industry"]:
            ax = axes[1, 1]
            self._plot_symbol_scatter_by_industry(window_data["by_industry"], ax, "按行业分组")

        plt.tight_layout()

        # 保存图表
        filename = f"symbol_risk_return_scatter_{window_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        logger.info(f"Symbol risk-return scatter saved: {filepath}")
        return str(filepath)

    def _plot_symbol_scatter(self, symbol_data: Dict[str, Dict[str, Any]], ax, title: str, color_by: str = "performance"):
        """绘制品种散点图"""
        if not symbol_data:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        symbols = []
        returns = []
        volatilities = []
        sharpe_ratios = []

        for symbol, metrics in symbol_data.items():
            if metrics.get('annual_volatility', 0) > 0:  # 只显示有波动率的品种
                symbols.append(symbol)
                returns.append(metrics.get('annual_return', 0) * 100)
                volatilities.append(metrics.get('annual_volatility', 0) * 100)
                sharpe_ratios.append(metrics.get('sharpe_ratio', 0))

        if not symbols:
            ax.text(0.5, 0.5, '无有效数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        # 创建散点图
        if color_by == "performance":
            colors = [self.colors['profit'] if r > 0 else self.colors['loss'] for r in returns]
            scatter = ax.scatter(volatilities, returns, c=colors, s=60, alpha=0.7, edgecolors='black')
        else:
            scatter = ax.scatter(volatilities, returns, c=sharpe_ratios, cmap='RdYlGn',
                               s=60, alpha=0.7, edgecolors='black')
            plt.colorbar(scatter, ax=ax, label='夏普比率')

        # 添加标签（只标注表现最好和最差的）
        if len(symbols) > 2:
            best_idx = returns.index(max(returns))
            worst_idx = returns.index(min(returns))

            ax.annotate(f'最佳: {symbols[best_idx]}',
                       (volatilities[best_idx], returns[best_idx]),
                       xytext=(5, 5), textcoords='offset points', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.7))

            ax.annotate(f'最差: {symbols[worst_idx]}',
                       (volatilities[worst_idx], returns[worst_idx]),
                       xytext=(5, -15), textcoords='offset points', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.7))

        ax.set_xlabel('年化波动率 (%)')
        ax.set_ylabel('年化收益率 (%)')
        ax.set_title(title, fontsize=12, fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)

    def _plot_symbol_scatter_by_strategy(self, strategy_data: Dict[str, Dict[str, Dict[str, Any]]], ax, title: str):
        """按策略着色的品种散点图"""
        if not strategy_data:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        import matplotlib.cm as cm
        colors = cm.Set3(np.linspace(0, 1, len(strategy_data)))

        for i, (strategy, symbols) in enumerate(strategy_data.items()):
            returns = []
            volatilities = []
            symbol_names = []

            for symbol, metrics in symbols.items():
                if metrics.get('annual_volatility', 0) > 0:
                    returns.append(metrics.get('annual_return', 0) * 100)
                    volatilities.append(metrics.get('annual_volatility', 0) * 100)
                    symbol_names.append(symbol)

            if returns:
                ax.scatter(volatilities, returns, c=[colors[i]], label=strategy,
                          s=60, alpha=0.7, edgecolors='black')

        ax.set_xlabel('年化波动率 (%)')
        ax.set_ylabel('年化收益率 (%)')
        ax.set_title(title, fontsize=12, fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.legend()
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)

    def _plot_symbol_scatter_by_frequency(self, freq_data: Dict[str, Dict[str, Dict[str, Any]]], ax, title: str):
        """按频率着色的品种散点图"""
        if not freq_data:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        import matplotlib.cm as cm
        colors = cm.tab10(np.linspace(0, 1, len(freq_data)))

        for i, (freq, symbols) in enumerate(freq_data.items()):
            returns = []
            volatilities = []

            for symbol, metrics in symbols.items():
                if metrics.get('annual_volatility', 0) > 0:
                    returns.append(metrics.get('annual_return', 0) * 100)
                    volatilities.append(metrics.get('annual_volatility', 0) * 100)

            if returns:
                ax.scatter(volatilities, returns, c=[colors[i]], label=freq,
                          s=60, alpha=0.7, edgecolors='black')

        ax.set_xlabel('年化波动率 (%)')
        ax.set_ylabel('年化收益率 (%)')
        ax.set_title(title, fontsize=12, fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.legend()
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)

    def _plot_symbol_scatter_by_industry(self, industry_data: Dict[str, Dict[str, Dict[str, Any]]], ax, title: str):
        """按行业着色的品种散点图"""
        if not industry_data:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        import matplotlib.cm as cm
        colors = cm.Paired(np.linspace(0, 1, len(industry_data)))

        for i, (industry, symbols) in enumerate(industry_data.items()):
            returns = []
            volatilities = []

            for symbol, metrics in symbols.items():
                if metrics.get('annual_volatility', 0) > 0:
                    returns.append(metrics.get('annual_return', 0) * 100)
                    volatilities.append(metrics.get('annual_volatility', 0) * 100)

            if returns:
                ax.scatter(volatilities, returns, c=[colors[i]], label=industry,
                          s=60, alpha=0.7, edgecolors='black')

        ax.set_xlabel('年化波动率 (%)')
        ax.set_ylabel('年化收益率 (%)')
        ax.set_title(title, fontsize=12, fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.legend()
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)

    def create_drawdown_charts(self, risk_results: Dict[str, Any]) -> List[str]:
        """创建回撤分析图表"""
        logger.info("Creating drawdown analysis charts...")

        chart_files = []

        # 检查是否有回撤分析数据
        if "drawdown_analysis" not in risk_results:
            logger.warning("No drawdown analysis data found")
            return chart_files

        drawdown_analysis = risk_results["drawdown_analysis"]

        # 创建回撤时间线图
        try:
            timeline_chart = self.drawdown_chart_generator.create_drawdown_timeline_chart(
                drawdown_analysis, self.output_dir
            )
            if timeline_chart:
                chart_files.append(timeline_chart)
        except Exception as e:
            logger.warning(f"Failed to create drawdown timeline chart: {e}")

        # 创建回撤统计图
        try:
            statistics_chart = self.drawdown_chart_generator.create_drawdown_statistics_chart(
                drawdown_analysis, self.output_dir
            )
            if statistics_chart:
                chart_files.append(statistics_chart)
        except Exception as e:
            logger.warning(f"Failed to create drawdown statistics chart: {e}")

        # 创建月度/年度回撤图
        try:
            monthly_yearly_chart = self.drawdown_chart_generator.create_monthly_yearly_drawdown_chart(
                drawdown_analysis, self.output_dir
            )
            if monthly_yearly_chart:
                chart_files.append(monthly_yearly_chart)
        except Exception as e:
            logger.warning(f"Failed to create monthly/yearly drawdown chart: {e}")

        logger.info(f"Created {len(chart_files)} drawdown charts")
        return chart_files

    def create_enhanced_cumulative_returns_chart(self, performance_data: Dict[str, Dict[str, Any]],
                                               risk_results: Dict[str, Any],
                                               title: str = "增强累积收益曲线（含回撤区间）") -> str:
        """创建增强的累积收益曲线图，叠加回撤区间"""
        logger.info("Creating enhanced cumulative returns chart with drawdown overlay...")

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figure_size,
                                      gridspec_kw={'height_ratios': [3, 1]})

        # 主图：累积收益曲线
        for strategy_name, data in performance_data.items():
            if 'cumulative_returns' in data and not data['cumulative_returns'].empty:
                cumulative_returns = data['cumulative_returns']
                dates = cumulative_returns.index

                # 绘制累积收益曲线
                color = self.colors['profit'] if data.get('total_pnl', 0) > 0 else self.colors['loss']
                ax1.plot(dates, cumulative_returns * 100, label=strategy_name,
                        linewidth=2, color=color)

        # 叠加回撤区间（如果有回撤分析数据）
        if "drawdown_analysis" in risk_results:
            self._overlay_drawdown_areas(ax1, risk_results["drawdown_analysis"])

        # 设置主图
        ax1.set_title(title, fontsize=16, fontweight='bold')
        ax1.set_ylabel('累积收益率 (%)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend(loc='upper left')
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # 下图：回撤深度
        self._plot_drawdown_depth(ax2, performance_data, risk_results)

        # 格式化x轴日期
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 保存图表
        filename = f"enhanced_cumulative_returns_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        logger.info(f"Enhanced cumulative returns chart saved: {filepath}")
        return str(filepath)

    def _overlay_drawdown_areas(self, ax, drawdown_analysis: Dict[str, Any]):
        """在累积收益图上叠加回撤区域"""
        overall_analysis = drawdown_analysis.get("overall_analysis", {})
        drawdown_periods = overall_analysis.get("drawdown_periods", [])
        cumulative_returns = overall_analysis.get("cumulative_returns", pd.Series())

        if cumulative_returns.empty or not drawdown_periods:
            return

        # 获取回撤区域颜色配置
        try:
            drawdown_color = self.config.analysis.drawdown_analysis.visualization.colors.drawdown_area
        except (AttributeError, KeyError):
            drawdown_color = "#FF6B6B"

        for period in drawdown_periods:
            start_date = period.start_date
            end_date = period.end_date

            # 获取回撤期间的数据
            period_data = cumulative_returns[start_date:end_date]

            if not period_data.empty:
                # 转换为百分比
                period_data_pct = period_data * 100
                peak_value_pct = period.peak_value * 100

                # 绘制回撤区域
                ax.fill_between(period_data.index,
                              period_data_pct.values,
                              peak_value_pct,
                              color=drawdown_color,
                              alpha=0.3,
                              label='回撤区间' if period.sequence_number == 1 else "")

    def _plot_drawdown_depth(self, ax, performance_data: Dict[str, Any], risk_results: Dict[str, Any]):
        """绘制回撤深度图"""
        # 尝试从回撤分析中获取数据
        if "drawdown_analysis" in risk_results:
            overall_analysis = risk_results["drawdown_analysis"].get("overall_analysis", {})
            cumulative_returns = overall_analysis.get("cumulative_returns", pd.Series())

            if not cumulative_returns.empty:
                # 计算回撤序列
                running_max = cumulative_returns.expanding().max()
                drawdown_series = (cumulative_returns - running_max) / running_max * 100

                # 获取回撤区域颜色配置
                try:
                    drawdown_color = self.config.analysis.drawdown_analysis.visualization.colors.drawdown_area
                except (AttributeError, KeyError):
                    drawdown_color = "#FF6B6B"

                # 绘制回撤深度
                ax.fill_between(drawdown_series.index, drawdown_series.values, 0,
                              color=drawdown_color, alpha=0.6, label='回撤深度')

                ax.set_title('回撤深度', fontsize=12)
                ax.set_ylabel('回撤 (%)', fontsize=10)
                ax.set_xlabel('日期', fontsize=12)
                ax.grid(True, alpha=0.3)
                ax.legend()
                return

        # 如果没有回撤分析数据，使用传统方法
        for strategy_name, data in performance_data.items():
            if 'max_drawdown' in data and 'drawdown_series' in data['max_drawdown']:
                drawdown = data['max_drawdown']['drawdown_series'] * 100
                color = self.colors['profit'] if data.get('total_pnl', 0) > 0 else self.colors['loss']
                ax.fill_between(drawdown.index, drawdown, 0, alpha=0.3, color=color, label=strategy_name)

        ax.set_title('回撤曲线', fontsize=12)
        ax.set_ylabel('回撤 (%)', fontsize=10)
        ax.set_xlabel('日期', fontsize=12)
        ax.grid(True, alpha=0.3)
        if len(performance_data) > 1:
            ax.legend()
