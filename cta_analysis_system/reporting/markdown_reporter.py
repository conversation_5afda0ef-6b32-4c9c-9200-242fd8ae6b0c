"""
CTA分析Markdown报告生成模块
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
if current_dir.name != 'cta_analysis_system':
    project_root = current_dir.parent
else:
    project_root = current_dir
sys.path.insert(0, str(project_root))


import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import logging
from pathlib import Path
from datetime import datetime

from config.settings import CTAConfig

logger = logging.getLogger(__name__)


class MarkdownReporter:
    """Markdown报告生成器"""
    
    def __init__(self, config: CTAConfig, output_dir: str = "reports"):
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_comprehensive_report(self, performance_results: Dict[str, Any],
                                    risk_results: Dict[str, Any],
                                    contribution_results: Dict[str, Any],
                                    position_results: Dict[str, Any] = None,
                                    chart_files: List[str] = None,
                                    dashboard_files: Dict[str, str] = None) -> str:
        """生成综合Markdown报告"""
        logger.info("Generating comprehensive Markdown report...")
        
        try:
            report_content = []
            
            # 报告标题和摘要
            report_content.extend(self._generate_header())
            report_content.extend(self._generate_executive_summary(performance_results, risk_results, contribution_results))
            
            # 策略表现分析
            report_content.extend(self._generate_performance_section(performance_results))

            # 品种表现分析
            report_content.extend(self._generate_symbol_performance_section(performance_results))
            
            # 风险分析
            report_content.extend(self._generate_risk_section(risk_results))

            # 回撤区间分析
            if "drawdown_analysis" in risk_results:
                report_content.extend(self._generate_drawdown_section(risk_results["drawdown_analysis"]))
            
            # 贡献分析
            report_content.extend(self._generate_contribution_section(contribution_results))

            # 持仓分析
            if position_results:
                report_content.extend(self._generate_position_section(position_results))

            # 图表展示
            if chart_files:
                report_content.extend(self._generate_charts_section(chart_files))

            # 仪表板展示
            if dashboard_files:
                report_content.extend(self._generate_dashboard_section(dashboard_files))
            
            # 策略建议
            report_content.extend(self._generate_recommendations_section(performance_results, risk_results, contribution_results))
            
            # 附录
            report_content.extend(self._generate_appendix())
            
            # 保存报告
            filename = f"cta_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            filepath = self.output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_content))
            
            logger.info(f"Markdown report saved: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"Failed to generate Markdown report: {e}")
            return ""
    
    def _generate_header(self) -> List[str]:
        """生成报告头部"""
        current_time = datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')
        
        return [
            "# CTA策略表现深度分析报告",
            "",
            f"**生成时间：** {current_time}",
            "",
            "---",
            ""
        ]
    
    def _generate_executive_summary(self, performance_results: Dict[str, Any], 
                                  risk_results: Dict[str, Any], 
                                  contribution_results: Dict[str, Any]) -> List[str]:
        """生成执行摘要"""
        content = [
            "## 执行摘要",
            "",
            "### 关键发现",
            ""
        ]
        
        # 策略表现摘要
        if "strategy_category" in performance_results:
            total_strategies = len(performance_results["strategy_category"])
            profitable_strategies = sum(1 for data in performance_results["strategy_category"].values() 
                                      if data.get("total_pnl", 0) > 0)
            
            content.extend([
                f"- **策略数量：** {total_strategies}个策略类别",
                f"- **盈利策略：** {profitable_strategies}个策略盈利",
                f"- **盈利比例：** {profitable_strategies/total_strategies*100:.1f}%",
                ""
            ])
            
            # 最佳和最差策略
            best_strategy = max(performance_results["strategy_category"].items(), 
                              key=lambda x: x[1].get("total_pnl", 0))
            worst_strategy = min(performance_results["strategy_category"].items(), 
                               key=lambda x: x[1].get("total_pnl", 0))
            
            content.extend([
                f"- **最佳策略：** {best_strategy[0]} (收益: {best_strategy[1].get('total_pnl', 0):.0f})",
                f"- **最差策略：** {worst_strategy[0]} (收益: {worst_strategy[1].get('total_pnl', 0):.0f})",
                ""
            ])
        
        # 风险摘要
        if "risk_summary" in risk_results:
            risk_summary = risk_results["risk_summary"]
            content.extend([
                f"- **整体风险水平：** {risk_summary.get('overall_risk_level', 'N/A')}",
                f"- **主要风险数量：** {len(risk_summary.get('key_risks', []))}个",
                ""
            ])
        
        # 贡献摘要
        if "contribution_summary" in contribution_results:
            contrib_summary = contribution_results["contribution_summary"]
            content.extend([
                f"- **贡献集中度：** {contrib_summary.get('contribution_concentration', 0):.1f}%",
                f"- **分散化得分：** {contrib_summary.get('diversification_score', 0):.1f}",
                ""
            ])
        
        content.append("---")
        content.append("")
        
        return content
    
    def _generate_performance_section(self, performance_results: Dict[str, Any]) -> List[str]:
        """生成表现分析部分"""
        content = [
            "## 策略表现分析",
            ""
        ]
        
        # 策略类别表现
        if "strategy_category" in performance_results:
            content.extend([
                "### 策略类别表现",
                "",
                "| 策略类别 | 总收益 | 年化收益率(%) | 年化波动率(%) | 夏普比率 | 最大回撤(%) |",
                "|---------|--------|--------------|--------------|----------|------------|"
            ])
            
            for strategy, data in performance_results["strategy_category"].items():
                total_pnl = data.get("total_pnl", 0)
                annual_return = data.get("annual_return", 0) * 100
                annual_vol = data.get("annual_volatility", 0) * 100
                sharpe = data.get("sharpe_ratio", 0)
                max_dd = data.get("max_drawdown", {}).get("max_drawdown", 0) * 100
                
                content.append(f"| {strategy} | {total_pnl:.0f} | {annual_return:.2f} | {annual_vol:.2f} | {sharpe:.2f} | {max_dd:.2f} |")
            
            content.extend(["", ""])
        
        # 时间窗口表现
        if "time_window_performance" in performance_results:
            content.extend([
                "### 时间窗口表现",
                "",
                "| 时间窗口 | 总收益 | 交易天数 | 日均收益 |",
                "|---------|--------|----------|----------|"
            ])
            
            for window, data in performance_results["time_window_performance"].items():
                total_pnl = data.get("total_pnl", 0)
                trading_days = data.get("trading_days", 0)
                avg_daily = total_pnl / max(trading_days, 1)
                
                content.append(f"| {window} | {total_pnl:.0f} | {trading_days} | {avg_daily:.2f} |")
            
            content.extend(["", ""])
        
        # Trend策略详细分析
        if "trend_details" in performance_results and performance_results["trend_details"]:
            content.extend([
                "### Trend策略详细分析",
                ""
            ])
            
            trend_details = performance_results["trend_details"]
            
            # 按信号分析
            if "by_signal" in trend_details:
                content.extend([
                    "#### 按信号类型分析",
                    "",
                    "| 信号类型 | 总收益 | 年化收益率(%) | 夏普比率 |",
                    "|---------|--------|--------------|----------|"
                ])
                
                for signal, data in trend_details["by_signal"].items():
                    total_pnl = data.get("total_pnl", 0)
                    annual_return = data.get("annual_return", 0) * 100
                    sharpe = data.get("sharpe_ratio", 0)
                    
                    content.append(f"| {signal} | {total_pnl:.0f} | {annual_return:.2f} | {sharpe:.2f} |")
                
                content.extend(["", ""])
            
            # 按频率分析
            if "by_frequency" in trend_details:
                content.extend([
                    "#### 按信号频率分析",
                    "",
                    "| 信号频率 | 总收益 | 年化收益率(%) | 夏普比率 |",
                    "|---------|--------|--------------|----------|"
                ])
                
                for freq, data in trend_details["by_frequency"].items():
                    total_pnl = data.get("total_pnl", 0)
                    annual_return = data.get("annual_return", 0) * 100
                    sharpe = data.get("sharpe_ratio", 0)
                    
                    content.append(f"| {freq} | {total_pnl:.0f} | {annual_return:.2f} | {sharpe:.2f} |")
                
                content.extend(["", ""])
        
        content.append("---")
        content.append("")
        
        return content

    def _generate_symbol_performance_section(self, performance_results: Dict[str, Any]) -> List[str]:
        """生成品种表现分析部分"""
        content = [
            "## 品种表现分析",
            ""
        ]

        if "symbol_category_performance" not in performance_results:
            content.extend([
                "品种表现数据不可用",
                "",
                "---",
                ""
            ])
            return content

        symbol_perf_data = performance_results["symbol_category_performance"]

        for window_name in ["daily", "weekly", "monthly"]:
            if window_name not in symbol_perf_data:
                continue

            window_data = symbol_perf_data[window_name]

            content.extend([
                f"### {window_name.title()}品种表现",
                ""
            ])

            # 整体品种表现前5名
            if "overall" in window_data and window_data["overall"]:
                content.extend([
                    "#### 整体品种表现前5名",
                    "",
                    "| 品种 | 总收益 | 年化收益率(%) | 夏普比率 | 胜率(%) |",
                    "|------|--------|--------------|----------|---------|"
                ])

                # 获取前5名品种
                from analysis.performance_analyzer import StrategyPerformanceAnalyzer
                analyzer = StrategyPerformanceAnalyzer(None)  # 临时创建
                top_symbols = analyzer.get_top_symbol_categories(window_data["overall"], top_n=5)

                for symbol_info in top_symbols["top_performers"]:
                    symbol = symbol_info["symbol"]
                    total_pnl = symbol_info.get("total_pnl", 0)
                    annual_return = symbol_info.get("annual_return", 0) * 100
                    sharpe_ratio = symbol_info.get("sharpe_ratio", 0)
                    win_rate = symbol_info.get("win_rate", 0) * 100

                    content.append(f"| {symbol} | {total_pnl:.0f} | {annual_return:.2f} | {sharpe_ratio:.2f} | {win_rate:.1f} |")

                content.extend(["", ""])

            # 各策略最佳品种
            if "by_strategy" in window_data and window_data["by_strategy"]:
                content.extend([
                    "#### 各策略最佳品种",
                    "",
                    "| 策略 | 最佳品种 | 收益 | 夏普比率 |",
                    "|------|----------|------|----------|"
                ])

                for strategy, symbols in window_data["by_strategy"].items():
                    if symbols:
                        best_symbol = max(symbols.items(), key=lambda x: x[1].get('total_pnl', 0))
                        symbol_name = best_symbol[0]
                        pnl = best_symbol[1].get('total_pnl', 0)
                        sharpe = best_symbol[1].get('sharpe_ratio', 0)

                        content.append(f"| {strategy} | {symbol_name} | {pnl:.0f} | {sharpe:.2f} |")

                content.extend(["", ""])

        content.append("---")
        content.append("")

        return content

    def _generate_risk_section(self, risk_results: Dict[str, Any]) -> List[str]:
        """生成风险分析部分"""
        content = [
            "## 风险分析",
            ""
        ]
        
        # 风险摘要
        if "risk_summary" in risk_results:
            risk_summary = risk_results["risk_summary"]
            content.extend([
                "### 风险摘要",
                "",
                f"**整体风险水平：** {risk_summary.get('overall_risk_level', 'N/A')}",
                ""
            ])
            
            # 主要风险
            key_risks = risk_summary.get("key_risks", [])
            if key_risks:
                content.extend(["**主要风险：**", ""])
                for risk in key_risks:
                    content.append(f"- {risk}")
                content.append("")
        
        # 尾部风险指标
        if "tail_risk_metrics" in risk_results:
            content.extend([
                "### 尾部风险指标",
                "",
                "| 策略 | 偏度 | 峰度 | 最大连续亏损天数 | 尾部比率 |",
                "|------|------|------|-----------------|----------|"
            ])
            
            for strategy, metrics in risk_results["tail_risk_metrics"].items():
                skewness = metrics.get("skewness", 0)
                kurtosis = metrics.get("kurtosis", 0)
                max_consecutive = metrics.get("max_consecutive_losses", 0)
                tail_ratio = metrics.get("tail_ratio", 0)
                
                content.append(f"| {strategy} | {skewness:.2f} | {kurtosis:.2f} | {max_consecutive} | {tail_ratio:.2f} |")
            
            content.extend(["", ""])
        
        # 风险调整后收益
        if "risk_adjusted_returns" in risk_results:
            content.extend([
                "### 风险调整后收益指标",
                "",
                "| 策略 | 信息比率 | 卡尔马比率 | 收益风险比 |",
                "|------|----------|------------|------------|"
            ])
            
            for strategy, metrics in risk_results["risk_adjusted_returns"].items():
                info_ratio = metrics.get("information_ratio", 0)
                calmar_ratio = metrics.get("calmar_ratio", 0)
                gain_loss_ratio = metrics.get("gain_loss_ratio", 0)
                
                content.append(f"| {strategy} | {info_ratio:.2f} | {calmar_ratio:.2f} | {gain_loss_ratio:.2f} |")
            
            content.extend(["", ""])
        
        content.append("---")
        content.append("")
        
        return content
    
    def _generate_contribution_section(self, contribution_results: Dict[str, Any]) -> List[str]:
        """生成贡献分析部分"""
        content = [
            "## 收益贡献分析",
            ""
        ]
        
        # 策略贡献
        if "strategy_contributions" in contribution_results:
            strategy_contrib = contribution_results["strategy_contributions"]

            content.extend([
                "### 策略收益贡献",
                "",
                "| 策略类别 | 收益贡献 | 贡献百分比(%) | 盈亏性质 |",
                "|---------|----------|--------------|----------|"
            ])

            # 获取策略贡献数据
            strategy_contribs = strategy_contrib["strategy_contributions"]

            for strategy, data in strategy_contribs.items():
                pnl = data["pnl"]
                percentage = data["percentage"]
                nature = "盈利" if data["is_profit"] else "亏损"
                
                content.append(f"| {strategy} | {pnl:.0f} | {percentage:.2f} | {nature} |")
            
            content.extend([
                "",
                f"**总收益：** {strategy_contrib.get('total_pnl', 0):.0f}",
                f"**总盈利：** {strategy_contrib.get('total_gain', 0):.0f}",
                f"**总亏损：** {strategy_contrib.get('total_loss', 0):.0f}",
                f"**计算方法：** {strategy_contrib.get('calculation_method', '')}",
                ""
            ])
        
        # 贡献摘要
        if "contribution_summary" in contribution_results:
            contrib_summary = contribution_results["contribution_summary"]
            
            content.extend([
                "### 贡献分析摘要",
                "",
                f"**贡献集中度：** {contrib_summary.get('contribution_concentration', 0):.1f}%",
                f"**分散化得分：** {contrib_summary.get('diversification_score', 0):.1f}",
                ""
            ])
            
            # 前三大贡献者
            top_contributors = contrib_summary.get("top_contributors", [])
            if top_contributors:
                content.extend(["**前三大贡献者：**", ""])
                for i, contrib in enumerate(top_contributors, 1):
                    content.append(f"{i}. {contrib['strategy']}: {contrib['pnl']:.0f} ({contrib['percentage']:.2f}%)")
                content.append("")
        
        content.append("---")
        content.append("")
        
        return content
    
    def _generate_charts_section(self, chart_files: List[str]) -> List[str]:
        """生成图表展示部分"""
        content = [
            "## 图表展示",
            ""
        ]
        
        chart_descriptions = {
            "cumulative_returns": "累积收益曲线",
            "waterfall": "收益贡献瀑布图",
            "risk_return_scatter": "风险收益散点图",
            "correlation_heatmap": "策略相关性热力图",
            "rolling_metrics": "滚动指标图",
            "monthly_returns_heatmap": "月度收益热力图"
        }
        
        for chart_file in chart_files:
            chart_path = Path(chart_file)
            chart_name = chart_path.stem
            
            # 尝试匹配图表类型
            chart_title = "图表"
            for key, title in chart_descriptions.items():
                if key in chart_name:
                    chart_title = title
                    break
            
            content.extend([
                f"### {chart_title}",
                "",
                f"![{chart_title}]({chart_file})",
                ""
            ])
        
        content.append("---")
        content.append("")
        
        return content
    
    def _generate_recommendations_section(self, performance_results: Dict[str, Any], 
                                        risk_results: Dict[str, Any], 
                                        contribution_results: Dict[str, Any]) -> List[str]:
        """生成策略建议部分"""
        content = [
            "## 策略建议",
            ""
        ]
        
        # 资金分配建议
        content.extend([
            "### 资金分配建议",
            ""
        ])
        
        if "strategy_category" in performance_results:
            for strategy, data in performance_results["strategy_category"].items():
                sharpe_ratio = data.get("sharpe_ratio", 0)
                
                if sharpe_ratio > 1.0:
                    content.append(f"- **{strategy}策略：** 表现优异（夏普比率{sharpe_ratio:.2f}），建议增加资金配置")
                elif sharpe_ratio < 0:
                    content.append(f"- **{strategy}策略：** 表现不佳（夏普比率{sharpe_ratio:.2f}），建议减少资金配置")
                else:
                    content.append(f"- **{strategy}策略：** 表现一般（夏普比率{sharpe_ratio:.2f}），维持当前配置")
            
            content.append("")
        
        # 风险管理建议
        content.extend([
            "### 风险管理建议",
            ""
        ])
        
        if "risk_summary" in risk_results:
            risk_summary = risk_results["risk_summary"]
            
            if risk_summary.get("overall_risk_level") == "High":
                content.append("- **整体风险较高：** 建议加强风险控制措施，降低仓位或增加对冲")
            
            for risk in risk_summary.get("key_risks", []):
                content.append(f"- **风险提示：** {risk}")
            
            content.append("")
        
        # 组合优化建议
        content.extend([
            "### 组合优化建议",
            ""
        ])
        
        if "contribution_summary" in contribution_results:
            contrib_summary = contribution_results["contribution_summary"]
            
            concentration = contrib_summary.get("contribution_concentration", 0)
            if concentration > 50:
                content.append(f"- **降低集中度：** 当前收益过于集中（{concentration:.1f}%），建议优化策略组合")
            
            diversification = contrib_summary.get("diversification_score", 0)
            if diversification < 50:
                content.append(f"- **提高分散化：** 当前分散化程度较低（{diversification:.1f}），建议增加策略多样性")
            
            content.append("")
        
        content.append("---")
        content.append("")
        
        return content
    
    def _generate_appendix(self) -> List[str]:
        """生成附录"""
        return [
            "## 附录",
            "",
            "### 指标说明",
            "",
            "- **夏普比率：** 衡量风险调整后收益的指标，数值越高越好",
            "- **索提诺比率：** 类似夏普比率，但只考虑下行风险",
            "- **最大回撤：** 从峰值到谷值的最大跌幅",
            "- **VaR：** 在给定置信水平下的最大可能损失",
            "- **ES：** 超过VaR的平均损失",
            "",
            "### 计算方法",
            "",
            "- **年化收益率：** 日收益率均值 × 252",
            "- **年化波动率：** 日收益率标准差 × √252",
            "- **收益贡献百分比：** 根据配置的计算方法确定",
            "",
            f"**报告生成时间：** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            ""
        ]

    def _generate_position_section(self, position_results: Dict[str, Any]) -> List[str]:
        """生成持仓分析部分"""
        section = [
            "## 持仓分析",
            "",
            "### 当前持仓概览",
            ""
        ]

        # 持仓摘要
        if "summary" in position_results:
            summary = position_results["summary"]
            section.extend([
                f"- **总持仓金额：** {summary.get('total_position', 0):,.2f} 万元",
                f"- **策略数量：** {summary.get('strategy_count', 0)}",
                f"- **行业数量：** {summary.get('industry_count', 0)}",
                f"- **最大策略：** {summary.get('max_strategy', {}).get('name', '')} ({summary.get('max_strategy', {}).get('position', 0):,.2f} 万元)",
                f"- **最大行业：** {summary.get('max_industry', {}).get('name', '')} ({summary.get('max_industry', {}).get('position', 0):,.2f} 万元)",
                ""
            ])

        # 持仓明细表
        if "position_report" in position_results and position_results["position_report"]:
            section.extend([
                "### 持仓明细",
                "",
                "| 策略名称 | 行业 | 净持仓 | 持仓市值(万元) | 持仓方向 | 多头金额 | 空头金额 |",
                "|---------|------|--------|---------------|----------|----------|----------|"
            ])

            for position in position_results["position_report"]:
                section.append(
                    f"| {position.get('strategy_name', '')} | "
                    f"{position.get('industry', '')} | "
                    f"{position.get('net_position', 0):,.2f} | "
                    f"{position.get('position_value', 0):,.2f} | "
                    f"{position.get('position_direction', '')} | "
                    f"{position.get('long_amount', 0):,.2f} | "
                    f"{position.get('short_amount', 0):,.2f} |"
                )

            section.append("")

        # 集中度分析
        if "concentration_analysis" in position_results:
            concentration = position_results["concentration_analysis"]
            section.extend([
                "### 集中度分析",
                "",
                f"- **集中度水平：** {concentration.get('concentration_level', '未知')}",
                f"- **策略HHI指数：** {concentration.get('strategy_hhi', 0):.4f}",
                f"- **行业HHI指数：** {concentration.get('industry_hhi', 0):.4f}",
                ""
            ])

            # 策略集中度
            if "strategy_concentration" in concentration:
                section.extend([
                    "#### 策略集中度分布",
                    "",
                    "| 策略 | 集中度(%) |",
                    "|------|-----------|"
                ])

                for strategy, conc in concentration["strategy_concentration"].items():
                    section.append(f"| {strategy} | {conc * 100:.2f}% |")

                section.append("")

            # 行业集中度
            if "industry_concentration" in concentration:
                section.extend([
                    "#### 行业集中度分布",
                    "",
                    "| 行业 | 集中度(%) |",
                    "|------|-----------|"
                ])

                for industry, conc in concentration["industry_concentration"].items():
                    section.append(f"| {industry} | {conc * 100:.2f}% |")

                section.append("")

        # 多空轧差分析
        if "netting_analysis" in position_results and position_results["netting_analysis"].get("enabled", False):
            netting = position_results["netting_analysis"]
            section.extend([
                "### 多空轧差分析",
                "",
                f"- **轧差效率：** {netting.get('summary', {}).get('netting_efficiency', '未知')}",
                f"- **总净持仓：** {netting.get('summary', {}).get('total_net_position', 0):,.2f} 万元",
                f"- **总毛持仓：** {netting.get('summary', {}).get('total_gross_position', 0):,.2f} 万元",
                f"- **整体轧差比率：** {netting.get('summary', {}).get('overall_netting_ratio', 0):.2%}",
                ""
            ])

        return section

    def _generate_dashboard_section(self, dashboard_files: Dict[str, str]) -> List[str]:
        """生成仪表板部分"""
        section = [
            "## 分析仪表板",
            "",
            "本次分析生成了综合仪表板，提供多维度的可视化分析：",
            ""
        ]

        if "static_dashboard" in dashboard_files:
            section.extend([
                f"### 静态仪表板",
                "",
                f"静态仪表板文件：`{dashboard_files['static_dashboard']}`",
                "",
                "静态仪表板包含以下内容：",
                "- 今日PnL摘要",
                "- 策略表现对比",
                "- 收益分布分析",
                "- 风险指标概览",
                "- 行业贡献分析",
                "- 持仓分布情况",
                ""
            ])

        if "interactive_dashboard" in dashboard_files:
            section.extend([
                f"### 交互式仪表板",
                "",
                f"交互式仪表板文件：`{dashboard_files['interactive_dashboard']}`",
                "",
                "交互式仪表板特点：",
                "- 支持鼠标悬停查看详细数据",
                "- 可缩放和平移图表",
                "- 动态数据筛选",
                "- 多图表联动分析",
                ""
            ])

        section.extend([
            "### 使用建议",
            "",
            "1. **静态仪表板**：适合打印和报告展示，提供清晰的概览信息",
            "2. **交互式仪表板**：适合深入分析，支持动态探索数据",
            "3. **定期更新**：建议每日更新仪表板以反映最新的市场表现",
            ""
        ])

        return section

    def _generate_drawdown_section(self, drawdown_analysis: Dict[str, Any]) -> List[str]:
        """生成回撤区间分析部分"""
        section = [
            "## 回撤区间分析",
            "",
            "### 回撤分析概览",
            ""
        ]

        # 汇总统计
        summary_stats = drawdown_analysis.get("summary_statistics", {})
        if summary_stats:
            section.extend([
                f"- **总回撤期间数：** {summary_stats.get('total_drawdown_periods', 0)}个",
                f"- **平均回撤幅度：** {summary_stats.get('average_drawdown_magnitude', 0) * 100:.2f}%",
                f"- **平均持续天数：** {summary_stats.get('average_drawdown_duration', 0):.0f}天",
                f"- **平均恢复时间：** {summary_stats.get('average_recovery_time', 0):.0f}天",
                f"- **恢复率：** {summary_stats.get('recovery_rate', 0) * 100:.1f}%",
                ""
            ])

            # 最严重回撤
            max_drawdown_period = summary_stats.get("max_drawdown_period")
            if max_drawdown_period:
                section.extend([
                    "### 最严重回撤期间",
                    "",
                    f"- **开始日期：** {max_drawdown_period.start_date.strftime('%Y-%m-%d')}",
                    f"- **结束日期：** {max_drawdown_period.end_date.strftime('%Y-%m-%d')}",
                    f"- **持续天数：** {max_drawdown_period.duration_days}天",
                    f"- **最大回撤幅度：** {max_drawdown_period.max_drawdown_percentage * 100:.2f}%",
                    f"- **策略分类：** {max_drawdown_period.strategy_category}",
                    f"- **是否已恢复：** {'是' if max_drawdown_period.is_recovered else '否'}",
                    ""
                ])

                if max_drawdown_period.recovery_date:
                    section.append(f"- **恢复日期：** {max_drawdown_period.recovery_date.strftime('%Y-%m-%d')}")
                    section.append(f"- **恢复用时：** {max_drawdown_period.recovery_days}天")
                    section.append("")

            # 最长回撤
            longest_drawdown_period = summary_stats.get("longest_drawdown_period")
            if longest_drawdown_period and longest_drawdown_period != max_drawdown_period:
                section.extend([
                    "### 最长回撤期间",
                    "",
                    f"- **开始日期：** {longest_drawdown_period.start_date.strftime('%Y-%m-%d')}",
                    f"- **结束日期：** {longest_drawdown_period.end_date.strftime('%Y-%m-%d')}",
                    f"- **持续天数：** {longest_drawdown_period.duration_days}天",
                    f"- **回撤幅度：** {longest_drawdown_period.max_drawdown_percentage * 100:.2f}%",
                    f"- **策略分类：** {longest_drawdown_period.strategy_category}",
                    ""
                ])

        # 获取格式化的回撤期间数据
        from analysis.drawdown_analyzer import DrawdownAnalyzer

        # 创建临时分析器实例来格式化数据
        temp_config = type('Config', (), {
            'analysis': type('Analysis', (), {
                'drawdown_analysis': type('DrawdownAnalysis', (), {
                    'output': type('Output', (), {
                        'sort_by': 'magnitude',
                        'max_drawdowns_display': 10
                    })()
                })()
            })()
        })()

        temp_analyzer = DrawdownAnalyzer(temp_config)
        formatted_periods = temp_analyzer.format_drawdown_periods_for_output(drawdown_analysis)

        if formatted_periods:
            section.extend([
                "### 主要回撤期间详情",
                "",
                "| 序号 | 时间维度 | 开始日期 | 结束日期 | 持续天数 | 最大回撤(%) | 恢复日期 | 恢复天数 | 策略分类 |",
                "|------|----------|----------|----------|----------|-------------|----------|----------|----------|"
            ])

            for period in formatted_periods[:10]:  # 只显示前10个
                section.append(
                    f"| {period.get('回撤序号', '')} | "
                    f"{period.get('时间维度', '')} | "
                    f"{period.get('开始日期', '')} | "
                    f"{period.get('结束日期', '')} | "
                    f"{period.get('持续天数', '')} | "
                    f"{period.get('最大回撤百分比', '')} | "
                    f"{period.get('恢复日期', '')} | "
                    f"{period.get('恢复天数', '')} | "
                    f"{period.get('策略分类', '')} |"
                )

            section.append("")

        # 各策略回撤统计
        by_strategy = summary_stats.get("by_strategy", {})
        if by_strategy:
            section.extend([
                "### 各策略回撤统计",
                "",
                "| 策略 | 回撤期间数 | 平均回撤幅度(%) | 平均持续天数 | 恢复率(%) |",
                "|------|------------|-----------------|--------------|-----------|"
            ])

            for strategy, stats in by_strategy.items():
                section.append(
                    f"| {strategy} | "
                    f"{stats.get('total_drawdown_periods', 0)} | "
                    f"{stats.get('average_drawdown_magnitude', 0) * 100:.2f} | "
                    f"{stats.get('average_drawdown_duration', 0):.0f} | "
                    f"{stats.get('recovery_rate', 0) * 100:.1f} |"
                )

            section.append("")

        # 时间维度分析
        time_dimension_analysis = drawdown_analysis.get("time_dimension_analysis", {})
        if time_dimension_analysis:
            section.extend([
                "### 时间维度回撤分析",
                ""
            ])

            for time_dim, time_data in time_dimension_analysis.items():
                periods = time_data.get("drawdown_periods", [])
                if periods:
                    section.extend([
                        f"#### {time_dim.title()}回撤分析",
                        "",
                        f"- **回撤期间数：** {len(periods)}个",
                        f"- **平均回撤幅度：** {np.mean([p.max_drawdown_percentage for p in periods]) * 100:.2f}%",
                        f"- **平均持续时间：** {np.mean([p.duration_days for p in periods]):.0f}天",
                        ""
                    ])

        # 回撤分析结论
        section.extend([
            "### 回撤分析结论",
            ""
        ])

        if summary_stats:
            avg_magnitude = summary_stats.get('average_drawdown_magnitude', 0) * 100
            recovery_rate = summary_stats.get('recovery_rate', 0) * 100

            if avg_magnitude > 10:
                section.append("- **风险提示：** 平均回撤幅度较大，建议加强风险控制")
            elif avg_magnitude < 3:
                section.append("- **风险评估：** 平均回撤幅度较小，风险控制良好")
            else:
                section.append("- **风险评估：** 平均回撤幅度适中，风险可控")

            if recovery_rate > 80:
                section.append("- **恢复能力：** 回撤恢复率较高，策略韧性良好")
            elif recovery_rate < 50:
                section.append("- **恢复能力：** 回撤恢复率较低，需关注策略持续性")
            else:
                section.append("- **恢复能力：** 回撤恢复率一般，建议优化策略")

            section.append("")

        section.extend([
            "---",
            ""
        ])

        return section
