"""
CTA策略分析系统仪表板生成器
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
import seaborn as sns
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime
from pathlib import Path
import plotly.graph_objects as go
import plotly.subplots as sp
from plotly.offline import plot
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class DashboardGenerator:
    """仪表板生成器"""
    
    def __init__(self, config):
        """初始化仪表板生成器"""
        self.config = config
        self.dashboard_config = config.dashboard
        self.colors = config.visualization.colors
        
        # 设置matplotlib样式
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 创建输出目录
        output_dir_name = self.dashboard_config.output.get('output_dir', 'dashboard') if isinstance(self.dashboard_config.output, dict) else 'dashboard'
        self.output_dir = Path(config.storage.base_path) / output_dir_name
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def generate_comprehensive_dashboard(self, 
                                       performance_results: Dict[str, Any],
                                       risk_results: Dict[str, Any],
                                       contribution_results: Dict[str, Any],
                                       position_results: Dict[str, Any] = None) -> Dict[str, str]:
        """生成综合仪表板"""
        logger.info("Generating comprehensive dashboard...")
        
        if not self.dashboard_config.enabled:
            logger.info("Dashboard generation is disabled")
            return {}
        
        # 生成静态仪表板
        static_file = self._generate_static_dashboard(
            performance_results, risk_results, contribution_results, position_results
        )
        
        # 生成交互式仪表板
        try:
            interactive_file = self._generate_interactive_dashboard(
                performance_results, risk_results, contribution_results, position_results
            )
        except Exception as e:
            logger.warning(f"Failed to generate interactive dashboard: {e}")
            interactive_file = None
        
        return {
            "static_dashboard": static_file,
            "interactive_dashboard": interactive_file
        }
    
    def _generate_static_dashboard(self, 
                                 performance_results: Dict[str, Any],
                                 risk_results: Dict[str, Any],
                                 contribution_results: Dict[str, Any],
                                 position_results: Dict[str, Any] = None) -> str:
        """生成静态PNG仪表板"""
        logger.info("Generating static dashboard...")
        
        # 设置图表参数
        layout_config = self.dashboard_config.layout if isinstance(self.dashboard_config.layout, dict) else {}
        fig_width, fig_height = layout_config.get('figure_size', [20, 15])
        rows, cols = layout_config.get('grid_size', [3, 4])
        
        # 创建主图表
        fig = plt.figure(figsize=(fig_width, fig_height))
        spacing = layout_config.get('spacing', 0.3)
        gs = gridspec.GridSpec(rows, cols, hspace=spacing, wspace=spacing)
        
        # 获取时间窗口数据
        time_window_data = performance_results.get("time_window_performance", {})
        
        # 1. 每日表现摘要 (高优先级) - 增强版本
        ax1 = fig.add_subplot(gs[0, 0:2])
        self._plot_enhanced_daily_summary(ax1, time_window_data, performance_results, risk_results)
        
        # 2. 策略表现对比 (高优先级)
        ax2 = fig.add_subplot(gs[0, 2:4])
        self._plot_strategy_performance(ax2, performance_results.get("strategy_category", {}))
        
        # 3. 收益分布 (高优先级)
        ax3 = fig.add_subplot(gs[1, 0:2])
        self._plot_return_distribution(ax3, performance_results)
        
        # 4. 风险指标雷达图 (中优先级)
        ax4 = fig.add_subplot(gs[1, 2:4])
        self._plot_risk_radar(ax4, risk_results)
        
        # 5. 行业贡献分析 (中优先级)
        ax5 = fig.add_subplot(gs[2, 0:2])
        self._plot_industry_contribution(ax5, contribution_results.get("industry_contributions", {}))
        
        # 6. 持仓分析 (如果有数据)
        ax6 = fig.add_subplot(gs[2, 2:4])
        if position_results:
            self._plot_position_analysis(ax6, position_results)
        else:
            self._plot_correlation_heatmap(ax6, performance_results)
        
        # 设置整体标题
        fig.suptitle(f'CTA策略分析仪表板 - {datetime.now().strftime("%Y-%m-%d %H:%M")}', 
                    fontsize=16, fontweight='bold')
        
        # 保存静态图片
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        static_format = self.dashboard_config.output.get('static_format', 'png') if isinstance(self.dashboard_config.output, dict) else 'png'
        static_dpi = self.dashboard_config.output.get('static_dpi', 300) if isinstance(self.dashboard_config.output, dict) else 300
        static_filename = f"cta_dashboard_{timestamp}.{static_format}"
        static_filepath = self.output_dir / static_filename

        plt.savefig(static_filepath,
                   dpi=static_dpi,
                   bbox_inches='tight',
                   facecolor='white')
        plt.close()
        
        logger.info(f"Static dashboard saved: {static_filepath}")
        return str(static_filepath)
    
    def _plot_daily_pnl_summary(self, ax, time_window_data: Dict):
        """绘制今日PnL摘要"""
        if not time_window_data:
            ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('今日PnL摘要')
            return
        
        # 获取不同时间窗口的PnL
        periods = ['daily', 'weekly', 'monthly', 'yearly']
        period_names = ['今日', '本周', '本月', '今年']
        pnl_values = []
        
        for period in periods:
            if period in time_window_data:
                pnl_val = time_window_data[period].get('total_pnl', 0)
                try:
                    pnl_float = float(pnl_val) if pnl_val is not None else 0.0
                    if not (pd.isna(pnl_float) or np.isinf(pnl_float)):
                        pnl_values.append(pnl_float)
                    else:
                        pnl_values.append(0.0)
                except (ValueError, TypeError):
                    pnl_values.append(0.0)
            else:
                pnl_values.append(0.0)
        
        # 创建条形图
        colors = [self.colors['profit'] if pnl >= 0 else self.colors['loss'] for pnl in pnl_values]
        bars = ax.bar(period_names, pnl_values, color=colors, alpha=0.7)
        
        # 添加数值标签
        for bar, value in zip(bars, pnl_values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{float(value):.0f}', ha='center', va='bottom' if height >= 0 else 'top')
        
        ax.set_title('不同时间窗口PnL表现', fontweight='bold')
        ax.set_ylabel('PnL (元)')
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)

    def _plot_enhanced_daily_summary(self, ax, time_window_data: Dict, performance_results: Dict, risk_results: Dict):
        """绘制增强版每日表现摘要"""
        # 创建子图布局
        ax.clear()

        # 获取今日数据
        daily_data = time_window_data.get('daily', {})
        today_pnl = daily_data.get('total_pnl', 0)

        # 获取策略表现数据
        strategy_data = performance_results.get("strategy_category", {})

        # 创建摘要信息
        summary_text = f"今日PnL: {today_pnl:,.0f}元\n"

        if strategy_data:
            # 找出表现最好和最差的策略
            strategy_pnls = {}
            for strategy, data in strategy_data.items():
                daily_pnl_series = data.get("daily_pnl", pd.Series())
                if len(daily_pnl_series) > 0:
                    strategy_pnls[strategy] = daily_pnl_series.iloc[-1]  # 最后一天的PnL

            if strategy_pnls:
                best_strategy = max(strategy_pnls, key=strategy_pnls.get)
                worst_strategy = min(strategy_pnls, key=strategy_pnls.get)

                summary_text += f"最佳策略: {best_strategy} ({strategy_pnls[best_strategy]:,.0f}元)\n"
                summary_text += f"最差策略: {worst_strategy} ({strategy_pnls[worst_strategy]:,.0f}元)\n"

        # 添加风险警报
        if "risk_validation" in risk_results:
            validation_data = risk_results["risk_validation"]
            warnings = validation_data.get("validation_summary", {}).get("warnings", [])
            errors = validation_data.get("validation_summary", {}).get("errors", [])

            if errors:
                summary_text += f"⚠️ 风险错误: {len(errors)}个\n"
            elif warnings:
                summary_text += f"⚠️ 风险警告: {len(warnings)}个\n"
            else:
                summary_text += "✅ 风险指标正常\n"

        # 显示摘要文本
        ax.text(0.05, 0.95, summary_text, transform=ax.transAxes, fontsize=12,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        # 绘制时间窗口PnL条形图（右侧）
        periods = ['daily', 'weekly', 'monthly', 'yearly']
        period_names = ['今日', '本周', '本月', '今年']
        pnl_values = []

        for period in periods:
            if period in time_window_data:
                pnl_val = time_window_data[period].get('total_pnl', 0)
                try:
                    pnl_float = float(pnl_val) if pnl_val is not None else 0.0
                    if not (pd.isna(pnl_float) or np.isinf(pnl_float)):
                        pnl_values.append(pnl_float)
                    else:
                        pnl_values.append(0.0)
                except (ValueError, TypeError):
                    pnl_values.append(0.0)
            else:
                pnl_values.append(0.0)

        # 在右侧绘制条形图
        x_pos = [0.6, 0.7, 0.8, 0.9]
        colors = [self.colors['profit'] if pnl >= 0 else self.colors['loss'] for pnl in pnl_values]

        for i, (pos, pnl, color, name) in enumerate(zip(x_pos, pnl_values, colors, period_names)):
            height = abs(pnl) / max(abs(max(pnl_values)), abs(min(pnl_values)), 1) * 0.3
            if pnl >= 0:
                rect = plt.Rectangle((pos, 0.5), 0.08, height, facecolor=color, alpha=0.7)
            else:
                rect = plt.Rectangle((pos, 0.5 - height), 0.08, height, facecolor=color, alpha=0.7)
            ax.add_patch(rect)

            # 添加标签
            ax.text(pos + 0.04, 0.4, name, ha='center', va='top', fontsize=8, rotation=45)
            ax.text(pos + 0.04, 0.5 + height/2 if pnl >= 0 else 0.5 - height/2,
                   f'{pnl:,.0f}', ha='center', va='center', fontsize=8)

        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_title('每日表现摘要', fontweight='bold', fontsize=14)
        ax.axis('off')
    
    def _plot_strategy_performance(self, ax, strategy_data: Dict):
        """绘制策略表现对比"""
        if not strategy_data:
            ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('策略表现对比')
            return
        
        strategies = list(strategy_data.keys())
        total_pnls = []
        sharpe_ratios = []

        for s in strategies:
            # 验证PnL数据
            pnl_val = strategy_data[s].get('total_pnl', 0)
            try:
                pnl_float = float(pnl_val) if pnl_val is not None else 0.0
                if not (pd.isna(pnl_float) or np.isinf(pnl_float)):
                    total_pnls.append(pnl_float)
                else:
                    total_pnls.append(0.0)
            except (ValueError, TypeError):
                total_pnls.append(0.0)

            # 验证夏普比率数据
            sharpe_val = strategy_data[s].get('sharpe_ratio', 0)
            try:
                sharpe_float = float(sharpe_val) if sharpe_val is not None else 0.0
                if not (pd.isna(sharpe_float) or np.isinf(sharpe_float)):
                    sharpe_ratios.append(sharpe_float)
                else:
                    sharpe_ratios.append(0.0)
            except (ValueError, TypeError):
                sharpe_ratios.append(0.0)

        # 创建散点图
        colors = [self.colors['profit'] if pnl >= 0 else self.colors['loss'] for pnl in total_pnls]
        scatter = ax.scatter(total_pnls, sharpe_ratios, c=colors, s=100, alpha=0.7)

        # 添加策略标签
        for i, strategy in enumerate(strategies):
            ax.annotate(str(strategy), (total_pnls[i], sharpe_ratios[i]),
                       xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        ax.set_xlabel('总PnL (元)')
        ax.set_ylabel('夏普比率')
        ax.set_title('策略表现：收益 vs 风险调整收益', fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax.axvline(x=0, color='black', linestyle='--', alpha=0.5)
    
    def _plot_return_distribution(self, ax, performance_results: Dict):
        """绘制收益分布"""
        # 尝试获取收益数据
        strategy_data = performance_results.get("strategy_category", {})
        if not strategy_data:
            ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('收益分布')
            return
        
        all_returns = []
        for strategy, data in strategy_data.items():
            if 'daily_pnl' in data and hasattr(data['daily_pnl'], '__iter__'):
                all_returns.extend(data['daily_pnl'].tolist())
        
        if not all_returns:
            ax.text(0.5, 0.5, '暂无收益数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('收益分布')
            return
        
        # 绘制直方图
        ax.hist(all_returns, bins=30, alpha=0.7, color=self.colors['neutral'], edgecolor='black')
        
        # 添加统计信息
        mean_return = np.mean(all_returns)
        std_return = np.std(all_returns)

        # 验证统计值
        try:
            if not (pd.isna(mean_return) or np.isinf(mean_return)):
                ax.axvline(mean_return, color=self.colors['profit'], linestyle='--',
                          label=f'均值: {float(mean_return):.2f}')
            if not (pd.isna(std_return) or np.isinf(std_return)):
                ax.axvline(mean_return + std_return, color=self.colors['loss'], linestyle=':',
                          label=f'+1σ: {float(mean_return + std_return):.2f}')
                ax.axvline(mean_return - std_return, color=self.colors['loss'], linestyle=':',
                          label=f'-1σ: {float(mean_return - std_return):.2f}')
        except (ValueError, TypeError):
            pass  # 跳过无效的统计值
        
        ax.set_xlabel('日收益 (元)')
        ax.set_ylabel('频次')
        ax.set_title('日收益分布', fontweight='bold')
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)
    
    def _plot_risk_radar(self, ax, risk_results: Dict):
        """绘制风险指标雷达图"""
        # 简化的风险指标显示
        if not risk_results:
            ax.text(0.5, 0.5, '暂无风险数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('风险指标')
            return
        
        # 获取风险摘要
        risk_summary = risk_results.get('risk_summary', {})
        
        metrics = ['最大回撤', 'VaR(95%)', '波动率', '夏普比率']
        values = [
            abs(risk_summary.get('max_drawdown', 0)) * 100,  # 转换为百分比
            abs(risk_summary.get('var_95', 0)) * 100,
            risk_summary.get('volatility', 0) * 100,
            max(0, risk_summary.get('sharpe_ratio', 0)) * 10  # 放大显示
        ]
        
        # 创建简单的条形图代替雷达图
        colors = [self.colors['loss'], self.colors['loss'], self.colors['neutral'], self.colors['profit']]
        bars = ax.barh(metrics, values, color=colors, alpha=0.7)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            width = bar.get_width()
            ax.text(width, bar.get_y() + bar.get_height()/2,
                   f'{float(value):.2f}', ha='left', va='center')
        
        ax.set_xlabel('风险指标值')
        ax.set_title('关键风险指标', fontweight='bold')
        ax.grid(True, alpha=0.3)
    
    def _plot_industry_contribution(self, ax, industry_data: Dict):
        """绘制行业贡献分析"""
        if not industry_data:
            ax.text(0.5, 0.5, '暂无行业数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('行业贡献分析')
            return
        
        industries_raw = list(industry_data.keys())
        # 确保行业名称是字符串
        industries = []
        contributions = []

        for ind in industries_raw:
            # 安全地转换行业名称为字符串
            try:
                industry_name = str(ind) if ind is not None else 'Unknown'
            except (ValueError, TypeError):
                industry_name = 'Unknown'
            industries.append(industry_name)

            contrib_val = industry_data[ind].get('total_pnl', 0)
            try:
                contrib_float = float(contrib_val) if contrib_val is not None else 0.0
                if not (pd.isna(contrib_float) or np.isinf(contrib_float)):
                    contributions.append(contrib_float)
                else:
                    contributions.append(0.0)
            except (ValueError, TypeError):
                contributions.append(0.0)

        # 创建水平条形图
        colors = [self.colors['profit'] if contrib >= 0 else self.colors['loss']
                 for contrib in contributions]
        bars = ax.barh(industries, contributions, color=colors, alpha=0.7)
        
        # 添加数值标签
        for bar, value in zip(bars, contributions):
            width = bar.get_width()
            ax.text(width, bar.get_y() + bar.get_height()/2,
                   f'{float(value):.0f}', ha='left' if width >= 0 else 'right', va='center')
        
        ax.set_xlabel('贡献PnL (元)')
        ax.set_title('行业贡献分析', fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)
    
    def _plot_position_analysis(self, ax, position_results: Dict):
        """绘制持仓分析"""
        if not position_results:
            ax.text(0.5, 0.5, '暂无持仓数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('持仓分析')
            return
        
        # 获取策略持仓数据
        strategy_positions = position_results.get('strategy_positions', {})
        if not strategy_positions:
            ax.text(0.5, 0.5, '暂无策略持仓数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('持仓分析')
            return
        
        strategies = list(strategy_positions.keys())
        positions = []

        for s in strategies:
            pos_value = strategy_positions[s].get('total_strategy_position', 0)
            try:
                pos_float = float(pos_value) if pos_value is not None else 0.0
                if not (pd.isna(pos_float) or np.isinf(pos_float)):
                    positions.append(pos_float)
                else:
                    positions.append(0.0)
            except (ValueError, TypeError):
                positions.append(0.0)

        # 过滤掉零值或无效值
        valid_data = [(s, p) for s, p in zip(strategies, positions) if p > 0]
        if not valid_data:
            ax.text(0.5, 0.5, '暂无有效持仓数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('持仓分析')
            return

        strategies, positions = zip(*valid_data)

        # 创建饼图
        colors = plt.cm.Set3(np.linspace(0, 1, len(strategies)))
        # 确保标签是字符串
        strategy_labels = [str(s) for s in strategies]
        # 确保positions是有效的数值
        valid_positions = [float(p) for p in positions if not (pd.isna(p) or np.isinf(p))]
        if len(valid_positions) != len(positions):
            # 如果有无效值，重新过滤
            valid_data = [(s, p) for s, p in zip(strategy_labels, positions)
                         if not (pd.isna(p) or np.isinf(p)) and p > 0]
            if valid_data:
                strategy_labels, positions = zip(*valid_data)
            else:
                ax.text(0.5, 0.5, '暂无有效持仓数据', ha='center', va='center', transform=ax.transAxes)
                ax.set_title('策略持仓分布', fontweight='bold')
                return

        # 自定义autopct函数来处理无效值
        def safe_autopct(pct):
            try:
                if pd.isna(pct) or np.isinf(pct):
                    return 'N/A'
                else:
                    return f'{float(pct):.1f}%'
            except (ValueError, TypeError):
                return 'N/A'

        wedges, texts, autotexts = ax.pie(positions, labels=strategy_labels, autopct=safe_autopct,
                                         colors=colors, startangle=90)
        
        ax.set_title('策略持仓分布', fontweight='bold')
    
    def _plot_correlation_heatmap(self, ax, performance_results: Dict):
        """绘制相关性热力图"""
        strategy_data = performance_results.get("strategy_category", {})
        if len(strategy_data) < 2:
            ax.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('策略相关性')
            return
        
        # 构建相关性矩阵
        strategies = list(strategy_data.keys())
        correlation_matrix = np.random.rand(len(strategies), len(strategies))  # 简化处理
        np.fill_diagonal(correlation_matrix, 1.0)
        
        # 绘制热力图
        im = ax.imshow(correlation_matrix, cmap='RdYlBu_r', aspect='auto', vmin=-1, vmax=1)
        
        # 设置标签
        ax.set_xticks(range(len(strategies)))
        ax.set_yticks(range(len(strategies)))
        ax.set_xticklabels(strategies, rotation=45, ha='right')
        ax.set_yticklabels(strategies)
        
        # 添加数值
        for i in range(len(strategies)):
            for j in range(len(strategies)):
                corr_val = correlation_matrix[i, j]
                try:
                    if pd.isna(corr_val) or np.isinf(corr_val):
                        text_val = 'N/A'
                    else:
                        text_val = f'{float(corr_val):.2f}'
                except (ValueError, TypeError):
                    text_val = 'N/A'
                ax.text(j, i, text_val, ha='center', va='center', fontsize=8)
        
        ax.set_title('策略相关性矩阵', fontweight='bold')
        
        # 添加颜色条
        plt.colorbar(im, ax=ax, shrink=0.8)

    def _generate_interactive_dashboard(self,
                                      performance_results: Dict[str, Any],
                                      risk_results: Dict[str, Any],
                                      contribution_results: Dict[str, Any],
                                      position_results: Dict[str, Any] = None) -> str:
        """生成交互式HTML仪表板"""
        logger.info("Generating interactive dashboard...")

        # 创建子图布局
        fig = sp.make_subplots(
            rows=3, cols=2,
            subplot_titles=('今日PnL摘要', '策略表现对比', '收益分布', '风险指标', '行业贡献', '持仓分析'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"type": "domain"}]]  # 最后一个位置用于饼图
        )

        # 1. 每日表现摘要 - 增强版本
        self._add_interactive_enhanced_daily_summary(fig, performance_results, risk_results, 1, 1)

        # 2. 策略表现对比
        self._add_interactive_strategy_performance(fig, performance_results.get("strategy_category", {}), 1, 2)

        # 3. 收益分布
        self._add_interactive_return_distribution(fig, performance_results, 2, 1)

        # 4. 风险指标
        self._add_interactive_risk_metrics(fig, risk_results, 2, 2)

        # 5. 行业贡献
        self._add_interactive_industry_contribution(fig, contribution_results.get("industry_contributions", {}), 3, 1)

        # 6. 持仓分析
        if position_results:
            self._add_interactive_position_analysis(fig, position_results, 3, 2)
        else:
            self._add_interactive_correlation_analysis(fig, performance_results, 3, 2)

        # 更新布局
        fig.update_layout(
            title=f'CTA策略分析交互式仪表板 - {datetime.now().strftime("%Y-%m-%d %H:%M")}',
            showlegend=True,
            height=1000,
            width=1400
        )

        # 保存交互式HTML
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        interactive_format = self.dashboard_config.output.get('interactive_format', 'html') if isinstance(self.dashboard_config.output, dict) else 'html'
        interactive_filename = f"cta_dashboard_{timestamp}.{interactive_format}"
        interactive_filepath = self.output_dir / interactive_filename

        plot(fig, filename=str(interactive_filepath), auto_open=False)

        logger.info(f"Interactive dashboard saved: {interactive_filepath}")
        return str(interactive_filepath)

    def _add_interactive_pnl_summary(self, fig, time_window_data: Dict, row: int, col: int):
        """添加交互式PnL摘要"""
        periods = ['daily', 'weekly', 'monthly', 'yearly']
        period_names = ['今日', '本周', '本月', '今年']
        pnl_values = []

        for period in periods:
            if period in time_window_data:
                pnl_val = time_window_data[period].get('total_pnl', 0)
                try:
                    pnl_float = float(pnl_val) if pnl_val is not None else 0.0
                    if not (pd.isna(pnl_float) or np.isinf(pnl_float)):
                        pnl_values.append(pnl_float)
                    else:
                        pnl_values.append(0.0)
                except (ValueError, TypeError):
                    pnl_values.append(0.0)
            else:
                pnl_values.append(0.0)

        colors = ['green' if pnl >= 0 else 'red' for pnl in pnl_values]

        # 确保文本标签是有效的
        text_labels = []
        for pnl in pnl_values:
            try:
                if pd.isna(pnl) or np.isinf(pnl):
                    text_labels.append('N/A')
                else:
                    text_labels.append(f'{float(pnl):.0f}')
            except (ValueError, TypeError):
                text_labels.append('N/A')

        fig.add_trace(
            go.Bar(x=period_names, y=pnl_values,
                  marker_color=colors, name='PnL',
                  text=text_labels,
                  textposition='auto'),
            row=row, col=col
        )

    def _add_interactive_enhanced_daily_summary(self, fig, performance_results: Dict, risk_results: Dict, row: int, col: int):
        """添加交互式增强版每日表现摘要"""
        # 获取时间窗口数据
        time_window_data = performance_results.get("time_window_performance", {})
        daily_data = time_window_data.get('daily', {})

        # 创建摘要表格数据
        summary_data = []

        # 今日PnL
        today_pnl = daily_data.get('total_pnl', 0)
        summary_data.append(['今日PnL', f'{today_pnl:,.0f}元'])

        # 策略表现
        strategy_data = performance_results.get("strategy_category", {})
        if strategy_data:
            strategy_pnls = {}
            for strategy, data in strategy_data.items():
                daily_pnl_series = data.get("daily_pnl", pd.Series())
                if len(daily_pnl_series) > 0:
                    strategy_pnls[strategy] = daily_pnl_series.iloc[-1]

            if strategy_pnls:
                best_strategy = max(strategy_pnls, key=strategy_pnls.get)
                worst_strategy = min(strategy_pnls, key=strategy_pnls.get)

                summary_data.append(['最佳策略', f'{best_strategy}: {strategy_pnls[best_strategy]:,.0f}元'])
                summary_data.append(['最差策略', f'{worst_strategy}: {strategy_pnls[worst_strategy]:,.0f}元'])

        # 风险状态
        if "risk_validation" in risk_results:
            validation_data = risk_results["risk_validation"]
            warnings = validation_data.get("validation_summary", {}).get("warnings", [])
            errors = validation_data.get("validation_summary", {}).get("errors", [])

            if errors:
                summary_data.append(['风险状态', f'⚠️ {len(errors)}个错误'])
            elif warnings:
                summary_data.append(['风险状态', f'⚠️ {len(warnings)}个警告'])
            else:
                summary_data.append(['风险状态', '✅ 正常'])

        # 时间窗口表现
        periods = ['weekly', 'monthly', 'yearly']
        period_names = ['本周', '本月', '今年']

        for period, name in zip(periods, period_names):
            if period in time_window_data:
                pnl_val = time_window_data[period].get('total_pnl', 0)
                summary_data.append([f'{name}PnL', f'{pnl_val:,.0f}元'])

        # 创建表格
        if summary_data:
            categories = [item[0] for item in summary_data]
            values = [item[1] for item in summary_data]

            # 使用表格形式显示
            fig.add_trace(
                go.Table(
                    header=dict(
                        values=['指标', '数值'],
                        fill_color='lightblue',
                        align='center',
                        font=dict(size=12, color='black')
                    ),
                    cells=dict(
                        values=[categories, values],
                        fill_color='white',
                        align='left',
                        font=dict(size=11)
                    )
                ),
                row=row, col=col
            )

    def _add_interactive_strategy_performance(self, fig, strategy_data: Dict, row: int, col: int):
        """添加交互式策略表现"""
        if not strategy_data:
            return

        strategies = list(strategy_data.keys())
        total_pnls = []
        sharpe_ratios = []

        for s in strategies:
            # 验证PnL数据
            pnl_val = strategy_data[s].get('total_pnl', 0)
            try:
                pnl_float = float(pnl_val) if pnl_val is not None else 0.0
                if not (pd.isna(pnl_float) or np.isinf(pnl_float)):
                    total_pnls.append(pnl_float)
                else:
                    total_pnls.append(0.0)
            except (ValueError, TypeError):
                total_pnls.append(0.0)

            # 验证夏普比率数据
            sharpe_val = strategy_data[s].get('sharpe_ratio', 0)
            try:
                sharpe_float = float(sharpe_val) if sharpe_val is not None else 0.0
                if not (pd.isna(sharpe_float) or np.isinf(sharpe_float)):
                    sharpe_ratios.append(sharpe_float)
                else:
                    sharpe_ratios.append(0.0)
            except (ValueError, TypeError):
                sharpe_ratios.append(0.0)

        colors = ['green' if pnl >= 0 else 'red' for pnl in total_pnls]

        # 安全地转换策略名称为字符串
        strategy_labels = []
        for s in strategies:
            try:
                strategy_labels.append(str(s) if s is not None else 'Unknown')
            except (ValueError, TypeError):
                strategy_labels.append('Unknown')

        fig.add_trace(
            go.Scatter(x=total_pnls, y=sharpe_ratios,
                      mode='markers+text',
                      marker=dict(color=colors, size=10),
                      text=strategy_labels,
                      textposition='top center',
                      name='策略表现'),
            row=row, col=col
        )

    def _add_interactive_return_distribution(self, fig, performance_results: Dict, row: int, col: int):
        """添加交互式收益分布"""
        strategy_data = performance_results.get("strategy_category", {})
        all_returns = []

        for strategy, data in strategy_data.items():
            if 'daily_pnl' in data and hasattr(data['daily_pnl'], '__iter__'):
                all_returns.extend(data['daily_pnl'].tolist())

        if all_returns:
            fig.add_trace(
                go.Histogram(x=all_returns, nbinsx=30, name='收益分布'),
                row=row, col=col
            )

    def _add_interactive_risk_metrics(self, fig, risk_results: Dict, row: int, col: int):
        """添加交互式风险指标"""
        risk_summary = risk_results.get('risk_summary', {})

        metrics = ['最大回撤', 'VaR(95%)', '波动率', '夏普比率']
        values = [
            abs(risk_summary.get('max_drawdown', 0)) * 100,
            abs(risk_summary.get('var_95', 0)) * 100,
            risk_summary.get('volatility', 0) * 100,
            max(0, risk_summary.get('sharpe_ratio', 0)) * 10
        ]

        fig.add_trace(
            go.Bar(y=metrics, x=values, orientation='h',
                  marker_color=['red', 'red', 'blue', 'green'],
                  name='风险指标'),
            row=row, col=col
        )

    def _add_interactive_industry_contribution(self, fig, industry_data: Dict, row: int, col: int):
        """添加交互式行业贡献"""
        if not industry_data:
            return

        industries_raw = list(industry_data.keys())
        # 确保行业名称是字符串
        industries = []
        contributions = []

        for ind in industries_raw:
            # 安全地转换行业名称为字符串
            try:
                industry_name = str(ind) if ind is not None else 'Unknown'
            except (ValueError, TypeError):
                industry_name = 'Unknown'
            industries.append(industry_name)
            contributions.append(industry_data[ind].get('total_pnl', 0))

        colors = ['green' if contrib >= 0 else 'red' for contrib in contributions]

        fig.add_trace(
            go.Bar(y=industries, x=contributions, orientation='h',
                  marker_color=colors, name='行业贡献'),
            row=row, col=col
        )

    def _add_interactive_position_analysis(self, fig, position_results: Dict, row: int, col: int):
        """添加交互式持仓分析"""
        strategy_positions = position_results.get('strategy_positions', {})
        if not strategy_positions:
            return

        strategies = list(strategy_positions.keys())
        positions = []

        for s in strategies:
            pos_value = strategy_positions[s].get('total_strategy_position', 0)
            try:
                pos_float = float(pos_value) if pos_value is not None else 0.0
                if not (pd.isna(pos_float) or np.isinf(pos_float)):
                    positions.append(pos_float)
                else:
                    positions.append(0.0)
            except (ValueError, TypeError):
                positions.append(0.0)

        # Filter out zero or invalid values
        valid_data = [(s, p) for s, p in zip(strategies, positions) if p > 0]
        if valid_data:
            strategies, positions = zip(*valid_data)
            # 安全地转换策略名称为字符串
            strategy_labels = []
            for s in strategies:
                try:
                    strategy_labels.append(str(s) if s is not None else 'Unknown')
                except (ValueError, TypeError):
                    strategy_labels.append('Unknown')

            fig.add_trace(
                go.Pie(labels=strategy_labels,
                      values=[float(p) for p in positions],
                      name='持仓分布'),
                row=row, col=col
            )

    def _add_interactive_correlation_analysis(self, fig, performance_results: Dict, row: int, col: int):
        """添加交互式相关性分析"""
        strategy_data = performance_results.get("strategy_category", {})
        if len(strategy_data) < 2:
            return

        strategies = list(strategy_data.keys())
        correlation_matrix = np.random.rand(len(strategies), len(strategies))
        np.fill_diagonal(correlation_matrix, 1.0)

        fig.add_trace(
            go.Heatmap(z=correlation_matrix,
                      x=strategies,
                      y=strategies,
                      colorscale='RdYlBu_r',
                      name='相关性'),
            row=row, col=col
        )
