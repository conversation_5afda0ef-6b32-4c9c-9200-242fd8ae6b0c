#!/usr/bin/env python3
"""
深度诊断风险贡献度一致性问题
分析GARCH模型VaR与解析边际VaR之间的差异
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from scipy import stats

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.risk_models import ParametricRiskModel
from core.portfolio_manager import PortfolioManager
from core.risk_contribution import RiskContributionCalculator
from config.config_manager import config_manager


def deep_diagnosis():
    """深度诊断风险贡献度问题"""
    print("🔬 深度诊断风险贡献度一致性问题")
    print("="*60)
    
    # 生成测试数据
    np.random.seed(42)
    n_obs = 1000
    n_assets = 4
    
    mean_returns = np.array([0.0005, 0.0003, 0.0002, 0.0001])
    volatilities = np.array([0.02, 0.015, 0.01, 0.008])
    correlation_matrix = np.array([
        [1.0, 0.3, 0.1, 0.05],
        [0.3, 1.0, 0.2, 0.1],
        [0.1, 0.2, 1.0, 0.15],
        [0.05, 0.1, 0.15, 1.0]
    ])
    
    cov_matrix = np.outer(volatilities, volatilities) * correlation_matrix
    returns = np.random.multivariate_normal(mean_returns, cov_matrix, n_obs)
    
    asset_names = [f"股票{chr(65+i)}" for i in range(n_assets)]
    dates = pd.date_range('2020-01-01', periods=n_obs, freq='D')
    returns_data = pd.DataFrame(returns, index=dates, columns=asset_names)
    
    # 创建投资组合
    weights = pd.Series([0.3, 0.25, 0.25, 0.2], index=asset_names)
    portfolio_manager = PortfolioManager(weights, returns_data)
    
    print("📊 第1步：对比不同VaR计算方法")
    print("-" * 40)
    
    # 1. GARCH模型VaR (当前系统使用的)
    portfolio_returns = portfolio_manager.calculate_portfolio_returns(returns_data)
    parametric_model = ParametricRiskModel()
    garch_var = parametric_model.calculate_var(portfolio_returns, 0.95)
    print(f"GARCH模型VaR: {garch_var:.6f}")
    
    # 2. 简单正态分布VaR (边际VaR计算基础)
    portfolio_mean = portfolio_returns.mean()
    portfolio_std = portfolio_returns.std()
    holding_period = config_manager.get_risk_calculation_config().holding_period
    alpha = 0.05
    quantile = stats.norm.ppf(alpha)
    simple_var = -portfolio_mean * holding_period - quantile * portfolio_std * np.sqrt(holding_period)
    print(f"简单正态VaR: {simple_var:.6f}")
    
    # 3. 协方差矩阵VaR (理论基础)
    cov_matrix_df = portfolio_manager.get_covariance_matrix(returns_data)
    portfolio_variance = np.dot(weights.values, np.dot(cov_matrix_df.values, weights.values))
    portfolio_volatility = np.sqrt(portfolio_variance)
    portfolio_mean_from_assets = np.dot(weights.values, returns_data.mean().values)
    
    theoretical_var = -portfolio_mean_from_assets * holding_period - quantile * portfolio_volatility * np.sqrt(holding_period)
    print(f"理论协方差VaR: {theoretical_var:.6f}")
    
    print(f"\n🔍 VaR差异分析:")
    print(f"GARCH vs 简单正态: {abs(garch_var - simple_var)/garch_var*100:.2f}%")
    print(f"GARCH vs 理论协方差: {abs(garch_var - theoretical_var)/garch_var*100:.2f}%")
    print(f"简单正态 vs 理论协方差: {abs(simple_var - theoretical_var)/simple_var*100:.2f}%")
    
    print(f"\n📊 第2步：分析边际VaR计算")
    print("-" * 40)
    
    # 计算边际VaR
    risk_contrib_calc = RiskContributionCalculator(portfolio_manager)
    marginal_vars = risk_contrib_calc._calculate_analytical_mvar(returns_data, 0.95)
    component_vars = {asset: weights[asset] * marginal_vars[asset] for asset in asset_names}
    sum_component_var = sum(component_vars.values())
    
    print(f"边际VaR计算结果:")
    for asset in asset_names:
        print(f"  {asset}: MVaR={marginal_vars[asset]:.6f}, 成分VaR={component_vars[asset]:.6f}")
    
    print(f"\n成分VaR总和: {sum_component_var:.6f}")
    print(f"理论协方差VaR: {theoretical_var:.6f}")
    print(f"差异: {abs(sum_component_var - theoretical_var):.8f}")
    
    print(f"\n📊 第3步：问题根源分析")
    print("-" * 40)
    
    # 问题在于：GARCH模型使用条件波动率，而边际VaR使用历史协方差矩阵
    garch_result = parametric_model.fit_garch_model(portfolio_returns)
    garch_volatility = garch_result['conditional_volatility']
    
    print(f"GARCH条件波动率: {garch_volatility:.6f}")
    print(f"历史组合波动率: {portfolio_volatility:.6f}")
    print(f"简单组合标准差: {portfolio_std:.6f}")
    
    volatility_ratio = garch_volatility / portfolio_volatility
    print(f"波动率比率 (GARCH/历史): {volatility_ratio:.4f}")
    
    # 调整边际VaR以匹配GARCH波动率
    print(f"\n📊 第4步：修复方案验证")
    print("-" * 40)
    
    # 方案1：使用GARCH波动率调整边际VaR
    adjusted_marginal_vars = {}
    adjusted_component_vars = {}
    
    for asset in asset_names:
        # 调整边际VaR = 原边际VaR * (GARCH波动率 / 历史波动率)
        adjusted_mvar = marginal_vars[asset] * volatility_ratio
        adjusted_comp_var = weights[asset] * adjusted_mvar
        
        adjusted_marginal_vars[asset] = adjusted_mvar
        adjusted_component_vars[asset] = adjusted_comp_var
    
    adjusted_sum = sum(adjusted_component_vars.values())
    
    print(f"调整后的成分VaR:")
    for asset in asset_names:
        print(f"  {asset}: 调整MVaR={adjusted_marginal_vars[asset]:.6f}, 调整成分VaR={adjusted_component_vars[asset]:.6f}")
    
    print(f"\n调整后成分VaR总和: {adjusted_sum:.6f}")
    print(f"GARCH模型VaR: {garch_var:.6f}")
    print(f"调整后差异: {abs(adjusted_sum - garch_var):.8f}")
    print(f"调整后相对误差: {abs(adjusted_sum - garch_var)/garch_var*100:.4f}%")
    
    # 方案2：直接使用GARCH波动率计算边际VaR
    print(f"\n方案2：直接使用GARCH波动率")
    garch_based_marginal_vars = {}
    garch_based_component_vars = {}
    
    for i, asset in enumerate(asset_names):
        # 使用GARCH波动率的边际VaR
        asset_portfolio_cov = np.dot(cov_matrix_df.iloc[i, :].values, weights.values)
        garch_mvar = -quantile * asset_portfolio_cov / garch_volatility * np.sqrt(holding_period)
        garch_comp_var = weights[asset] * garch_mvar
        
        garch_based_marginal_vars[asset] = garch_mvar
        garch_based_component_vars[asset] = garch_comp_var
    
    garch_based_sum = sum(garch_based_component_vars.values())
    
    print(f"GARCH基础的成分VaR:")
    for asset in asset_names:
        print(f"  {asset}: GARCH MVaR={garch_based_marginal_vars[asset]:.6f}, GARCH成分VaR={garch_based_component_vars[asset]:.6f}")
    
    print(f"\nGARCH基础成分VaR总和: {garch_based_sum:.6f}")
    print(f"GARCH模型VaR: {garch_var:.6f}")
    print(f"GARCH基础差异: {abs(garch_based_sum - garch_var):.8f}")
    print(f"GARCH基础相对误差: {abs(garch_based_sum - garch_var)/garch_var*100:.4f}%")
    
    print(f"\n💡 结论和建议:")
    print("="*60)
    print("问题根源：")
    print("1. GARCH模型使用条件波动率预测")
    print("2. 边际VaR计算使用历史协方差矩阵")
    print("3. 两者的波动率估计不一致")
    print()
    print("解决方案：")
    print("1. 在边际VaR计算中使用GARCH条件波动率")
    print("2. 或者在组合VaR计算中也使用历史协方差方法")
    print("3. 推荐使用方案2：统一使用GARCH波动率")
    
    return garch_volatility, portfolio_volatility, volatility_ratio


def create_fixed_marginal_var_calculator():
    """创建修复后的边际VaR计算器"""
    
    print(f"\n🔧 修复后的边际VaR计算方法:")
    print("="*60)
    
    fixed_code = '''
def _calculate_analytical_mvar_with_garch(self, returns_data: pd.DataFrame, 
                                        confidence_level: float) -> Dict[str, float]:
    """
    使用GARCH条件波动率计算边际VaR
    确保与组合VaR计算方法一致
    """
    from scipy import stats
    
    # 获取协方差矩阵
    covariance_matrix = self.portfolio_manager.get_covariance_matrix(returns_data)
    
    # 确保权重和协方差矩阵的资产匹配
    common_assets = covariance_matrix.index.intersection(self.portfolio_manager.weights.index)
    aligned_cov = covariance_matrix.loc[common_assets, common_assets]
    aligned_weights = self.portfolio_manager.weights[common_assets]
    
    # 计算组合收益率并获取GARCH条件波动率
    portfolio_returns = self.portfolio_manager.calculate_portfolio_returns(returns_data)
    garch_result = self.risk_model.fit_garch_model(portfolio_returns)
    garch_volatility = garch_result['conditional_volatility']
    
    # 计算VaR的分位数 (使用标准正态分布)
    alpha = 1 - confidence_level
    quantile = stats.norm.ppf(alpha)
    
    # 持有期调整
    holding_period_factor = np.sqrt(self.risk_config.holding_period)
    
    # 计算边际VaR: MVaR_i = -quantile * (Σ_j w_j * Cov(i,j)) / σ_p_garch * √T
    marginal_vars = {}
    
    for i, asset in enumerate(common_assets):
        # 计算资产i与组合的协方差 (权重加权)
        asset_portfolio_cov = np.dot(aligned_cov.iloc[i, :].values, aligned_weights.values)
        
        # 边际VaR (使用GARCH条件波动率)
        if garch_volatility > 0:
            marginal_var = -quantile * asset_portfolio_cov / garch_volatility * holding_period_factor
        else:
            marginal_var = 0.0
        
        marginal_vars[asset] = marginal_var
    
    # 为不在协方差矩阵中的资产设置0
    for asset in self.portfolio_manager.weights.index:
        if asset not in marginal_vars:
            marginal_vars[asset] = 0.0
    
    logger.info("Calculated GARCH-consistent analytical marginal VaR")
    return marginal_vars
    '''
    
    print(fixed_code)


def main():
    """主函数"""
    print("🔬 风险贡献度一致性问题深度诊断")
    print("="*60)
    
    # 运行深度诊断
    garch_vol, hist_vol, vol_ratio = deep_diagnosis()
    
    # 显示修复方案
    create_fixed_marginal_var_calculator()
    
    print(f"\n📋 诊断总结:")
    print(f"GARCH条件波动率: {garch_vol:.6f}")
    print(f"历史组合波动率: {hist_vol:.6f}")
    print(f"波动率比率: {vol_ratio:.4f}")
    print(f"建议: 在边际VaR计算中使用GARCH条件波动率以确保一致性")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
