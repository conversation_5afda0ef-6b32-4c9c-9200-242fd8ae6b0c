"""
历史回溯测试结果可视化脚本
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import sys

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def visualize_backtest_results(excel_file: str):
    """
    可视化回溯测试结果
    
    Args:
        excel_file: Excel文件路径
    """
    try:
        # 读取数据
        df = pd.read_excel(excel_file, sheet_name='Historical_VaR_ES')
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        
        print(f"✅ 成功加载数据: {df.shape[0]} 天")
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('投资组合风险管理历史回溯测试结果', fontsize=16, fontweight='bold')
        
        # 1. VaR时间序列
        ax1 = axes[0, 0]
        var_columns = ['parametric_var', 'historical_var', 'monte_carlo_var']
        for col in var_columns:
            if col in df.columns:
                method_name = col.replace('_var', '').replace('_', ' ').title()
                ax1.plot(df.index, df[col] * 100, label=method_name, linewidth=1.5)
        
        ax1.set_title('VaR时间序列 (99%置信水平)', fontweight='bold')
        ax1.set_ylabel('VaR (%)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. ES时间序列
        ax2 = axes[0, 1]
        es_columns = ['parametric_es', 'historical_es', 'monte_carlo_es']
        for col in es_columns:
            if col in df.columns:
                method_name = col.replace('_es', '').replace('_', ' ').title()
                ax2.plot(df.index, df[col] * 100, label=method_name, linewidth=1.5)
        
        ax2.set_title('ES时间序列 (99%置信水平)', fontweight='bold')
        ax2.set_ylabel('ES (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 投资组合波动率
        ax3 = axes[1, 0]
        ax3.plot(df.index, df['portfolio_volatility'] * 100, color='red', linewidth=1.5)
        ax3.set_title('投资组合波动率', fontweight='bold')
        ax3.set_ylabel('波动率 (%)')
        ax3.grid(True, alpha=0.3)
        
        # 4. VaR方法比较散点图
        ax4 = axes[1, 1]
        if 'parametric_var' in df.columns and 'historical_var' in df.columns:
            ax4.scatter(df['parametric_var'] * 100, df['historical_var'] * 100, 
                       alpha=0.6, s=20)
            
            # 添加对角线
            min_val = min(df['parametric_var'].min(), df['historical_var'].min()) * 100
            max_val = max(df['parametric_var'].max(), df['historical_var'].max()) * 100
            ax4.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
            
            ax4.set_xlabel('参数法 VaR (%)')
            ax4.set_ylabel('历史模拟法 VaR (%)')
            ax4.set_title('VaR方法比较', fontweight='bold')
            ax4.grid(True, alpha=0.3)
            
            # 计算相关性
            corr = df['parametric_var'].corr(df['historical_var'])
            ax4.text(0.05, 0.95, f'相关性: {corr:.3f}', 
                    transform=ax4.transAxes, fontsize=10,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图表
        output_dir = Path("results")
        output_dir.mkdir(exist_ok=True)
        chart_file = output_dir / "backtest_visualization.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存到: {chart_file}")
        
        # 显示统计摘要
        print("\n📈 统计摘要:")
        var_cols = [col for col in df.columns if 'var' in col]
        for col in var_cols:
            mean_val = df[col].mean() * 100
            std_val = df[col].std() * 100
            print(f"  {col:20}: 均值 {mean_val:5.2f}%, 标准差 {std_val:4.2f}%")
        
        return fig
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        return None


def main():
    """主函数"""
    # 查找最新的回溯测试文件
    results_dir = Path("results")
    excel_files = list(results_dir.glob("historical_var_es_backtest_*.xlsx"))
    
    if not excel_files:
        print("❌ 未找到历史回溯测试文件")
        print("请先运行: python main.py --mode backtest")
        return
    
    # 使用最新的文件
    latest_file = max(excel_files, key=lambda x: x.stat().st_mtime)
    print(f"📁 使用文件: {latest_file}")
    
    # 生成可视化
    fig = visualize_backtest_results(str(latest_file))
    
    if fig:
        print("✅ 可视化完成!")
        
        # 询问是否显示图表
        try:
            show_chart = input("\n是否显示图表? (y/n): ").lower().strip()
            if show_chart in ['y', 'yes', '是']:
                plt.show()
        except KeyboardInterrupt:
            print("\n👋 退出")


if __name__ == "__main__":
    main()
