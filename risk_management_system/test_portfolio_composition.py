#!/usr/bin/env python3
"""
多资产组合风险构成分析功能测试脚本
验证新增的权重配置管理和组合风险构成分析功能
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import json

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from main import RiskManagementSystem
from core.multi_portfolio_analyzer import MultiPortfolioAnalyzer
from core.weight_config_manager import WeightConfigManager


def test_weight_config_manager():
    """测试权重配置管理器"""
    print("🧪 测试权重配置管理器...")
    
    try:
        weight_manager = WeightConfigManager()
        
        # 1. 测试权重验证
        print("1. 测试权重验证...")
        test_weights = {"股票A": 0.3, "股票B": 0.2, "债券A": 0.3, "债券B": 0.2}
        validated_weights, is_normalized = weight_manager.validate_weights(test_weights)
        print(f"   ✅ 权重验证通过，总和: {validated_weights.sum():.3f}")
        
        # 2. 测试权重解析
        print("2. 测试权重解析...")
        weight_string = "股票A:0.3,股票B:0.2,债券A:0.3,债券B:0.2"
        parsed_weights = weight_manager.parse_weight_input(weight_string)
        print(f"   ✅ 权重解析成功: {len(parsed_weights)} 个资产")
        
        # 3. 测试预定义方案加载
        print("3. 测试预定义方案加载...")
        predefined_scenarios = weight_manager.load_predefined_scenarios()
        print(f"   ✅ 加载预定义方案: {len(predefined_scenarios)} 个")
        
        # 4. 测试随机方案生成
        print("4. 测试随机方案生成...")
        assets = ["股票A", "股票B", "债券A", "债券B", "商品A"]
        random_scenarios = weight_manager.generate_random_scenarios(assets, 3, random_seed=42)
        print(f"   ✅ 生成随机方案: {len(random_scenarios)} 个")
        
        # 5. 测试权重统计
        print("5. 测试权重统计...")
        stats = weight_manager.get_weight_statistics(validated_weights)
        print(f"   ✅ 权重统计完成，HHI: {stats['concentration_hhi']:.3f}")
        
        print("✅ 权重配置管理器测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 权重配置管理器测试失败: {e}")
        return False


def test_single_portfolio_analysis():
    """测试单个投资组合分析"""
    print("\n🧪 测试单个投资组合分析...")
    
    try:
        # 初始化分析器
        multi_analyzer = MultiPortfolioAnalyzer()
        
        # 测试权重配置
        test_weights = {"股票A": 0.3, "股票B": 0.2, "债券A": 0.3, "债券B": 0.2}
        
        # 运行分析（不保存文件）
        results = multi_analyzer.run_single_portfolio_analysis(
            weights=test_weights,
            scenario_name="Test Portfolio",
            save_results=False
        )
        
        if not results.get('success'):
            print(f"❌ 单个投资组合分析失败: {results.get('error')}")
            return False
        
        analysis_result = results['analysis_result']
        portfolio_metrics = analysis_result['portfolio_metrics']
        
        print(f"   ✅ 分析完成: {analysis_result['scenario_name']}")
        print(f"   📊 组合VaR: {portfolio_metrics['portfolio_var']*100:.2f}%")
        print(f"   📊 组合ES: {portfolio_metrics['portfolio_es']*100:.2f}%")
        print(f"   📊 有效资产数: {portfolio_metrics['effective_assets']:.1f}")
        print(f"   📊 集中度指数: {portfolio_metrics['concentration_hhi']:.3f}")
        
        print("✅ 单个投资组合分析测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 单个投资组合分析测试失败: {e}")
        return False


def test_multi_portfolio_analysis():
    """测试多投资组合对比分析"""
    print("\n🧪 测试多投资组合对比分析...")
    
    try:
        # 初始化分析器
        multi_analyzer = MultiPortfolioAnalyzer()
        
        # 创建测试方案
        test_scenarios = {
            "Conservative": {"股票A": 0.2, "股票B": 0.1, "债券A": 0.4, "债券B": 0.3},
            "Balanced": {"股票A": 0.3, "股票B": 0.2, "债券A": 0.3, "债券B": 0.2},
            "Aggressive": {"股票A": 0.4, "股票B": 0.3, "债券A": 0.2, "债券B": 0.1}
        }
        
        # 运行分析（不保存文件）
        results = multi_analyzer.run_multi_portfolio_analysis(
            scenarios=test_scenarios,
            include_predefined=False,
            save_results=False
        )
        
        if not results.get('success'):
            print(f"❌ 多投资组合分析失败: {results.get('error')}")
            return False
        
        multi_analysis = results['multi_analysis_results']
        comparison_analysis = multi_analysis['comparison_analysis']
        
        print(f"   ✅ 对比分析完成: {results['num_scenarios']} 个方案")
        print(f"   🏆 最优方案: {comparison_analysis['best_overall_scenario']}")
        
        # 显示排名
        rankings = comparison_analysis['rankings']
        print(f"   📊 最低VaR: {rankings['lowest_var']}")
        print(f"   📊 最高分散化: {rankings['highest_diversification']}")
        
        print("✅ 多投资组合对比分析测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 多投资组合对比分析测试失败: {e}")
        return False


def test_optimization_analysis():
    """测试投资组合优化分析"""
    print("\n🧪 测试投资组合优化分析...")
    
    try:
        # 初始化分析器
        multi_analyzer = MultiPortfolioAnalyzer()
        
        # 当前权重配置
        current_weights = {"股票A": 0.4, "股票B": 0.3, "债券A": 0.2, "债券B": 0.1}
        
        # 运行优化分析（不保存文件）
        results = multi_analyzer.run_optimization_analysis(
            current_weights=current_weights,
            num_random_scenarios=3,
            save_results=False
        )
        
        if not results.get('success'):
            print(f"❌ 投资组合优化分析失败: {results.get('error')}")
            return False
        
        optimization_results = results['optimization_results']
        improvement_potential = optimization_results['improvement_potential']
        
        print(f"   ✅ 优化分析完成")
        print(f"   🎯 最优方案: {results['optimal_scenario']}")
        print(f"   📈 VaR改进潜力: {improvement_potential['var_improvement_pct']:.1f}%")
        print(f"   📈 ES改进潜力: {improvement_potential['es_improvement_pct']:.1f}%")
        
        # 检查权重调整建议
        recommendations = optimization_results['recommendations']
        if recommendations.get('weight_adjustments'):
            print(f"   💡 权重调整建议: {len(recommendations['weight_adjustments'])} 项")
        
        print("✅ 投资组合优化分析测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 投资组合优化分析测试失败: {e}")
        return False


def test_main_system_integration():
    """测试主系统集成"""
    print("\n🧪 测试主系统集成...")
    
    try:
        # 初始化风险管理系统
        risk_system = RiskManagementSystem()
        
        # 1. 测试单个投资组合分析
        print("1. 测试单个投资组合分析集成...")
        single_results = risk_system.run_portfolio_composition_analysis(
            weights={"股票A": 0.3, "股票B": 0.2, "债券A": 0.3, "债券B": 0.2},
            analysis_type="single"
        )
        
        if not single_results.get('success'):
            print(f"❌ 单个分析集成失败: {single_results.get('error')}")
            return False
        
        print(f"   ✅ 单个分析集成成功: {single_results['scenario_name']}")
        
        # 2. 测试多投资组合分析
        print("2. 测试多投资组合分析集成...")
        multi_results = risk_system.run_portfolio_composition_analysis(
            analysis_type="multi",
            include_predefined=True
        )
        
        if not multi_results.get('success'):
            print(f"❌ 多组合分析集成失败: {multi_results.get('error')}")
            return False
        
        print(f"   ✅ 多组合分析集成成功: {multi_results['num_scenarios']} 个方案")
        
        # 3. 测试优化分析
        print("3. 测试优化分析集成...")
        opt_results = risk_system.run_portfolio_composition_analysis(
            weights={"股票A": 0.4, "股票B": 0.3, "债券A": 0.2, "债券B": 0.1},
            analysis_type="optimization"
        )
        
        if not opt_results.get('success'):
            print(f"❌ 优化分析集成失败: {opt_results.get('error')}")
            return False
        
        print(f"   ✅ 优化分析集成成功: {opt_results['optimal_scenario']}")
        
        print("✅ 主系统集成测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 主系统集成测试失败: {e}")
        return False


def test_weight_input_formats():
    """测试各种权重输入格式"""
    print("\n🧪 测试权重输入格式...")
    
    try:
        weight_manager = WeightConfigManager()
        
        # 1. 测试简单格式
        print("1. 测试简单格式...")
        simple_format = "股票A:0.3,股票B:0.2,债券A:0.3,债券B:0.2"
        parsed_simple = weight_manager.parse_weight_input(simple_format)
        print(f"   ✅ 简单格式解析成功: {len(parsed_simple)} 个资产")
        
        # 2. 测试JSON格式
        print("2. 测试JSON格式...")
        json_format = '{"股票A": 0.3, "股票B": 0.2, "债券A": 0.3, "债券B": 0.2}'
        parsed_json = weight_manager.parse_weight_input(json_format)
        print(f"   ✅ JSON格式解析成功: {len(parsed_json)} 个资产")
        
        # 3. 测试字典格式
        print("3. 测试字典格式...")
        dict_format = {"股票A": 0.3, "股票B": 0.2, "债券A": 0.3, "债券B": 0.2}
        parsed_dict = weight_manager.parse_weight_input(dict_format)
        print(f"   ✅ 字典格式解析成功: {len(parsed_dict)} 个资产")
        
        # 4. 测试等权重列表
        print("4. 测试等权重列表...")
        list_format = ["股票A", "股票B", "债券A", "债券B"]
        parsed_list = weight_manager.parse_weight_input(list_format)
        print(f"   ✅ 列表格式解析成功: {len(parsed_list)} 个资产，每个权重: {list(parsed_list.values())[0]:.2f}")
        
        print("✅ 权重输入格式测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 权重输入格式测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 多资产组合风险构成分析功能测试套件")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("权重配置管理器", test_weight_config_manager),
        ("权重输入格式", test_weight_input_formats),
        ("单个投资组合分析", test_single_portfolio_analysis),
        ("多投资组合对比分析", test_multi_portfolio_analysis),
        ("投资组合优化分析", test_optimization_analysis),
        ("主系统集成", test_main_system_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 40)
        
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    # 测试结果摘要
    print("\n" + "=" * 60)
    print(f"📊 测试结果摘要: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! 多资产组合风险构成分析功能正常工作")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
