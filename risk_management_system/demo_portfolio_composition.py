#!/usr/bin/env python3
"""
多资产组合风险构成分析功能演示脚本
展示新增功能的完整使用流程
"""

import sys
from pathlib import Path
import pandas as pd
import json

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from main import RiskManagementSystem


def demo_single_portfolio_analysis():
    """演示单个投资组合分析"""
    print("🎯 演示1: 单个投资组合风险构成分析")
    print("=" * 50)
    
    # 初始化系统
    risk_system = RiskManagementSystem()
    
    # 定义权重配置
    custom_weights = {
        "股票A": 0.30,
        "股票B": 0.25,
        "债券A": 0.25,
        "债券B": 0.15,
        "商品A": 0.05
    }
    
    print(f"📊 分析权重配置: {custom_weights}")
    
    # 运行分析
    results = risk_system.run_portfolio_composition_analysis(
        weights=custom_weights,
        analysis_type="single"
    )
    
    if results.get('success'):
        print("✅ 分析完成!")
        print(f"📊 投资组合VaR: {results['portfolio_var']:.2f}%")
        print(f"📊 投资组合ES: {results['portfolio_es']:.2f}%")
        print(f"📊 有效资产数: {results['effective_assets']:.1f}")
        
        # 分散化评估
        concentration = results['analysis_summary'].get('concentration_hhi', 0)
        if concentration < 0.2:
            div_status = "🟢 良好分散"
        elif concentration < 0.4:
            div_status = "🟡 中等分散"
        else:
            div_status = "🔴 集中度较高"
        print(f"📊 分散化状态: {div_status}")
    else:
        print(f"❌ 分析失败: {results.get('error')}")
    
    print()


def demo_multi_portfolio_comparison():
    """演示多投资组合对比分析"""
    print("🎯 演示2: 多投资组合对比分析")
    print("=" * 50)
    
    # 初始化系统
    risk_system = RiskManagementSystem()
    
    print("📊 对比预定义投资组合方案...")
    
    # 运行多投资组合对比分析
    results = risk_system.run_portfolio_composition_analysis(
        analysis_type="multi",
        include_predefined=True
    )
    
    if results.get('success'):
        print("✅ 对比分析完成!")
        print(f"📊 对比方案数量: {results['num_scenarios']}")
        print(f"🏆 最优方案: {results['best_scenario']}")
        
        risk_range = results['analysis_summary'].get('risk_range', {})
        if risk_range:
            var_range = risk_range.get('var_range', (0, 0))
            print(f"📊 VaR变化范围: {var_range[0]:.2f}% - {var_range[1]:.2f}%")
            
            div_range = risk_range.get('diversification_range', (0, 0))
            print(f"📊 分散化范围: {div_range[0]:.1f} - {div_range[1]:.1f} 有效资产")
    else:
        print(f"❌ 对比分析失败: {results.get('error')}")
    
    print()


def demo_optimization_analysis():
    """演示投资组合优化分析"""
    print("🎯 演示3: 投资组合优化分析")
    print("=" * 50)
    
    # 初始化系统
    risk_system = RiskManagementSystem()
    
    # 定义当前权重配置（风险较高的配置）
    current_weights = {
        "股票A": 0.45,
        "股票B": 0.35,
        "债券A": 0.15,
        "债券B": 0.05,
        "商品A": 0.00
    }
    
    print(f"📊 当前权重配置: {current_weights}")
    print("📊 寻找优化建议...")
    
    # 运行优化分析
    results = risk_system.run_portfolio_composition_analysis(
        weights=current_weights,
        analysis_type="optimization"
    )
    
    if results.get('success'):
        print("✅ 优化分析完成!")
        print(f"🎯 最优方案: {results['optimal_scenario']}")
        print(f"📊 对比方案数: {results['num_scenarios_compared']}")
        
        improvement = results['analysis_summary'].get('improvement_potential', {})
        if improvement:
            var_improvement = improvement.get('var_improvement_pct', 0)
            es_improvement = improvement.get('es_improvement_pct', 0)
            if var_improvement > 0:
                print(f"📈 VaR改进潜力: {var_improvement:.1f}%")
                print(f"📈 ES改进潜力: {es_improvement:.1f}%")
            else:
                print("📊 当前配置已接近最优")
    else:
        print(f"❌ 优化分析失败: {results.get('error')}")
    
    print()


def demo_weight_input_formats():
    """演示各种权重输入格式"""
    print("🎯 演示4: 多种权重输入格式")
    print("=" * 50)
    
    # 初始化系统
    risk_system = RiskManagementSystem()
    
    # 1. 简单格式
    print("1. 简单格式输入:")
    simple_weights = "股票A:0.3,股票B:0.2,债券A:0.3,债券B:0.2"
    print(f"   输入: {simple_weights}")
    
    results1 = risk_system.run_portfolio_composition_analysis(
        weights=simple_weights,
        analysis_type="single"
    )
    
    if results1.get('success'):
        print(f"   ✅ 解析成功 - VaR: {results1['portfolio_var']:.2f}%")
    else:
        print(f"   ❌ 解析失败: {results1.get('error')}")
    
    # 2. JSON格式
    print("\n2. JSON格式输入:")
    json_weights = '{"股票A": 0.25, "股票B": 0.25, "债券A": 0.25, "债券B": 0.25}'
    print(f"   输入: {json_weights}")
    
    results2 = risk_system.run_portfolio_composition_analysis(
        weights=json_weights,
        analysis_type="single"
    )
    
    if results2.get('success'):
        print(f"   ✅ 解析成功 - VaR: {results2['portfolio_var']:.2f}%")
    else:
        print(f"   ❌ 解析失败: {results2.get('error')}")
    
    # 3. 字典格式
    print("\n3. 字典格式输入:")
    dict_weights = {"股票A": 0.4, "股票B": 0.3, "债券A": 0.2, "债券B": 0.1}
    print(f"   输入: {dict_weights}")
    
    results3 = risk_system.run_portfolio_composition_analysis(
        weights=dict_weights,
        analysis_type="single"
    )
    
    if results3.get('success'):
        print(f"   ✅ 解析成功 - VaR: {results3['portfolio_var']:.2f}%")
    else:
        print(f"   ❌ 解析失败: {results3.get('error')}")
    
    print()


def demo_file_based_analysis():
    """演示基于文件的权重配置分析"""
    print("🎯 演示5: 基于文件的权重配置分析")
    print("=" * 50)
    
    # 初始化系统
    risk_system = RiskManagementSystem()
    
    # 检查测试权重文件是否存在
    weights_file = "test_weights.yaml"
    if not Path(weights_file).exists():
        print(f"❌ 权重配置文件不存在: {weights_file}")
        return
    
    print(f"📁 从文件加载权重配置: {weights_file}")
    
    # 运行基于文件的多投资组合分析
    results = risk_system.run_portfolio_composition_analysis(
        analysis_type="multi",
        weights_file=weights_file,
        include_predefined=False  # 只使用文件中的配置
    )
    
    if results.get('success'):
        print("✅ 文件分析完成!")
        print(f"📊 从文件加载方案数: {results['num_scenarios']}")
        print(f"🏆 最优方案: {results['best_scenario']}")
        
        risk_range = results['analysis_summary'].get('risk_range', {})
        if risk_range:
            var_range = risk_range.get('var_range', (0, 0))
            print(f"📊 VaR变化范围: {var_range[0]:.2f}% - {var_range[1]:.2f}%")
    else:
        print(f"❌ 文件分析失败: {results.get('error')}")
    
    print()


def demo_command_line_usage():
    """演示命令行使用方法"""
    print("🎯 演示6: 命令行使用方法")
    print("=" * 50)
    
    print("以下是各种命令行使用示例:")
    print()
    
    print("1. 单个投资组合分析:")
    print("   python main.py --mode portfolio-composition \\")
    print("       --composition-type single \\")
    print("       --weights \"股票A:0.3,股票B:0.2,债券A:0.3,债券B:0.2\"")
    print()
    
    print("2. 多投资组合对比分析:")
    print("   python main.py --mode portfolio-composition \\")
    print("       --composition-type multi \\")
    print("       --include-predefined")
    print()
    
    print("3. 从文件加载权重配置:")
    print("   python main.py --mode portfolio-composition \\")
    print("       --composition-type multi \\")
    print("       --weights-file test_weights.yaml")
    print()
    
    print("4. 投资组合优化分析:")
    print("   python main.py --mode portfolio-composition \\")
    print("       --composition-type optimization \\")
    print("       --weights \"股票A:0.4,股票B:0.3,债券A:0.2,债券B:0.1\"")
    print()


def main():
    """主演示函数"""
    print("🚀 多资产组合风险构成分析功能演示")
    print("=" * 60)
    print("本演示将展示新增的多资产组合风险构成分析功能的各种使用方法")
    print()
    
    # 运行各个演示
    demos = [
        demo_single_portfolio_analysis,
        demo_multi_portfolio_comparison,
        demo_optimization_analysis,
        demo_weight_input_formats,
        demo_file_based_analysis,
        demo_command_line_usage
    ]
    
    for i, demo_func in enumerate(demos, 1):
        try:
            demo_func()
        except Exception as e:
            print(f"❌ 演示 {i} 执行失败: {e}")
            print()
        
        if i < len(demos):
            input("按回车键继续下一个演示...")
            print()
    
    # 总结
    print("🎉 演示完成!")
    print("=" * 60)
    print("多资产组合风险构成分析功能特点:")
    print("✅ 支持多种权重输入格式（简单格式、JSON、字典、文件）")
    print("✅ 提供三种分析类型（单个、对比、优化）")
    print("✅ 生成专业的可视化图表和详细报告")
    print("✅ 支持命令行和程序化调用")
    print("✅ 与现有风险管理系统完全集成")
    print()
    print("📁 所有分析结果已保存到 results/ 目录")
    print("📊 包含可视化图表、详细报告和Excel数据文件")
    print()
    print("🔗 更多信息请参考:")
    print("   - README.md: 完整功能说明")
    print("   - MULTI_PORTFOLIO_ANALYSIS_SUMMARY.md: 功能实现总结")
    print("   - test_portfolio_composition.py: 功能测试脚本")


if __name__ == "__main__":
    main()
