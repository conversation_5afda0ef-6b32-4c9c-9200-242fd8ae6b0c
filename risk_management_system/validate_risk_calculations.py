#!/usr/bin/env python3
"""
VaR和ES计算逻辑全面验证脚本
检查和验证风险管理系统中的VaR和ES计算的正确性
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.risk_models import ParametricRiskModel, HistoricalSimulationModel, MonteCarloSimulationModel
from core.portfolio_manager import PortfolioManager
from core.marginal_risk import MarginalRiskAnalyzer
from core.risk_contribution import RiskContributionCalculator
from config.config_manager import config_manager


class RiskCalculationValidator:
    """VaR和ES计算验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.tolerance = 1e-6
        self.confidence_level = 0.95
        self.results = {
            'var_tests': [],
            'es_tests': [],
            'marginal_tests': [],
            'consistency_tests': [],
            'numerical_tests': []
        }
        
        print("🔬 VaR和ES计算逻辑验证器初始化完成")
    
    def generate_test_data(self, n_obs: int = 1000, n_assets: int = 4) -> pd.DataFrame:
        """生成测试数据"""
        np.random.seed(42)  # 确保结果可重复
        
        # 生成相关的收益率数据
        mean_returns = np.array([0.0005, 0.0003, 0.0002, 0.0001])[:n_assets]
        volatilities = np.array([0.02, 0.015, 0.01, 0.008])[:n_assets]
        
        # 创建相关性矩阵
        correlation_matrix = np.array([
            [1.0, 0.3, 0.1, 0.05],
            [0.3, 1.0, 0.2, 0.1],
            [0.1, 0.2, 1.0, 0.15],
            [0.05, 0.1, 0.15, 1.0]
        ])[:n_assets, :n_assets]
        
        # 生成协方差矩阵
        cov_matrix = np.outer(volatilities, volatilities) * correlation_matrix
        
        # 生成多元正态分布的收益率
        returns = np.random.multivariate_normal(mean_returns, cov_matrix, n_obs)
        
        asset_names = [f"股票{chr(65+i)}" for i in range(n_assets)]
        dates = pd.date_range('2020-01-01', periods=n_obs, freq='D')
        
        return pd.DataFrame(returns, index=dates, columns=asset_names)
    
    def test_var_calculation_correctness(self):
        """测试VaR计算的正确性"""
        print("\n🧪 测试1: VaR计算正确性验证")
        print("-" * 50)
        
        # 生成测试数据
        returns_data = self.generate_test_data()
        
        # 测试参数法VaR
        parametric_model = ParametricRiskModel()
        
        for asset in returns_data.columns:
            asset_returns = returns_data[asset]
            
            # 计算VaR
            var = parametric_model.calculate_var(asset_returns, self.confidence_level)
            
            # 手动验证：正态分布VaR = -μ - σ * Φ^(-1)(α)
            mu = asset_returns.mean()
            sigma = asset_returns.std()
            alpha = 1 - self.confidence_level
            expected_var = -mu - sigma * stats.norm.ppf(alpha)
            
            # 考虑持有期调整
            holding_period = config_manager.get_risk_calculation_config().holding_period
            expected_var *= np.sqrt(holding_period)
            
            relative_error = abs(var - expected_var) / expected_var if expected_var != 0 else abs(var)
            
            test_result = {
                'asset': asset,
                'calculated_var': var,
                'expected_var': expected_var,
                'relative_error': relative_error,
                'passed': relative_error < 0.1  # 10%容差，考虑GARCH模型的影响
            }
            
            self.results['var_tests'].append(test_result)
            
            status = "✅ PASS" if test_result['passed'] else "❌ FAIL"
            print(f"   {asset}: VaR={var:.4f}, Expected={expected_var:.4f}, Error={relative_error:.2%} {status}")
        
        passed_tests = sum(1 for test in self.results['var_tests'] if test['passed'])
        print(f"\n📊 VaR计算测试结果: {passed_tests}/{len(self.results['var_tests'])} 通过")
    
    def test_es_calculation_correctness(self):
        """测试ES计算的正确性"""
        print("\n🧪 测试2: ES计算正确性验证")
        print("-" * 50)
        
        # 生成测试数据
        returns_data = self.generate_test_data()
        
        # 测试参数法ES
        parametric_model = ParametricRiskModel()
        
        for asset in returns_data.columns:
            asset_returns = returns_data[asset]
            
            # 计算VaR和ES
            var = parametric_model.calculate_var(asset_returns, self.confidence_level)
            es = parametric_model.calculate_es(asset_returns, self.confidence_level)
            
            # 验证ES >= VaR（理论要求）
            es_var_relation = es >= var
            
            # 手动验证：正态分布ES = -μ - σ * φ(Φ^(-1)(α)) / α
            mu = asset_returns.mean()
            sigma = asset_returns.std()
            alpha = 1 - self.confidence_level
            quantile = stats.norm.ppf(alpha)
            expected_es = -mu - sigma * stats.norm.pdf(quantile) / alpha
            
            # 考虑持有期调整
            holding_period = config_manager.get_risk_calculation_config().holding_period
            expected_es *= np.sqrt(holding_period)
            
            relative_error = abs(es - expected_es) / expected_es if expected_es != 0 else abs(es)
            
            test_result = {
                'asset': asset,
                'calculated_es': es,
                'calculated_var': var,
                'expected_es': expected_es,
                'es_var_relation': es_var_relation,
                'relative_error': relative_error,
                'passed': relative_error < 0.1 and es_var_relation
            }
            
            self.results['es_tests'].append(test_result)
            
            status = "✅ PASS" if test_result['passed'] else "❌ FAIL"
            relation_status = "✅" if es_var_relation else "❌"
            print(f"   {asset}: ES={es:.4f}, VaR={var:.4f}, ES≥VaR:{relation_status}, Error={relative_error:.2%} {status}")
        
        passed_tests = sum(1 for test in self.results['es_tests'] if test['passed'])
        print(f"\n📊 ES计算测试结果: {passed_tests}/{len(self.results['es_tests'])} 通过")
    
    def test_marginal_risk_calculations(self):
        """测试边际风险计算"""
        print("\n🧪 测试3: 边际风险计算验证")
        print("-" * 50)
        
        # 生成测试数据和投资组合
        returns_data = self.generate_test_data()
        weights = pd.Series([0.3, 0.25, 0.25, 0.2], index=returns_data.columns)
        
        portfolio_manager = PortfolioManager(weights, returns_data)
        marginal_analyzer = MarginalRiskAnalyzer(portfolio_manager)
        
        # 计算边际VaR
        marginal_vars = marginal_analyzer.calculate_marginal_var(returns_data)
        
        # 验证边际VaR的数值稳定性
        for asset, mvar in marginal_vars.items():
            # 边际VaR应该是有限的数值
            is_finite = np.isfinite(mvar)
            is_reasonable = abs(mvar) < 1.0  # 边际VaR不应该过大
            
            test_result = {
                'asset': asset,
                'marginal_var': mvar,
                'is_finite': is_finite,
                'is_reasonable': is_reasonable,
                'passed': is_finite and is_reasonable
            }
            
            self.results['marginal_tests'].append(test_result)
            
            status = "✅ PASS" if test_result['passed'] else "❌ FAIL"
            print(f"   {asset}: MVaR={mvar:.6f}, Finite:{is_finite}, Reasonable:{is_reasonable} {status}")
        
        passed_tests = sum(1 for test in self.results['marginal_tests'] if test['passed'])
        print(f"\n📊 边际风险测试结果: {passed_tests}/{len(self.results['marginal_tests'])} 通过")
    
    def test_risk_contribution_consistency(self):
        """测试风险贡献度一致性"""
        print("\n🧪 测试4: 风险贡献度一致性验证")
        print("-" * 50)
        
        # 生成测试数据和投资组合
        returns_data = self.generate_test_data()
        weights = pd.Series([0.3, 0.25, 0.25, 0.2], index=returns_data.columns)
        
        portfolio_manager = PortfolioManager(weights, returns_data)
        risk_contrib_calc = RiskContributionCalculator(portfolio_manager)
        
        # 计算组合VaR
        portfolio_returns = portfolio_manager.calculate_portfolio_returns(returns_data)
        parametric_model = ParametricRiskModel()
        portfolio_var = parametric_model.calculate_var(portfolio_returns, self.confidence_level)
        
        # 计算成分VaR
        component_vars = risk_contrib_calc.calculate_component_var(returns_data)
        
        # 验证成分VaR之和等于组合VaR
        sum_component_var = sum(component_vars.values())
        absolute_error = abs(sum_component_var - portfolio_var)
        relative_error = absolute_error / portfolio_var if portfolio_var > 0 else absolute_error
        
        # 使用内置验证方法 (使用配置的容差)
        is_valid = risk_contrib_calc.validate_risk_contributions(component_vars, portfolio_var)

        # 获取验证配置
        validation_config = config_manager.get_risk_validation_config()
        tolerance = validation_config.var_consistency_tolerance

        test_result = {
            'portfolio_var': portfolio_var,
            'sum_component_var': sum_component_var,
            'absolute_error': absolute_error,
            'relative_error': relative_error,
            'validation_passed': is_valid,
            'passed': relative_error < tolerance or not validation_config.enable_strict_validation
        }
        
        self.results['consistency_tests'].append(test_result)
        
        status = "✅ PASS" if test_result['passed'] else "❌ FAIL"
        print(f"   组合VaR: {portfolio_var:.6f}")
        print(f"   成分VaR总和: {sum_component_var:.6f}")
        print(f"   绝对误差: {absolute_error:.6f}")
        print(f"   相对误差: {relative_error:.4%}")
        print(f"   一致性验证: {status}")
        
        # 显示各资产的成分VaR
        print(f"\n   各资产成分VaR:")
        for asset, comp_var in component_vars.items():
            contribution_pct = (comp_var / portfolio_var * 100) if portfolio_var > 0 else 0
            print(f"     {asset}: {comp_var:.6f} ({contribution_pct:.1f}%)")
    
    def test_numerical_stability(self):
        """测试数值稳定性"""
        print("\n🧪 测试5: 数值稳定性验证")
        print("-" * 50)
        
        # 测试极端权重配置
        returns_data = self.generate_test_data()
        
        extreme_scenarios = [
            ("等权重", pd.Series([0.25, 0.25, 0.25, 0.25], index=returns_data.columns)),
            ("集中权重", pd.Series([0.97, 0.01, 0.01, 0.01], index=returns_data.columns)),
            ("小权重", pd.Series([0.001, 0.001, 0.001, 0.997], index=returns_data.columns))
        ]
        
        parametric_model = ParametricRiskModel()
        
        for scenario_name, weights in extreme_scenarios:
            try:
                portfolio_manager = PortfolioManager(weights, returns_data)
                portfolio_returns = portfolio_manager.calculate_portfolio_returns(returns_data)
                
                # 计算VaR和ES
                var = parametric_model.calculate_var(portfolio_returns, self.confidence_level)
                es = parametric_model.calculate_es(portfolio_returns, self.confidence_level)
                
                # 检查数值稳定性
                var_finite = np.isfinite(var)
                es_finite = np.isfinite(es)
                es_ge_var = es >= var
                reasonable_range = 0 < var < 1 and 0 < es < 1
                
                test_result = {
                    'scenario': scenario_name,
                    'var': var,
                    'es': es,
                    'var_finite': var_finite,
                    'es_finite': es_finite,
                    'es_ge_var': es_ge_var,
                    'reasonable_range': reasonable_range,
                    'passed': all([var_finite, es_finite, es_ge_var, reasonable_range])
                }
                
                self.results['numerical_tests'].append(test_result)
                
                status = "✅ PASS" if test_result['passed'] else "❌ FAIL"
                print(f"   {scenario_name}: VaR={var:.6f}, ES={es:.6f}, ES≥VaR:{es_ge_var} {status}")
                
            except Exception as e:
                print(f"   {scenario_name}: ❌ FAIL - 计算异常: {e}")
                self.results['numerical_tests'].append({
                    'scenario': scenario_name,
                    'error': str(e),
                    'passed': False
                })
        
        passed_tests = sum(1 for test in self.results['numerical_tests'] if test.get('passed', False))
        print(f"\n📊 数值稳定性测试结果: {passed_tests}/{len(self.results['numerical_tests'])} 通过")
    
    def test_cross_model_consistency(self):
        """测试不同模型间的一致性"""
        print("\n🧪 测试6: 跨模型一致性验证")
        print("-" * 50)
        
        # 生成测试数据
        returns_data = self.generate_test_data(n_obs=500)  # 较少数据点以减少计算时间
        
        # 创建不同的风险模型
        parametric_model = ParametricRiskModel()
        historical_model = HistoricalSimulationModel()
        
        # 测试单个资产
        asset = returns_data.columns[0]
        asset_returns = returns_data[asset]
        
        # 计算VaR和ES
        param_var = parametric_model.calculate_var(asset_returns, self.confidence_level)
        param_es = parametric_model.calculate_es(asset_returns, self.confidence_level)
        
        hist_var = historical_model.calculate_var(asset_returns, self.confidence_level)
        hist_es = historical_model.calculate_es(asset_returns, self.confidence_level)
        
        # 比较结果（应该在合理范围内）
        var_diff = abs(param_var - hist_var) / max(param_var, hist_var)
        es_diff = abs(param_es - hist_es) / max(param_es, hist_es)
        
        # 一般来说，不同方法的结果差异应该在50%以内
        var_consistent = var_diff < 0.5
        es_consistent = es_diff < 0.5
        
        print(f"   参数法 VaR: {param_var:.6f}, ES: {param_es:.6f}")
        print(f"   历史法 VaR: {hist_var:.6f}, ES: {hist_es:.6f}")
        print(f"   VaR差异: {var_diff:.2%}, ES差异: {es_diff:.2%}")
        
        consistency_status = "✅ PASS" if (var_consistent and es_consistent) else "❌ FAIL"
        print(f"   跨模型一致性: {consistency_status}")
    
    def generate_summary_report(self):
        """生成验证摘要报告"""
        print("\n" + "="*60)
        print("📋 VaR和ES计算验证摘要报告")
        print("="*60)
        
        # 统计各类测试结果
        test_categories = [
            ("VaR计算正确性", self.results['var_tests']),
            ("ES计算正确性", self.results['es_tests']),
            ("边际风险计算", self.results['marginal_tests']),
            ("风险贡献度一致性", self.results['consistency_tests']),
            ("数值稳定性", self.results['numerical_tests'])
        ]
        
        total_tests = 0
        total_passed = 0
        
        for category_name, tests in test_categories:
            if tests:
                passed = sum(1 for test in tests if test.get('passed', False))
                total = len(tests)
                total_tests += total
                total_passed += passed
                
                status = "✅" if passed == total else "⚠️" if passed > 0 else "❌"
                print(f"{status} {category_name}: {passed}/{total} 通过")
        
        print(f"\n🎯 总体验证结果: {total_passed}/{total_tests} 通过 ({total_passed/total_tests*100:.1f}%)")
        
        # 关键发现
        print(f"\n🔍 关键发现:")
        
        # 检查是否有严重问题
        critical_issues = []
        
        # VaR计算问题
        var_failures = [test for test in self.results['var_tests'] if not test.get('passed', False)]
        if var_failures:
            critical_issues.append(f"VaR计算存在 {len(var_failures)} 个问题")
        
        # ES计算问题
        es_failures = [test for test in self.results['es_tests'] if not test.get('passed', False)]
        if es_failures:
            critical_issues.append(f"ES计算存在 {len(es_failures)} 个问题")
        
        # 一致性问题
        consistency_failures = [test for test in self.results['consistency_tests'] if not test.get('passed', False)]
        if consistency_failures:
            critical_issues.append("风险贡献度一致性验证失败")
        
        if critical_issues:
            print("❌ 发现关键问题:")
            for issue in critical_issues:
                print(f"   - {issue}")
        else:
            print("✅ 未发现关键计算错误")
        
        # 建议
        print(f"\n💡 建议:")
        if total_passed / total_tests >= 0.9:
            print("   - 风险计算逻辑总体正确，可以投入使用")
        elif total_passed / total_tests >= 0.7:
            print("   - 存在一些计算问题，建议进一步检查和优化")
        else:
            print("   - 存在严重的计算问题，需要立即修复")
        
        print("   - 定期运行此验证脚本以确保计算准确性")
        print("   - 在生产环境中使用前进行更多的回测验证")


def main():
    """主验证函数"""
    print("🚀 VaR和ES计算逻辑全面验证")
    print("="*60)
    
    validator = RiskCalculationValidator()
    
    # 运行所有验证测试
    try:
        validator.test_var_calculation_correctness()
        validator.test_es_calculation_correctness()
        validator.test_marginal_risk_calculations()
        validator.test_risk_contribution_consistency()
        validator.test_numerical_stability()
        validator.test_cross_model_consistency()
        
        # 生成摘要报告
        validator.generate_summary_report()
        
        return True
        
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
