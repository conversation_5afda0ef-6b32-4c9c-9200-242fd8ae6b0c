"""
配置管理模块
负责加载和管理系统参数
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class RiskCalculationConfig:
    """风险计算配置"""
    confidence_level: float = 0.99
    holding_period: int = 1
    risk_free_rate: float = 0.0


@dataclass
class DataProcessingConfig:
    """数据处理配置"""
    historical_data_path: str = "data/historical_returns.csv"
    portfolio_weights_path: str = "data/portfolio_weights.csv"
    data_window_size: int = 250
    log_returns: bool = True
    min_data_points: int = 100


@dataclass
class ParametricMethodConfig:
    """参数法配置"""
    garch_order: list = None
    ewma_lambda: float = 0.94
    distribution_assumption: str = "normal"
    max_iter: int = 1000
    
    def __post_init__(self):
        if self.garch_order is None:
            self.garch_order = [1, 1]


@dataclass
class HistoricalSimulationConfig:
    """历史模拟法配置"""
    bootstrap_samples: int = 1000
    use_weighted_hs: bool = False


@dataclass
class MonteCarloConfig:
    """蒙特卡洛模拟配置"""
    num_simulations: int = 10000
    num_steps_mc: int = 1
    mc_model_params: str = "geometric_brownian_motion"
    random_seed: int = 42


@dataclass
class RiskContributionConfig:
    """风险贡献度配置"""
    delta_weight: float = 0.01
    use_analytical_mvar: bool = True


@dataclass
class OutputConfig:
    """输出配置"""
    results_path: str = "results/"
    daily_results_file: str = "daily_risk_metrics.xlsx"
    report_format: str = "markdown"
    save_intermediate_results: bool = True


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    log_file: str = "logs/risk_system.log"
    max_file_size: str = "10MB"
    backup_count: int = 5


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config/parameters.yaml"
        self._config = None
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if self._config is None:
            config_file = Path(self.config_path)
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    self._config = yaml.safe_load(f)
            else:
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        return self._config
    
    def get_risk_calculation_config(self) -> RiskCalculationConfig:
        """获取风险计算配置"""
        config = self.load_config()
        return RiskCalculationConfig(**config.get('risk_calculation', {}))
    
    def get_data_processing_config(self) -> DataProcessingConfig:
        """获取数据处理配置"""
        config = self.load_config()
        return DataProcessingConfig(**config.get('data_processing', {}))
    
    def get_parametric_method_config(self) -> ParametricMethodConfig:
        """获取参数法配置"""
        config = self.load_config()
        return ParametricMethodConfig(**config.get('parametric_method', {}))
    
    def get_historical_simulation_config(self) -> HistoricalSimulationConfig:
        """获取历史模拟法配置"""
        config = self.load_config()
        return HistoricalSimulationConfig(**config.get('historical_simulation', {}))
    
    def get_monte_carlo_config(self) -> MonteCarloConfig:
        """获取蒙特卡洛配置"""
        config = self.load_config()
        return MonteCarloConfig(**config.get('monte_carlo', {}))
    
    def get_risk_contribution_config(self) -> RiskContributionConfig:
        """获取风险贡献度配置"""
        config = self.load_config()
        return RiskContributionConfig(**config.get('risk_contribution', {}))
    
    def get_output_config(self) -> OutputConfig:
        """获取输出配置"""
        config = self.load_config()
        return OutputConfig(**config.get('output', {}))
    
    def get_logging_config(self) -> LoggingConfig:
        """获取日志配置"""
        config = self.load_config()
        return LoggingConfig(**config.get('logging', {}))

    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self.load_config()
    
    def update_parameter(self, section: str, key: str, value: Any) -> None:
        """更新单个参数"""
        config = self.load_config()
        if section not in config:
            config[section] = {}
        config[section][key] = value
        
        # 保存更新后的配置
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        # 清除缓存，强制重新加载
        self._config = None


# 全局配置管理器实例
config_manager = ConfigManager()
