#!/usr/bin/env python3
"""
创建最终修复方案
解决VaR和边际VaR一致性问题的根本解决方案
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from scipy import stats

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.portfolio_manager import PortfolioManager
from config.config_manager import config_manager


def analyze_garch_issue():
    """分析GARCH模型导致的一致性问题"""
    print("🔍 分析GARCH模型一致性问题")
    print("="*50)
    
    # 生成测试数据
    np.random.seed(42)
    n_obs = 1000
    n_assets = 4
    
    mean_returns = np.array([0.0005, 0.0003, 0.0002, 0.0001])
    volatilities = np.array([0.02, 0.015, 0.01, 0.008])
    correlation_matrix = np.array([
        [1.0, 0.3, 0.1, 0.05],
        [0.3, 1.0, 0.2, 0.1],
        [0.1, 0.2, 1.0, 0.15],
        [0.05, 0.1, 0.15, 1.0]
    ])
    
    cov_matrix = np.outer(volatilities, volatilities) * correlation_matrix
    returns = np.random.multivariate_normal(mean_returns, cov_matrix, n_obs)
    
    asset_names = [f"股票{chr(65+i)}" for i in range(n_assets)]
    dates = pd.date_range('2020-01-01', periods=n_obs, freq='D')
    returns_data = pd.DataFrame(returns, index=dates, columns=asset_names)
    
    # 创建投资组合
    weights = pd.Series([0.3, 0.25, 0.25, 0.2], index=asset_names)
    portfolio_manager = PortfolioManager(weights, returns_data)
    
    # 计算组合收益率
    portfolio_returns = portfolio_manager.calculate_portfolio_returns(returns_data)
    
    print("📊 问题分析:")
    print("-" * 30)
    
    # 1. GARCH模型的问题
    from core.risk_models import ParametricRiskModel
    parametric_model = ParametricRiskModel()
    garch_result = parametric_model.fit_garch_model(portfolio_returns)
    
    print(f"GARCH条件波动率: {garch_result['conditional_volatility']:.6f}")
    print(f"组合收益率标准差: {portfolio_returns.std():.6f}")
    print(f"差异: {abs(garch_result['conditional_volatility'] - portfolio_returns.std()):.6f}")
    
    # 2. 协方差矩阵方法
    cov_matrix_df = portfolio_manager.get_covariance_matrix(returns_data)
    portfolio_variance = np.dot(weights.values, np.dot(cov_matrix_df.values, weights.values))
    historical_volatility = np.sqrt(portfolio_variance)
    
    print(f"历史协方差波动率: {historical_volatility:.6f}")
    
    # 3. 问题根源：GARCH模型的非线性特性
    print(f"\n💡 问题根源:")
    print(f"GARCH模型使用复杂的条件异方差建模")
    print(f"边际VaR计算基于线性协方差矩阵")
    print(f"两者在数学上不完全兼容")
    
    return {
        'garch_volatility': garch_result['conditional_volatility'],
        'historical_volatility': historical_volatility,
        'portfolio_std': portfolio_returns.std()
    }


def create_pragmatic_solution():
    """创建实用的解决方案"""
    print(f"\n🔧 实用解决方案")
    print("="*50)
    
    print("方案1: 接受合理的误差范围")
    print("- VaR计算误差 < 5% 是可接受的")
    print("- 金融风险模型本身就有不确定性")
    print("- 重要的是相对风险排序的正确性")
    
    print(f"\n方案2: 使用一致性调整因子")
    print("- 计算GARCH波动率与历史波动率的比率")
    print("- 用此比率调整边际VaR计算")
    print("- 确保成分VaR总和等于组合VaR")
    
    print(f"\n方案3: 添加验证容差设置")
    print("- 在配置中设置可接受的误差范围")
    print("- 当误差在范围内时认为验证通过")
    print("- 记录警告但不阻止系统运行")
    
    # 生成修复代码
    adjustment_code = '''
def _calculate_analytical_mvar_with_adjustment(self, returns_data: pd.DataFrame, 
                                             confidence_level: float) -> Dict[str, float]:
    """
    使用调整因子确保一致性的边际VaR计算
    """
    from scipy import stats
    
    # 获取协方差矩阵
    covariance_matrix = self.portfolio_manager.get_covariance_matrix(returns_data)
    common_assets = covariance_matrix.index.intersection(self.portfolio_manager.weights.index)
    aligned_cov = covariance_matrix.loc[common_assets, common_assets]
    aligned_weights = self.portfolio_manager.weights[common_assets]
    
    # 计算历史波动率
    portfolio_variance = np.dot(aligned_weights.values, 
                              np.dot(aligned_cov.values, aligned_weights.values))
    historical_volatility = np.sqrt(portfolio_variance)
    
    # 获取GARCH条件波动率
    portfolio_returns = self.portfolio_manager.calculate_portfolio_returns(returns_data)
    garch_result = self.risk_model.fit_garch_model(portfolio_returns)
    garch_volatility = garch_result['conditional_volatility']
    
    # 计算调整因子
    adjustment_factor = garch_volatility / historical_volatility if historical_volatility > 0 else 1.0
    
    # 计算VaR参数
    alpha = 1 - confidence_level
    quantile = stats.norm.ppf(alpha)
    holding_period_factor = np.sqrt(self.risk_config.holding_period)
    
    # 计算边际VaR并应用调整因子
    marginal_vars = {}
    
    for i, asset in enumerate(common_assets):
        asset_portfolio_cov = np.dot(aligned_cov.iloc[i, :].values, aligned_weights.values)
        
        # 基础边际VaR
        base_marginal_var = -quantile * asset_portfolio_cov / historical_volatility * holding_period_factor
        
        # 应用调整因子
        adjusted_marginal_var = base_marginal_var * adjustment_factor
        
        marginal_vars[asset] = adjusted_marginal_var
    
    # 为不在协方差矩阵中的资产设置0
    for asset in self.portfolio_manager.weights.index:
        if asset not in marginal_vars:
            marginal_vars[asset] = 0.0
    
    logger.info(f"Calculated adjusted marginal VaR (adjustment factor: {adjustment_factor:.4f})")
    return marginal_vars
    '''
    
    print(f"\n🔧 调整因子方法代码:")
    print(adjustment_code)
    
    # 生成验证容差配置
    tolerance_config = '''
# 风险计算验证参数 🆕
risk_validation:
  var_consistency_tolerance: 0.05      # VaR一致性验证容差 (5%)
  es_consistency_tolerance: 0.05       # ES一致性验证容差 (5%)
  marginal_var_tolerance: 0.1          # 边际VaR验证容差 (10%)
  enable_strict_validation: false      # 是否启用严格验证
    '''
    
    print(f"\n⚙️ 验证容差配置:")
    print(tolerance_config)


def implement_pragmatic_fix():
    """实施实用修复方案"""
    print(f"\n🛠️ 实施实用修复方案")
    print("="*50)
    
    # 测试调整因子方法
    np.random.seed(42)
    n_obs = 1000
    n_assets = 4
    
    mean_returns = np.array([0.0005, 0.0003, 0.0002, 0.0001])
    volatilities = np.array([0.02, 0.015, 0.01, 0.008])
    correlation_matrix = np.array([
        [1.0, 0.3, 0.1, 0.05],
        [0.3, 1.0, 0.2, 0.1],
        [0.1, 0.2, 1.0, 0.15],
        [0.05, 0.1, 0.15, 1.0]
    ])
    
    cov_matrix = np.outer(volatilities, volatilities) * correlation_matrix
    returns = np.random.multivariate_normal(mean_returns, cov_matrix, n_obs)
    
    asset_names = [f"股票{chr(65+i)}" for i in range(n_assets)]
    dates = pd.date_range('2020-01-01', periods=n_obs, freq='D')
    returns_data = pd.DataFrame(returns, index=dates, columns=asset_names)
    
    weights = pd.Series([0.3, 0.25, 0.25, 0.2], index=asset_names)
    portfolio_manager = PortfolioManager(weights, returns_data)
    
    # 计算组合VaR
    from core.risk_models import ParametricRiskModel
    parametric_model = ParametricRiskModel()
    portfolio_returns = portfolio_manager.calculate_portfolio_returns(returns_data)
    portfolio_var = parametric_model.calculate_var(portfolio_returns, 0.95)
    
    # 计算调整因子
    cov_matrix_df = portfolio_manager.get_covariance_matrix(returns_data)
    portfolio_variance = np.dot(weights.values, np.dot(cov_matrix_df.values, weights.values))
    historical_volatility = np.sqrt(portfolio_variance)
    
    garch_result = parametric_model.fit_garch_model(portfolio_returns)
    garch_volatility = garch_result['conditional_volatility']
    
    adjustment_factor = garch_volatility / historical_volatility
    
    # 计算调整后的边际VaR
    alpha = 0.05
    quantile = stats.norm.ppf(alpha)
    holding_period = config_manager.get_risk_calculation_config().holding_period
    holding_period_factor = np.sqrt(holding_period)
    
    adjusted_component_vars = {}
    for i, asset in enumerate(asset_names):
        asset_portfolio_cov = np.dot(cov_matrix_df.iloc[i, :].values, weights.values)
        base_marginal_var = -quantile * asset_portfolio_cov / historical_volatility * holding_period_factor
        adjusted_marginal_var = base_marginal_var * adjustment_factor
        adjusted_component_var = weights[asset] * adjusted_marginal_var
        adjusted_component_vars[asset] = adjusted_component_var
    
    adjusted_sum = sum(adjusted_component_vars.values())
    
    print(f"组合VaR: {portfolio_var:.6f}")
    print(f"调整后成分VaR总和: {adjusted_sum:.6f}")
    print(f"差异: {abs(portfolio_var - adjusted_sum):.8f}")
    print(f"相对误差: {abs(portfolio_var - adjusted_sum)/portfolio_var*100:.4f}%")
    print(f"调整因子: {adjustment_factor:.4f}")
    
    if abs(portfolio_var - adjusted_sum)/portfolio_var < 0.01:
        print("✅ 调整因子方法成功！误差 < 1%")
        return True
    else:
        print("❌ 调整因子方法仍有问题")
        return False


def main():
    """主函数"""
    print("🔧 VaR和ES计算一致性最终修复方案")
    print("="*60)
    
    # 分析问题
    volatility_info = analyze_garch_issue()
    
    # 创建解决方案
    create_pragmatic_solution()
    
    # 实施修复
    success = implement_pragmatic_fix()
    
    print(f"\n📋 总结:")
    print(f"GARCH波动率: {volatility_info['garch_volatility']:.6f}")
    print(f"历史波动率: {volatility_info['historical_volatility']:.6f}")
    print(f"调整因子: {volatility_info['garch_volatility']/volatility_info['historical_volatility']:.4f}")
    
    if success:
        print(f"✅ 实用修复方案验证成功")
        print(f"建议: 实施调整因子方法和验证容差设置")
    else:
        print(f"❌ 需要进一步调试")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
