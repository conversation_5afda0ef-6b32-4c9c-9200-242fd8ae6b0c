"""
日志管理模块
提供统一的日志记录功能
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional

from config.config_manager import config_manager


class LoggerManager:
    """日志管理器"""

    def __init__(self):
        self.config = config_manager.get_logging_config()
        self._setup_logger()

    def _setup_logger(self) -> None:
        """设置日志配置"""
        # 创建日志目录
        log_dir = Path(self.config.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        # 移除默认处理器
        logger.remove()

        # 添加控制台输出
        logger.add(
            sys.stdout,
            level=self.config.level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>",
            colorize=True
        )

        # 添加文件输出
        logger.add(
            self.config.log_file,
            level=self.config.level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation=self.config.max_file_size,
            retention=self.config.backup_count,
            compression="zip",
            encoding="utf-8"
        )

        # 添加错误日志文件
        error_log_file = str(Path(self.config.log_file).parent / "error.log")
        logger.add(
            error_log_file,
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation=self.config.max_file_size,
            retention=self.config.backup_count,
            compression="zip",
            encoding="utf-8"
        )

    def get_logger(self, name: Optional[str] = None):
        """获取日志记录器"""
        if name:
            return logger.bind(name=name)
        return logger


# 全局日志管理器实例
log_manager = LoggerManager()
system_logger = log_manager.get_logger("RiskManagementSystem")


def get_module_logger(module_name: str):
    """获取模块专用日志记录器"""
    return log_manager.get_logger(module_name)
