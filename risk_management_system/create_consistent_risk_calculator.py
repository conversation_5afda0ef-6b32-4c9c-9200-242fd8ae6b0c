#!/usr/bin/env python3
"""
创建一致性风险计算器
确保组合VaR和成分VaR使用相同的计算方法
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from scipy import stats

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.portfolio_manager import PortfolioManager
from config.config_manager import config_manager


class ConsistentRiskCalculator:
    """一致性风险计算器"""
    
    def __init__(self, portfolio_manager: PortfolioManager):
        """初始化"""
        self.portfolio_manager = portfolio_manager
        self.risk_config = config_manager.get_risk_calculation_config()
    
    def calculate_consistent_var_and_marginal(self, returns_data: pd.DataFrame, 
                                            confidence_level: float = 0.95) -> dict:
        """
        使用一致的方法计算组合VaR和边际VaR
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            dict: 包含组合VaR、边际VaR和成分VaR的结果
        """
        # 获取协方差矩阵
        covariance_matrix = self.portfolio_manager.get_covariance_matrix(returns_data)
        
        # 确保权重和协方差矩阵的资产匹配
        common_assets = covariance_matrix.index.intersection(self.portfolio_manager.weights.index)
        aligned_cov = covariance_matrix.loc[common_assets, common_assets]
        aligned_weights = self.portfolio_manager.weights[common_assets]
        
        # 计算组合统计量
        portfolio_mean = np.dot(aligned_weights.values, returns_data[common_assets].mean().values)
        portfolio_variance = np.dot(aligned_weights.values, 
                                  np.dot(aligned_cov.values, aligned_weights.values))
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        # 计算VaR参数
        alpha = 1 - confidence_level
        quantile = stats.norm.ppf(alpha)
        holding_period_factor = np.sqrt(self.risk_config.holding_period)
        
        # 计算组合VaR (使用一致的方法)
        portfolio_var = (-portfolio_mean * self.risk_config.holding_period - 
                        quantile * portfolio_volatility * holding_period_factor)
        
        # 计算边际VaR
        marginal_vars = {}
        component_vars = {}
        
        for i, asset in enumerate(common_assets):
            # 计算资产i与组合的协方差 (权重加权)
            asset_portfolio_cov = np.dot(aligned_cov.iloc[i, :].values, aligned_weights.values)
            
            # 边际VaR: ∂VaR/∂w_i = -μ_i*T - quantile * Cov(i,portfolio) / σ_p * √T
            asset_mean = returns_data[asset].mean()
            marginal_var = (-asset_mean * self.risk_config.holding_period - 
                          quantile * asset_portfolio_cov / portfolio_volatility * holding_period_factor)
            
            # 成分VaR: w_i * ∂VaR/∂w_i
            component_var = aligned_weights[asset] * marginal_var
            
            marginal_vars[asset] = marginal_var
            component_vars[asset] = component_var
        
        # 验证欧拉定理
        sum_component_var = sum(component_vars.values())
        relative_error = abs(sum_component_var - portfolio_var) / portfolio_var if portfolio_var > 0 else 0
        
        return {
            'portfolio_var': portfolio_var,
            'portfolio_mean': portfolio_mean,
            'portfolio_volatility': portfolio_volatility,
            'marginal_vars': marginal_vars,
            'component_vars': component_vars,
            'sum_component_var': sum_component_var,
            'relative_error': relative_error,
            'euler_theorem_satisfied': relative_error < 1e-10
        }
    
    def calculate_simplified_var_and_marginal(self, returns_data: pd.DataFrame, 
                                            confidence_level: float = 0.95) -> dict:
        """
        使用简化方法计算（忽略均值项）
        """
        # 获取协方差矩阵
        covariance_matrix = self.portfolio_manager.get_covariance_matrix(returns_data)
        
        # 确保权重和协方差矩阵的资产匹配
        common_assets = covariance_matrix.index.intersection(self.portfolio_manager.weights.index)
        aligned_cov = covariance_matrix.loc[common_assets, common_assets]
        aligned_weights = self.portfolio_manager.weights[common_assets]
        
        # 计算组合波动率
        portfolio_variance = np.dot(aligned_weights.values, 
                                  np.dot(aligned_cov.values, aligned_weights.values))
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        # 计算VaR参数
        alpha = 1 - confidence_level
        quantile = stats.norm.ppf(alpha)
        holding_period_factor = np.sqrt(self.risk_config.holding_period)
        
        # 计算组合VaR (简化版本，忽略均值)
        portfolio_var = -quantile * portfolio_volatility * holding_period_factor
        
        # 计算边际VaR
        marginal_vars = {}
        component_vars = {}
        
        for i, asset in enumerate(common_assets):
            # 计算资产i与组合的协方差 (权重加权)
            asset_portfolio_cov = np.dot(aligned_cov.iloc[i, :].values, aligned_weights.values)
            
            # 边际VaR (简化版本): ∂VaR/∂w_i = -quantile * Cov(i,portfolio) / σ_p * √T
            marginal_var = -quantile * asset_portfolio_cov / portfolio_volatility * holding_period_factor
            
            # 成分VaR: w_i * ∂VaR/∂w_i
            component_var = aligned_weights[asset] * marginal_var
            
            marginal_vars[asset] = marginal_var
            component_vars[asset] = component_var
        
        # 验证欧拉定理
        sum_component_var = sum(component_vars.values())
        relative_error = abs(sum_component_var - portfolio_var) / portfolio_var if portfolio_var > 0 else 0
        
        return {
            'portfolio_var': portfolio_var,
            'portfolio_volatility': portfolio_volatility,
            'marginal_vars': marginal_vars,
            'component_vars': component_vars,
            'sum_component_var': sum_component_var,
            'relative_error': relative_error,
            'euler_theorem_satisfied': relative_error < 1e-10
        }


def test_consistent_calculation():
    """测试一致性计算"""
    print("🧪 测试一致性风险计算")
    print("="*50)
    
    # 生成测试数据
    np.random.seed(42)
    n_obs = 1000
    n_assets = 4
    
    mean_returns = np.array([0.0005, 0.0003, 0.0002, 0.0001])
    volatilities = np.array([0.02, 0.015, 0.01, 0.008])
    correlation_matrix = np.array([
        [1.0, 0.3, 0.1, 0.05],
        [0.3, 1.0, 0.2, 0.1],
        [0.1, 0.2, 1.0, 0.15],
        [0.05, 0.1, 0.15, 1.0]
    ])
    
    cov_matrix = np.outer(volatilities, volatilities) * correlation_matrix
    returns = np.random.multivariate_normal(mean_returns, cov_matrix, n_obs)
    
    asset_names = [f"股票{chr(65+i)}" for i in range(n_assets)]
    dates = pd.date_range('2020-01-01', periods=n_obs, freq='D')
    returns_data = pd.DataFrame(returns, index=dates, columns=asset_names)
    
    # 创建投资组合
    weights = pd.Series([0.3, 0.25, 0.25, 0.2], index=asset_names)
    portfolio_manager = PortfolioManager(weights, returns_data)
    
    # 创建一致性计算器
    calc = ConsistentRiskCalculator(portfolio_manager)
    
    print("📊 方法1: 完整计算 (包含均值项)")
    print("-" * 30)
    
    result1 = calc.calculate_consistent_var_and_marginal(returns_data)
    
    print(f"组合VaR: {result1['portfolio_var']:.6f}")
    print(f"成分VaR总和: {result1['sum_component_var']:.6f}")
    print(f"相对误差: {result1['relative_error']:.8f}")
    print(f"欧拉定理满足: {result1['euler_theorem_satisfied']}")
    
    print(f"\n各资产成分VaR:")
    for asset, comp_var in result1['component_vars'].items():
        contribution_pct = (comp_var / result1['portfolio_var'] * 100) if result1['portfolio_var'] > 0 else 0
        print(f"  {asset}: {comp_var:.6f} ({contribution_pct:.1f}%)")
    
    print(f"\n📊 方法2: 简化计算 (忽略均值项)")
    print("-" * 30)
    
    result2 = calc.calculate_simplified_var_and_marginal(returns_data)
    
    print(f"组合VaR: {result2['portfolio_var']:.6f}")
    print(f"成分VaR总和: {result2['sum_component_var']:.6f}")
    print(f"相对误差: {result2['relative_error']:.8f}")
    print(f"欧拉定理满足: {result2['euler_theorem_satisfied']}")
    
    print(f"\n各资产成分VaR:")
    for asset, comp_var in result2['component_vars'].items():
        contribution_pct = (comp_var / result2['portfolio_var'] * 100) if result2['portfolio_var'] > 0 else 0
        print(f"  {asset}: {comp_var:.6f} ({contribution_pct:.1f}%)")
    
    print(f"\n💡 结论:")
    print(f"方法1 (完整): 欧拉定理满足 = {result1['euler_theorem_satisfied']}")
    print(f"方法2 (简化): 欧拉定理满足 = {result2['euler_theorem_satisfied']}")
    
    if result2['euler_theorem_satisfied']:
        print("✅ 建议使用简化方法确保数学一致性")
        return result2
    elif result1['euler_theorem_satisfied']:
        print("✅ 建议使用完整方法确保数学一致性")
        return result1
    else:
        print("❌ 两种方法都存在问题，需要进一步调试")
        return None


def generate_fixed_code():
    """生成修复后的代码"""
    print(f"\n🔧 修复后的边际VaR计算代码:")
    print("="*60)
    
    code = '''
def _calculate_analytical_mvar_consistent(self, returns_data: pd.DataFrame, 
                                        confidence_level: float) -> Dict[str, float]:
    """
    使用数学一致的方法计算边际VaR
    确保成分VaR总和严格等于组合VaR
    """
    from scipy import stats
    
    # 获取协方差矩阵
    covariance_matrix = self.portfolio_manager.get_covariance_matrix(returns_data)
    
    # 确保权重和协方差矩阵的资产匹配
    common_assets = covariance_matrix.index.intersection(self.portfolio_manager.weights.index)
    aligned_cov = covariance_matrix.loc[common_assets, common_assets]
    aligned_weights = self.portfolio_manager.weights[common_assets]
    
    # 计算组合波动率
    portfolio_variance = np.dot(aligned_weights.values, 
                              np.dot(aligned_cov.values, aligned_weights.values))
    portfolio_volatility = np.sqrt(portfolio_variance)
    
    # 计算VaR参数
    alpha = 1 - confidence_level
    quantile = stats.norm.ppf(alpha)
    holding_period_factor = np.sqrt(self.risk_config.holding_period)
    
    # 计算边际VaR (简化版本，确保数学一致性)
    marginal_vars = {}
    
    for i, asset in enumerate(common_assets):
        # 计算资产i与组合的协方差 (权重加权)
        asset_portfolio_cov = np.dot(aligned_cov.iloc[i, :].values, aligned_weights.values)
        
        # 边际VaR: ∂VaR/∂w_i = -quantile * Cov(i,portfolio) / σ_p * √T
        if portfolio_volatility > 0:
            marginal_var = -quantile * asset_portfolio_cov / portfolio_volatility * holding_period_factor
        else:
            marginal_var = 0.0
        
        marginal_vars[asset] = marginal_var
    
    # 为不在协方差矩阵中的资产设置0
    for asset in self.portfolio_manager.weights.index:
        if asset not in marginal_vars:
            marginal_vars[asset] = 0.0
    
    logger.info("Calculated mathematically consistent analytical marginal VaR")
    return marginal_vars
    '''
    
    print(code)


def main():
    """主函数"""
    print("🔧 创建一致性风险计算器")
    print("="*60)
    
    # 测试一致性计算
    best_result = test_consistent_calculation()
    
    # 生成修复代码
    generate_fixed_code()
    
    if best_result and best_result['euler_theorem_satisfied']:
        print(f"\n✅ 找到了数学一致的计算方法")
        print(f"相对误差: {best_result['relative_error']:.2e}")
        return True
    else:
        print(f"\n❌ 仍需要进一步调试")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
