#!/usr/bin/env python3
"""
最终一致性检查
确定组合VaR和边际VaR计算的一致性问题
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from scipy import stats

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.risk_models import ParametricRiskModel
from core.portfolio_manager import PortfolioManager
from core.risk_contribution import RiskContributionCalculator
from config.config_manager import config_manager


def final_consistency_check():
    """最终一致性检查"""
    print("🔍 最终一致性检查")
    print("="*50)
    
    # 生成测试数据
    np.random.seed(42)
    n_obs = 1000
    n_assets = 4
    
    mean_returns = np.array([0.0005, 0.0003, 0.0002, 0.0001])
    volatilities = np.array([0.02, 0.015, 0.01, 0.008])
    correlation_matrix = np.array([
        [1.0, 0.3, 0.1, 0.05],
        [0.3, 1.0, 0.2, 0.1],
        [0.1, 0.2, 1.0, 0.15],
        [0.05, 0.1, 0.15, 1.0]
    ])
    
    cov_matrix = np.outer(volatilities, volatilities) * correlation_matrix
    returns = np.random.multivariate_normal(mean_returns, cov_matrix, n_obs)
    
    asset_names = [f"股票{chr(65+i)}" for i in range(n_assets)]
    dates = pd.date_range('2020-01-01', periods=n_obs, freq='D')
    returns_data = pd.DataFrame(returns, index=dates, columns=asset_names)
    
    # 创建投资组合
    weights = pd.Series([0.3, 0.25, 0.25, 0.2], index=asset_names)
    portfolio_manager = PortfolioManager(weights, returns_data)
    
    print("📊 第1步：检查当前系统的VaR计算")
    print("-" * 40)
    
    # 1. 当前系统的组合VaR
    portfolio_returns = portfolio_manager.calculate_portfolio_returns(returns_data)
    parametric_model = ParametricRiskModel()
    system_var = parametric_model.calculate_var(portfolio_returns, 0.95)
    print(f"系统组合VaR: {system_var:.6f}")
    
    # 2. 当前系统的边际VaR和成分VaR
    risk_contrib_calc = RiskContributionCalculator(portfolio_manager)
    marginal_vars = risk_contrib_calc._calculate_analytical_mvar(returns_data, 0.95)
    component_vars = {asset: weights[asset] * marginal_vars[asset] for asset in asset_names}
    sum_component_var = sum(component_vars.values())
    
    print(f"成分VaR总和: {sum_component_var:.6f}")
    print(f"差异: {abs(system_var - sum_component_var):.6f}")
    print(f"相对误差: {abs(system_var - sum_component_var)/system_var*100:.2f}%")
    
    print(f"\n📊 第2步：分析差异原因")
    print("-" * 40)
    
    # 检查GARCH模型的影响
    garch_result = parametric_model.fit_garch_model(portfolio_returns)
    garch_volatility = garch_result['conditional_volatility']
    
    # 计算历史协方差矩阵的组合波动率
    cov_matrix_df = portfolio_manager.get_covariance_matrix(returns_data)
    portfolio_variance = np.dot(weights.values, np.dot(cov_matrix_df.values, weights.values))
    historical_volatility = np.sqrt(portfolio_variance)
    
    print(f"GARCH条件波动率: {garch_volatility:.6f}")
    print(f"历史组合波动率: {historical_volatility:.6f}")
    print(f"波动率比率: {garch_volatility/historical_volatility:.4f}")
    
    # 检查简化VaR设置
    print(f"简化VaR设置: {parametric_model.param_config.use_simplified_var}")
    
    print(f"\n📊 第3步：手动计算一致的VaR")
    print("-" * 40)
    
    # 使用相同方法计算组合VaR和边际VaR
    alpha = 0.05
    quantile = stats.norm.ppf(alpha)
    holding_period = config_manager.get_risk_calculation_config().holding_period
    holding_period_factor = np.sqrt(holding_period)
    
    # 方法1：使用历史协方差矩阵
    consistent_var_hist = -quantile * historical_volatility * holding_period_factor
    print(f"一致VaR (历史): {consistent_var_hist:.6f}")
    print(f"成分VaR总和: {sum_component_var:.6f}")
    print(f"差异: {abs(consistent_var_hist - sum_component_var):.8f}")
    
    # 方法2：使用GARCH波动率
    consistent_var_garch = -quantile * garch_volatility * holding_period_factor
    print(f"一致VaR (GARCH): {consistent_var_garch:.6f}")
    
    # 重新计算使用GARCH波动率的边际VaR
    garch_marginal_vars = {}
    garch_component_vars = {}
    
    for i, asset in enumerate(asset_names):
        asset_portfolio_cov = np.dot(cov_matrix_df.iloc[i, :].values, weights.values)
        garch_mvar = -quantile * asset_portfolio_cov / garch_volatility * holding_period_factor
        garch_comp_var = weights[asset] * garch_mvar
        
        garch_marginal_vars[asset] = garch_mvar
        garch_component_vars[asset] = garch_comp_var
    
    garch_sum_component = sum(garch_component_vars.values())
    print(f"GARCH成分VaR总和: {garch_sum_component:.6f}")
    print(f"GARCH差异: {abs(consistent_var_garch - garch_sum_component):.8f}")
    
    print(f"\n📊 第4步：检查系统VaR计算的实际方法")
    print("-" * 40)
    
    # 检查系统实际使用的计算方法
    portfolio_mean = portfolio_returns.mean()
    portfolio_std = portfolio_returns.std()
    
    # 完整VaR (包含均值)
    full_var = -portfolio_mean * holding_period - quantile * garch_volatility * holding_period_factor
    
    # 简化VaR (忽略均值)
    simple_var = -quantile * garch_volatility * holding_period_factor
    
    print(f"完整VaR (含均值): {full_var:.6f}")
    print(f"简化VaR (无均值): {simple_var:.6f}")
    print(f"系统VaR: {system_var:.6f}")
    
    # 确定系统使用的方法
    if abs(system_var - simple_var) < abs(system_var - full_var):
        print("✅ 系统使用简化VaR方法")
        target_var = simple_var
    else:
        print("✅ 系统使用完整VaR方法")
        target_var = full_var
    
    print(f"\n💡 最终结论:")
    print(f"目标组合VaR: {target_var:.6f}")
    print(f"GARCH成分VaR总和: {garch_sum_component:.6f}")
    print(f"最终差异: {abs(target_var - garch_sum_component):.8f}")
    print(f"最终相对误差: {abs(target_var - garch_sum_component)/target_var*100:.4f}%")
    
    if abs(target_var - garch_sum_component) < 1e-10:
        print("✅ 完美一致性！")
        return True
    elif abs(target_var - garch_sum_component)/target_var < 0.01:
        print("✅ 可接受的一致性 (<1%)")
        return True
    else:
        print("❌ 仍存在一致性问题")
        return False


def main():
    """主函数"""
    print("🔍 VaR和ES计算一致性最终检查")
    print("="*60)
    
    success = final_consistency_check()
    
    if success:
        print(f"\n🎉 一致性问题已解决！")
        print(f"建议：更新边际VaR计算以使用GARCH条件波动率")
    else:
        print(f"\n⚠️ 一致性问题仍需解决")
        print(f"建议：进一步调试计算逻辑")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
