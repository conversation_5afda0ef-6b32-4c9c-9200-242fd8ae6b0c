"""
风险管理系统主程序
整合所有模块，提供完整的风险分析流程
"""

import argparse
import sys
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, Union
from datetime import datetime, timedelta
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from config.config_manager import config_manager
from core.data_loader import DataLoader
from core.portfolio_manager import PortfolioManager
from core.risk_models import RiskModelFactory
from core.risk_contribution import RiskContributionCalculator
from core.reporter import RiskReporter
from core.weight_impact_analyzer import WeightImpactAnalyzer
from core.multi_portfolio_analyzer import MultiPortfolioAnalyzer
from utils.logger import system_logger


class RiskManagementSystem:
    """风险管理系统主类"""

    def __init__(self):
        """初始化风险管理系统"""
        self.data_loader = DataLoader()
        self.risk_models = RiskModelFactory.get_all_models()
        self.reporter = RiskReporter()

        # 配置信息
        self.risk_config = config_manager.get_risk_calculation_config()

        system_logger.info("Risk Management System initialized")

    def run_historical_backtest(self, start_date: Optional[datetime] = None,
                               end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        运行历史回溯测试，计算每一天的VaR和ES

        Args:
            start_date: 回溯开始日期
            end_date: 回溯结束日期

        Returns:
            Dict: 回溯测试结果
        """
        system_logger.info("Starting historical backtest analysis")

        try:
            # 1. 加载数据
            system_logger.info("Step 1: Loading data")
            returns_data = self.data_loader.load_historical_returns()
            weights = self.data_loader.load_portfolio_weights()

            if returns_data.empty or weights.empty:
                system_logger.error("Failed to load data")
                return {"success": False, "error": "Data loading failed"}

            # 对齐数据
            aligned_returns, aligned_weights = self.data_loader.align_data_and_weights(returns_data, weights)

            if aligned_returns.empty:
                system_logger.error("No common assets found")
                return {"success": False, "error": "No common assets"}

            # 2. 确定回溯日期范围
            data_config = config_manager.get_data_processing_config()
            window_size = data_config.data_window_size

            if start_date is None:
                # 从有足够数据的第一天开始
                start_date = aligned_returns.index[window_size]
            if end_date is None:
                end_date = aligned_returns.index[-1]

            # 筛选回溯期间的日期
            backtest_dates = aligned_returns.index[
                (aligned_returns.index >= start_date) &
                (aligned_returns.index <= end_date)
            ]

            system_logger.info(f"Backtesting from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            system_logger.info(f"Total backtest days: {len(backtest_dates)}")

            # 3. 创建投资组合管理器
            portfolio_manager = PortfolioManager(aligned_weights, aligned_returns)

            # 4. 逐日计算VaR和ES
            system_logger.info("Step 2: Running daily VaR/ES calculations")
            historical_results = []

            for i, calc_date in enumerate(backtest_dates):
                if i % 50 == 0:  # 每50天输出一次进度
                    progress = (i + 1) / len(backtest_dates) * 100
                    system_logger.info(f"Progress: {progress:.1f}% ({i+1}/{len(backtest_dates)})")

                # 获取该日期的历史数据窗口
                end_idx = aligned_returns.index.get_loc(calc_date)
                start_idx = max(0, end_idx - window_size + 1)
                window_data = aligned_returns.iloc[start_idx:end_idx+1]

                if len(window_data) < data_config.min_data_points:
                    continue

                # 计算该日的VaR和ES
                daily_results = self._calculate_daily_var_es(
                    portfolio_manager, window_data, calc_date
                )

                if daily_results:
                    historical_results.append(daily_results)

            # 5. 保存历史结果
            system_logger.info("Step 3: Saving historical results")
            if historical_results:
                historical_df = pd.DataFrame(historical_results)
                historical_df.set_index('date', inplace=True)

                # 保存到Excel文件
                historical_file = self._save_historical_results(historical_df)

                # 生成回溯测试报告
                backtest_report = self._generate_backtest_report(historical_df, start_date, end_date)
                report_file = self.reporter.save_report(
                    backtest_report,
                    f"historical_backtest_report_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.md"
                )

                # 汇总结果
                final_results = {
                    "success": True,
                    "backtest_period": f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
                    "total_days": len(historical_results),
                    "window_size": window_size,
                    "files_generated": {
                        "historical_results": historical_file,
                        "backtest_report": report_file
                    },
                    "summary_statistics": self._calculate_backtest_summary(historical_df)
                }

                system_logger.info("Historical backtest completed successfully")
                return final_results
            else:
                return {"success": False, "error": "No valid results generated"}

        except Exception as e:
            system_logger.error(f"Historical backtest failed: {e}")
            return {"success": False, "error": str(e)}

    def run_full_analysis(self, calculation_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        运行完整的风险分析

        Args:
            calculation_date: 计算日期，如果为None则使用当前日期

        Returns:
            Dict: 分析结果
        """
        if calculation_date is None:
            calculation_date = datetime.now()

        system_logger.info(f"Starting full risk analysis for {calculation_date.strftime('%Y-%m-%d')}")

        try:
            # 1. 加载数据
            system_logger.info("Step 1: Loading data")
            returns_data = self.data_loader.load_historical_returns()
            weights = self.data_loader.load_portfolio_weights()

            if returns_data.empty or weights.empty:
                system_logger.error("Failed to load data")
                return {"success": False, "error": "Data loading failed"}

            # 对齐数据
            aligned_returns, aligned_weights = self.data_loader.align_data_and_weights(returns_data, weights)

            if aligned_returns.empty:
                system_logger.error("No common assets found")
                return {"success": False, "error": "No common assets"}

            # 2. 创建投资组合管理器
            system_logger.info("Step 2: Creating portfolio manager")
            portfolio_manager = PortfolioManager(aligned_weights, aligned_returns)

            # 3. 计算VaR和ES
            system_logger.info("Step 3: Calculating VaR and ES")
            risk_results = self._calculate_var_es(portfolio_manager, aligned_returns)

            # 4. 计算风险贡献度
            system_logger.info("Step 4: Calculating risk contributions")
            risk_contribution_calculator = RiskContributionCalculator(portfolio_manager)
            risk_contribution_results = risk_contribution_calculator.calculate_risk_contribution_summary(aligned_returns)

            # 5. 生成报告
            system_logger.info("Step 5: Generating reports")
            asset_names = list(aligned_weights.index)

            # 生成综合报告
            comprehensive_report = self.reporter.generate_comprehensive_report(
                risk_results,
                risk_contribution_results,
                asset_names,
                calculation_date
            )

            # 保存报告
            report_file = self.reporter.save_report(comprehensive_report)

            # 6. 保存每日结果
            system_logger.info("Step 6: Saving daily results")
            results_summary = {
                'risk_results': risk_results,
                'risk_contribution': risk_contribution_results,
                'portfolio_summary': portfolio_manager.get_portfolio_summary()
            }

            daily_results_file = self.reporter.save_daily_results(results_summary, calculation_date)

            # 导出JSON结果
            json_file = self.reporter.export_results_to_json(results_summary)

            # 7. 汇总结果
            final_results = {
                "success": True,
                "calculation_date": calculation_date.strftime('%Y-%m-%d'),
                "num_assets": len(asset_names),
                "portfolio_var_parametric": risk_results.get('parametric', {}).get('PORTFOLIO', {}).get('var', 0) * 100,
                "portfolio_es_parametric": risk_results.get('parametric', {}).get('PORTFOLIO', {}).get('es', 0) * 100,
                "files_generated": {
                    "comprehensive_report": report_file,
                    "daily_results": daily_results_file,
                    "json_export": json_file
                },
                "risk_results": risk_results,
                "risk_contribution": risk_contribution_results
            }

            system_logger.info("Full risk analysis completed successfully")
            return final_results

        except Exception as e:
            system_logger.error(f"Risk analysis failed: {e}")
            return {"success": False, "error": str(e)}

    def _calculate_var_es(self, portfolio_manager: PortfolioManager,
                         returns_data: pd.DataFrame) -> Dict[str, Dict[str, Dict[str, float]]]:
        """
        计算所有资产和组合的VaR和ES

        Args:
            portfolio_manager: 投资组合管理器
            returns_data: 收益率数据

        Returns:
            Dict: 风险计算结果
        """
        results = {}

        # 计算组合收益率
        portfolio_returns = portfolio_manager.calculate_portfolio_returns(returns_data)

        for method_name, risk_model in self.risk_models.items():
            system_logger.info(f"Calculating VaR/ES using {method_name} method")
            results[method_name] = {}

            try:
                # 计算组合VaR和ES
                portfolio_var, portfolio_es = risk_model.calculate_var_es(portfolio_returns)
                results[method_name]['PORTFOLIO'] = {
                    'var': portfolio_var,
                    'es': portfolio_es
                }

                # 计算各资产的VaR和ES
                for asset in portfolio_manager.weights.index:
                    if asset in returns_data.columns:
                        asset_returns = returns_data[asset].dropna()
                        if len(asset_returns) > 0:
                            asset_var, asset_es = risk_model.calculate_var_es(asset_returns)
                            results[method_name][asset] = {
                                'var': asset_var,
                                'es': asset_es
                            }
                        else:
                            results[method_name][asset] = {'var': 0.0, 'es': 0.0}
                    else:
                        results[method_name][asset] = {'var': 0.0, 'es': 0.0}

                system_logger.info(f"{method_name} method completed successfully")

            except Exception as e:
                system_logger.error(f"Error in {method_name} method: {e}")
                results[method_name] = {}

        return results

    def run_quick_analysis(self, method: str = 'parametric') -> Dict[str, Any]:
        """
        运行快速风险分析（仅使用指定方法）

        Args:
            method: 风险计算方法

        Returns:
            Dict: 分析结果
        """
        system_logger.info(f"Starting quick analysis using {method} method")

        try:
            # 加载数据
            returns_data = self.data_loader.load_historical_returns()
            weights = self.data_loader.load_portfolio_weights()

            if returns_data.empty or weights.empty:
                return {"success": False, "error": "Data loading failed"}

            # 对齐数据
            aligned_returns, aligned_weights = self.data_loader.align_data_and_weights(returns_data, weights)

            # 创建投资组合管理器
            portfolio_manager = PortfolioManager(aligned_weights, aligned_returns)

            # 计算组合收益率
            portfolio_returns = portfolio_manager.calculate_portfolio_returns(aligned_returns)

            # 使用指定方法计算VaR和ES
            risk_model = RiskModelFactory.create_model(method)
            portfolio_var, portfolio_es = risk_model.calculate_var_es(portfolio_returns)

            # 计算风险贡献度（仅参数法）
            risk_contribution_results = {}
            if method == 'parametric':
                risk_contribution_calculator = RiskContributionCalculator(portfolio_manager)
                risk_contribution_results = risk_contribution_calculator.calculate_risk_contribution_summary(aligned_returns)

            results = {
                "success": True,
                "method": method,
                "portfolio_var": portfolio_var * 100,
                "portfolio_es": portfolio_es * 100,
                "num_assets": len(aligned_weights),
                "risk_contribution": risk_contribution_results
            }

            system_logger.info("Quick analysis completed successfully")
            return results

        except Exception as e:
            system_logger.error(f"Quick analysis failed: {e}")
            return {"success": False, "error": str(e)}

    def _calculate_daily_var_es(self, portfolio_manager: PortfolioManager,
                               window_data: pd.DataFrame, calc_date: datetime) -> Optional[Dict[str, Any]]:
        """
        计算单日的VaR和ES

        Args:
            portfolio_manager: 投资组合管理器
            window_data: 历史数据窗口
            calc_date: 计算日期

        Returns:
            Dict: 该日的风险指标结果
        """
        try:
            # 计算组合收益率
            portfolio_returns = portfolio_manager.calculate_portfolio_returns(window_data)

            if len(portfolio_returns) < 30:  # 最少需要30个数据点
                return None

            daily_result = {
                'date': calc_date,
                'data_points': len(portfolio_returns),
                'portfolio_mean_return': portfolio_returns.mean(),
                'portfolio_volatility': portfolio_returns.std()
            }

            # 使用三种方法计算VaR和ES
            for method_name, risk_model in self.risk_models.items():
                try:
                    var, es = risk_model.calculate_var_es(portfolio_returns)
                    daily_result[f'{method_name}_var'] = var
                    daily_result[f'{method_name}_es'] = es

                except Exception as e:
                    system_logger.warning(f"Error calculating {method_name} for {calc_date}: {e}")
                    daily_result[f'{method_name}_var'] = np.nan
                    daily_result[f'{method_name}_es'] = np.nan

            return daily_result

        except Exception as e:
            system_logger.error(f"Error in daily calculation for {calc_date}: {e}")
            return None

    def _save_historical_results(self, historical_df: pd.DataFrame) -> str:
        """
        保存历史回溯结果

        Args:
            historical_df: 历史结果DataFrame

        Returns:
            str: 保存的文件路径
        """
        from pathlib import Path

        results_dir = Path(self.reporter.config.results_path)
        results_dir.mkdir(parents=True, exist_ok=True)

        # 保存到Excel文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        excel_file = results_dir / f"historical_var_es_backtest_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                # 主要结果
                historical_df.to_excel(writer, sheet_name='Historical_VaR_ES')

                # 统计摘要
                summary_stats = historical_df.describe()
                summary_stats.to_excel(writer, sheet_name='Summary_Statistics')

            system_logger.info(f"Historical results saved to {excel_file}")
            return str(excel_file)

        except Exception as e:
            system_logger.error(f"Error saving historical results: {e}")
            # 回退到CSV格式
            csv_file = results_dir / f"historical_var_es_backtest_{timestamp}.csv"
            historical_df.to_csv(csv_file)
            return str(csv_file)

    def _generate_backtest_report(self, historical_df: pd.DataFrame,
                                start_date: datetime, end_date: datetime) -> str:
        """
        生成回溯测试报告

        Args:
            historical_df: 历史结果DataFrame
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            str: 报告内容
        """
        report_lines = []

        # 报告标题
        report_lines.append("# 历史VaR/ES回溯测试报告")
        report_lines.append("")
        report_lines.append(f"**回溯期间**: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        report_lines.append(f"**总天数**: {len(historical_df)} 天")
        report_lines.append(f"**数据窗口**: {config_manager.get_data_processing_config().data_window_size} 天")
        report_lines.append(f"**置信水平**: {self.risk_config.confidence_level * 100:.1f}%")
        report_lines.append("")

        # 统计摘要
        report_lines.append("## 统计摘要")
        report_lines.append("")

        # VaR统计
        var_columns = [col for col in historical_df.columns if 'var' in col]
        if var_columns:
            report_lines.append("### VaR统计 (%)")
            report_lines.append("")
            report_lines.append("| 方法 | 均值 | 标准差 | 最小值 | 最大值 | 中位数 |")
            report_lines.append("|:-----|:-----|:-------|:-------|:-------|:-------|")

            for col in var_columns:
                if col in historical_df.columns:
                    stats = historical_df[col].describe()
                    method_name = col.replace('_var', '').replace('_', ' ').title()
                    report_lines.append(f"| {method_name} | {stats['mean']*100:.2f} | {stats['std']*100:.2f} | {stats['min']*100:.2f} | {stats['max']*100:.2f} | {stats['50%']*100:.2f} |")

            report_lines.append("")

        # ES统计
        es_columns = [col for col in historical_df.columns if 'es' in col]
        if es_columns:
            report_lines.append("### ES统计 (%)")
            report_lines.append("")
            report_lines.append("| 方法 | 均值 | 标准差 | 最小值 | 最大值 | 中位数 |")
            report_lines.append("|:-----|:-----|:-------|:-------|:-------|:-------|")

            for col in es_columns:
                if col in historical_df.columns:
                    stats = historical_df[col].describe()
                    method_name = col.replace('_es', '').replace('_', ' ').title()
                    report_lines.append(f"| {method_name} | {stats['mean']*100:.2f} | {stats['std']*100:.2f} | {stats['min']*100:.2f} | {stats['max']*100:.2f} | {stats['50%']*100:.2f} |")

            report_lines.append("")

        report_lines.append("---")
        report_lines.append(f"*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")

        return "\n".join(report_lines)

    def _calculate_backtest_summary(self, historical_df: pd.DataFrame) -> Dict[str, Any]:
        """
        计算回溯测试摘要统计

        Args:
            historical_df: 历史结果DataFrame

        Returns:
            Dict: 摘要统计
        """
        summary = {}

        # VaR统计
        var_columns = [col for col in historical_df.columns if 'var' in col]
        for col in var_columns:
            if col in historical_df.columns:
                method_name = col.replace('_var', '')
                summary[f'{method_name}_var_mean'] = historical_df[col].mean()
                summary[f'{method_name}_var_std'] = historical_df[col].std()
                summary[f'{method_name}_var_min'] = historical_df[col].min()
                summary[f'{method_name}_var_max'] = historical_df[col].max()

        return summary

    def update_parameters(self, **kwargs) -> None:
        """
        更新系统参数

        Args:
            **kwargs: 要更新的参数
        """
        for section_key, updates in kwargs.items():
            if isinstance(updates, dict):
                for param_key, value in updates.items():
                    config_manager.update_parameter(section_key, param_key, value)
                    system_logger.info(f"Updated parameter {section_key}.{param_key} = {value}")

    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态信息

        Returns:
            Dict: 系统状态
        """
        try:
            # 检查数据可用性
            returns_data = self.data_loader.load_historical_returns()
            weights = self.data_loader.load_portfolio_weights()

            status = {
                "system_ready": not (returns_data.empty or weights.empty),
                "data_status": {
                    "returns_data_shape": returns_data.shape if not returns_data.empty else (0, 0),
                    "weights_count": len(weights) if not weights.empty else 0,
                    "data_date_range": {
                        "start": returns_data.index.min().strftime('%Y-%m-%d') if not returns_data.empty else None,
                        "end": returns_data.index.max().strftime('%Y-%m-%d') if not returns_data.empty else None
                    } if not returns_data.empty else None
                },
                "configuration": {
                    "confidence_level": self.risk_config.confidence_level,
                    "holding_period": self.risk_config.holding_period,
                    "risk_free_rate": self.risk_config.risk_free_rate
                },
                "available_methods": list(self.risk_models.keys())
            }

            return status

        except Exception as e:
            system_logger.error(f"Error getting system status: {e}")
            return {"system_ready": False, "error": str(e)}

    def run_weight_impact_analysis(self, calculation_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        运行权重影响分析

        Args:
            calculation_date: 计算日期，如果为None则使用当前日期

        Returns:
            Dict: 权重影响分析结果
        """
        if calculation_date is None:
            calculation_date = datetime.now()

        system_logger.info(f"Starting weight impact analysis for {calculation_date.strftime('%Y-%m-%d')}")

        try:
            # 1. 加载数据
            system_logger.info("Step 1: Loading data")
            returns_data = self.data_loader.load_historical_returns()
            weights = self.data_loader.load_portfolio_weights()

            if returns_data.empty or weights.empty:
                system_logger.error("Failed to load data")
                return {"success": False, "error": "Data loading failed"}

            # 对齐数据
            aligned_returns, aligned_weights = self.data_loader.align_data_and_weights(returns_data, weights)

            if aligned_returns.empty:
                system_logger.error("No common assets found")
                return {"success": False, "error": "No common assets"}

            # 2. 创建投资组合管理器
            system_logger.info("Step 2: Creating portfolio manager")
            portfolio_manager = PortfolioManager(aligned_weights, aligned_returns)

            # 3. 运行权重影响分析
            system_logger.info("Step 3: Running comprehensive weight impact analysis")
            weight_analyzer = WeightImpactAnalyzer(portfolio_manager)
            analysis_results = weight_analyzer.run_comprehensive_analysis(aligned_returns, save_results=True)

            if 'error' in analysis_results:
                system_logger.error(f"Weight impact analysis failed: {analysis_results['error']}")
                return {"success": False, "error": analysis_results['error']}

            # 4. 获取分析摘要
            analysis_summary = weight_analyzer.get_analysis_summary(analysis_results)

            # 5. 汇总结果
            final_results = {
                "success": True,
                "calculation_date": calculation_date.strftime('%Y-%m-%d'),
                "num_assets": len(aligned_weights),
                "analysis_summary": analysis_summary,
                "analysis_completed": {
                    "sensitivity_analysis": analysis_summary['sensitivity_analysis_completed'],
                    "marginal_risk_analysis": analysis_summary['marginal_risk_analysis_completed'],
                    "optimization_analysis": analysis_summary['optimization_analysis_completed']
                }
            }

            # 添加关键发现
            if analysis_summary.get('key_findings'):
                final_results['key_findings'] = analysis_summary['key_findings']

            # 添加预期改进
            if analysis_summary.get('expected_improvements'):
                final_results['expected_improvements'] = analysis_summary['expected_improvements']

            # 添加建议数量
            final_results['recommendations_count'] = analysis_summary.get('recommendations_count', 0)

            system_logger.info("Weight impact analysis completed successfully")
            return final_results

        except Exception as e:
            system_logger.error(f"Weight impact analysis failed: {e}")
            return {"success": False, "error": str(e)}

    def run_portfolio_composition_analysis(self, weights: Optional[Union[Dict, str]] = None,
                                         analysis_type: str = "single",
                                         weights_file: Optional[str] = None,
                                         include_predefined: bool = True) -> Dict[str, Any]:
        """
        运行多资产组合风险构成分析

        Args:
            weights: 权重配置（字典或字符串格式）
            analysis_type: 分析类型 ('single', 'multi', 'optimization')
            weights_file: 权重配置文件路径
            include_predefined: 是否包含预定义方案

        Returns:
            Dict: 组合风险构成分析结果
        """
        system_logger.info(f"Starting portfolio composition analysis: {analysis_type}")

        try:
            # 初始化多资产组合分析器
            multi_analyzer = MultiPortfolioAnalyzer()

            if analysis_type == "single":
                # 单个投资组合分析
                if weights is None:
                    # 使用当前投资组合权重
                    weights_data = self.data_loader.load_portfolio_weights()
                    if weights_data.empty:
                        return {"success": False, "error": "No portfolio weights found"}
                    weights = weights_data

                results = multi_analyzer.run_single_portfolio_analysis(
                    weights=weights,
                    scenario_name="Custom Portfolio",
                    save_results=True
                )

            elif analysis_type == "multi":
                # 多投资组合对比分析
                custom_scenarios = {}

                # 从文件加载权重配置
                if weights_file:
                    try:
                        file_scenarios = multi_analyzer.load_weights_from_file(weights_file)
                        custom_scenarios.update(file_scenarios)
                        system_logger.info(f"Loaded {len(file_scenarios)} scenarios from file")
                    except Exception as e:
                        system_logger.warning(f"Failed to load weights from file: {e}")

                # 添加自定义权重
                if weights:
                    custom_scenarios["Custom"] = weights

                results = multi_analyzer.run_multi_portfolio_analysis(
                    scenarios=custom_scenarios if custom_scenarios else None,
                    include_predefined=include_predefined,
                    save_results=True
                )

            elif analysis_type == "optimization":
                # 投资组合优化分析
                if weights is None:
                    # 使用当前投资组合权重
                    weights_data = self.data_loader.load_portfolio_weights()
                    if weights_data.empty:
                        return {"success": False, "error": "No portfolio weights found"}
                    weights = weights_data

                results = multi_analyzer.run_optimization_analysis(
                    current_weights=weights,
                    save_results=True
                )

            else:
                return {"success": False, "error": f"Unknown analysis type: {analysis_type}"}

            if not results.get('success', False):
                system_logger.error(f"Portfolio composition analysis failed: {results.get('error', 'Unknown error')}")
                return results

            # 获取分析摘要
            analysis_summary = multi_analyzer.get_analysis_summary(results)

            # 汇总最终结果
            final_results = {
                "success": True,
                "analysis_type": analysis_type,
                "analysis_summary": analysis_summary
            }

            # 添加类型特定的结果信息
            if analysis_type == "single":
                final_results.update({
                    "scenario_name": results.get('scenario_name'),
                    "portfolio_var": analysis_summary.get('portfolio_var'),
                    "portfolio_es": analysis_summary.get('portfolio_es'),
                    "effective_assets": analysis_summary.get('effective_assets')
                })

            elif analysis_type == "multi":
                final_results.update({
                    "num_scenarios": results.get('num_scenarios'),
                    "best_scenario": analysis_summary.get('best_scenario'),
                    "risk_range": analysis_summary.get('risk_range')
                })

            elif analysis_type == "optimization":
                final_results.update({
                    "optimal_scenario": results.get('optimal_scenario'),
                    "num_scenarios_compared": results.get('num_scenarios_compared'),
                    "improvement_potential": analysis_summary.get('improvement_potential')
                })

            system_logger.info(f"Portfolio composition analysis completed successfully: {analysis_type}")
            return final_results

        except Exception as e:
            system_logger.error(f"Portfolio composition analysis failed: {e}")
            return {"success": False, "error": str(e)}


def create_sample_data():
    """创建示例数据用于测试"""
    system_logger.info("Creating sample data for testing")

    # 创建数据目录
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)

    # 生成示例收益率数据
    import numpy as np

    np.random.seed(42)
    dates = pd.date_range(end=datetime.now(), periods=500, freq='D')
    assets = ['股票A', '股票B', '债券A', '债券B', '商品A']

    # 生成相关的收益率数据
    n_assets = len(assets)
    correlation_matrix = np.array([
        [1.00, 0.60, 0.20, 0.15, 0.30],
        [0.60, 1.00, 0.25, 0.20, 0.35],
        [0.20, 0.25, 1.00, 0.70, 0.10],
        [0.15, 0.20, 0.70, 1.00, 0.05],
        [0.30, 0.35, 0.10, 0.05, 1.00]
    ])

    # 不同资产的波动率
    volatilities = np.array([0.02, 0.025, 0.008, 0.006, 0.03])  # 日波动率

    # 生成收益率
    returns = np.random.multivariate_normal(
        mean=np.zeros(n_assets),
        cov=np.outer(volatilities, volatilities) * correlation_matrix,
        size=len(dates)
    )

    returns_df = pd.DataFrame(returns, index=dates, columns=assets)
    returns_df.to_csv(data_dir / "historical_returns.csv")

    # 生成示例权重
    weights = pd.Series([0.3, 0.25, 0.2, 0.15, 0.1], index=assets)
    weights.to_csv(data_dir / "portfolio_weights.csv", header=['Weight'])

    system_logger.info("Sample data created successfully")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="投资组合风险管理系统")
    parser.add_argument("--mode", choices=["full", "quick", "status", "sample", "backtest", "weight-impact", "portfolio-composition"],
                       default="full", help="运行模式")
    parser.add_argument("--method", choices=["parametric", "historical", "monte_carlo"],
                       default="parametric", help="风险计算方法（快速模式）")
    parser.add_argument("--confidence-level", type=float, help="置信水平")
    parser.add_argument("--holding-period", type=int, help="持有期（天）")
    parser.add_argument("--create-sample", action="store_true", help="创建示例数据")
    parser.add_argument("--start-date", type=str, help="回溯开始日期 (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, help="回溯结束日期 (YYYY-MM-DD)")

    # 多资产组合分析参数
    parser.add_argument("--composition-type", choices=["single", "multi", "optimization"],
                       default="single", help="组合分析类型")
    parser.add_argument("--weights", type=str, help="权重配置 (JSON格式或简单格式)")
    parser.add_argument("--weights-file", type=str, help="权重配置文件路径")
    parser.add_argument("--include-predefined", action="store_true", default=True,
                       help="包含预定义权重方案")

    args = parser.parse_args()

    # 创建示例数据
    if args.create_sample or args.mode == "sample":
        create_sample_data()
        if args.mode == "sample":
            return

    # 更新参数（如果提供）
    if args.confidence_level or args.holding_period:
        updates = {}
        if args.confidence_level:
            updates['confidence_level'] = args.confidence_level
        if args.holding_period:
            updates['holding_period'] = args.holding_period

        config_manager.update_parameter('risk_calculation', 'confidence_level',
                                      args.confidence_level or config_manager.get_risk_calculation_config().confidence_level)
        if args.holding_period:
            config_manager.update_parameter('risk_calculation', 'holding_period', args.holding_period)

    # 初始化系统
    system = RiskManagementSystem()

    if args.mode == "full":
        results = system.run_full_analysis()

    elif args.mode == "quick":
        results = system.run_quick_analysis(args.method)

    elif args.mode == "status":
        results = system.get_system_status()
        results["success"] = True  # 状态检查总是成功的

    elif args.mode == "backtest":
        # 解析日期参数
        start_date = None
        end_date = None

        if args.start_date:
            try:
                start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
            except ValueError:
                print("❌ 无效的开始日期格式，请使用 YYYY-MM-DD")
                sys.exit(1)

        if args.end_date:
            try:
                end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
            except ValueError:
                print("❌ 无效的结束日期格式，请使用 YYYY-MM-DD")
                sys.exit(1)

        results = system.run_historical_backtest(start_date, end_date)

    elif args.mode == "weight-impact":
        results = system.run_weight_impact_analysis()

    elif args.mode == "portfolio-composition":
        results = system.run_portfolio_composition_analysis(
            weights=args.weights,
            analysis_type=args.composition_type,
            weights_file=args.weights_file,
            include_predefined=args.include_predefined
        )

    # 输出结果
    if results.get("success", False):
        print("✅ 分析完成!")

        if args.mode == "full":
            print(f"📊 组合VaR (参数法): {results.get('portfolio_var_parametric', 0):.2f}%")
            print(f"📊 组合ES (参数法): {results.get('portfolio_es_parametric', 0):.2f}%")
            print(f"📁 生成文件:")
            for file_type, file_path in results.get('files_generated', {}).items():
                print(f"   - {file_type}: {file_path}")

        elif args.mode == "quick":
            print(f"📊 组合VaR ({args.method}): {results.get('portfolio_var', 0):.2f}%")
            print(f"📊 组合ES ({args.method}): {results.get('portfolio_es', 0):.2f}%")

        elif args.mode == "status":
            print(f"🔧 系统状态: {'就绪' if results.get('system_ready') else '未就绪'}")
            if 'data_status' in results:
                data_status = results['data_status']
                print(f"📈 数据状态: {data_status['returns_data_shape'][0]} 天, {data_status['weights_count']} 资产")

        elif args.mode == "backtest":
            print(f"📊 回溯测试期间: {results.get('backtest_period', 'N/A')}")
            print(f"📊 总计算天数: {results.get('total_days', 0)}")
            print(f"📁 生成文件:")
            for file_type, file_path in results.get('files_generated', {}).items():
                print(f"   - {file_type}: {file_path}")

            # 显示摘要统计
            if 'summary_statistics' in results:
                summary = results['summary_statistics']
                print(f"📈 VaR统计摘要:")
                for key, value in summary.items():
                    if 'var_mean' in key:
                        method = key.replace('_var_mean', '')
                        print(f"   - {method}: 均值 {value*100:.2f}%")

        elif args.mode == "weight-impact":
            print(f"📊 权重影响分析完成!")
            print(f"📊 分析资产数量: {results.get('num_assets', 0)}")

            # 显示分析完成状态
            analysis_completed = results.get('analysis_completed', {})
            print(f"✅ 敏感性分析: {'完成' if analysis_completed.get('sensitivity_analysis') else '未完成'}")
            print(f"✅ 边际风险分析: {'完成' if analysis_completed.get('marginal_risk_analysis') else '未完成'}")
            print(f"✅ 优化建议分析: {'完成' if analysis_completed.get('optimization_analysis') else '未完成'}")

            # 显示关键发现
            if 'key_findings' in results:
                key_findings = results['key_findings']
                if 'top_risk_contributor' in key_findings:
                    top_contributor = key_findings['top_risk_contributor']
                    print(f"🔍 最大风险贡献资产: {top_contributor['asset']} ({top_contributor['contribution_pct']:.1f}%)")

            # 显示预期改进
            if 'expected_improvements' in results:
                improvements = results['expected_improvements']
                if improvements['var_reduction_pct'] > 0:
                    print(f"📈 预期VaR降低: {improvements['var_reduction_pct']:.1f}%")
                    print(f"📈 预期ES降低: {improvements['es_reduction_pct']:.1f}%")

            # 显示建议数量
            recommendations_count = results.get('recommendations_count', 0)
            if recommendations_count > 0:
                print(f"💡 生成权重调整建议: {recommendations_count} 项")

            print(f"📁 详细分析结果已保存到 results/ 目录")

        elif args.mode == "portfolio-composition":
            print(f"📊 投资组合风险构成分析完成!")
            print(f"📊 分析类型: {results.get('analysis_type', 'unknown')}")

            analysis_summary = results.get('analysis_summary', {})
            analysis_type = analysis_summary.get('analysis_type', 'unknown')

            if analysis_type == 'single_portfolio':
                print(f"📊 投资组合: {analysis_summary.get('scenario_name', 'Unknown')}")
                print(f"📊 组合VaR: {analysis_summary.get('portfolio_var', 0):.2f}%")
                print(f"📊 组合ES: {analysis_summary.get('portfolio_es', 0):.2f}%")
                print(f"📊 有效资产数: {analysis_summary.get('effective_assets', 0):.1f}")

                # 分散化评估
                concentration = analysis_summary.get('concentration_hhi', 0)
                if concentration < 0.2:
                    div_status = "🟢 良好分散"
                elif concentration < 0.4:
                    div_status = "🟡 中等分散"
                else:
                    div_status = "🔴 集中度较高"
                print(f"📊 分散化状态: {div_status}")

            elif analysis_type == 'multi_portfolio':
                print(f"📊 对比方案数量: {analysis_summary.get('num_scenarios', 0)}")
                print(f"📊 最优方案: {analysis_summary.get('best_scenario', 'Unknown')}")

                risk_range = analysis_summary.get('risk_range', {})
                if risk_range:
                    var_range = risk_range.get('var_range', (0, 0))
                    print(f"📊 VaR变化范围: {var_range[0]:.2f}% - {var_range[1]:.2f}%")

            elif analysis_type == 'optimization':
                print(f"📊 最优方案: {analysis_summary.get('optimal_scenario', 'Unknown')}")
                print(f"📊 对比方案数: {analysis_summary.get('num_scenarios_compared', 0)}")

                improvement = analysis_summary.get('improvement_potential', {})
                if improvement:
                    var_improvement = improvement.get('var_improvement_pct', 0)
                    es_improvement = improvement.get('es_improvement_pct', 0)
                    if var_improvement > 0:
                        print(f"📈 VaR改进潜力: {var_improvement:.1f}%")
                        print(f"📈 ES改进潜力: {es_improvement:.1f}%")
                    else:
                        print(f"📊 当前配置已接近最优")

            print(f"📁 详细分析结果已保存到 results/ 目录")
    else:
        print(f"❌ 分析失败: {results.get('error', '未知错误')}")
        sys.exit(1)


if __name__ == "__main__":
    main()
