#!/usr/bin/env python3
"""
权重影响分析功能测试脚本
验证新增的权重敏感性分析、边际风险分析和风险预算优化功能
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from main import RiskManagementSystem
from core.data_loader import DataLoader
from core.portfolio_manager import PortfolioManager
from core.weight_impact_analyzer import WeightImpactAnalyzer


def test_weight_impact_analysis():
    """测试权重影响分析功能"""
    print("🧪 开始测试权重影响分析功能...")
    
    try:
        # 1. 初始化系统
        print("\n1. 初始化风险管理系统...")
        risk_system = RiskManagementSystem()
        
        # 2. 检查系统状态
        print("2. 检查系统状态...")
        status = risk_system.get_system_status()
        if not status.get('system_ready', False):
            print("❌ 系统未就绪，请先创建示例数据")
            return False
        
        print(f"✅ 系统就绪 - 数据形状: {status['data_status']['returns_data_shape']}")
        
        # 3. 运行权重影响分析
        print("3. 运行权重影响分析...")
        results = risk_system.run_weight_impact_analysis()
        
        if not results.get('success', False):
            print(f"❌ 权重影响分析失败: {results.get('error', '未知错误')}")
            return False
        
        # 4. 验证分析结果
        print("4. 验证分析结果...")
        
        # 检查分析完成状态
        analysis_completed = results.get('analysis_completed', {})
        print(f"   - 敏感性分析: {'✅' if analysis_completed.get('sensitivity_analysis') else '❌'}")
        print(f"   - 边际风险分析: {'✅' if analysis_completed.get('marginal_risk_analysis') else '❌'}")
        print(f"   - 优化建议分析: {'✅' if analysis_completed.get('optimization_analysis') else '❌'}")
        
        # 检查关键发现
        if 'key_findings' in results:
            key_findings = results['key_findings']
            if 'top_risk_contributor' in key_findings:
                top_contributor = key_findings['top_risk_contributor']
                print(f"   - 最大风险贡献资产: {top_contributor['asset']} ({top_contributor['contribution_pct']:.1f}%)")
        
        # 检查预期改进
        if 'expected_improvements' in results:
            improvements = results['expected_improvements']
            print(f"   - 预期VaR降低: {improvements['var_reduction_pct']:.1f}%")
            print(f"   - 预期ES降低: {improvements['es_reduction_pct']:.1f}%")
        
        # 检查建议数量
        recommendations_count = results.get('recommendations_count', 0)
        print(f"   - 权重调整建议: {recommendations_count} 项")
        
        print("\n✅ 权重影响分析功能测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False


def test_individual_modules():
    """测试各个模块的独立功能"""
    print("\n🧪 测试各个模块的独立功能...")
    
    try:
        # 加载数据
        data_loader = DataLoader()
        returns_data = data_loader.load_historical_returns()
        weights = data_loader.load_portfolio_weights()
        
        if returns_data.empty or weights.empty:
            print("❌ 无法加载数据")
            return False
        
        # 对齐数据
        aligned_returns, aligned_weights = data_loader.align_data_and_weights(returns_data, weights)
        
        # 创建投资组合管理器
        portfolio_manager = PortfolioManager(aligned_weights, aligned_returns)
        
        # 测试权重影响分析器
        print("1. 测试权重影响分析器...")
        weight_analyzer = WeightImpactAnalyzer(portfolio_manager)
        
        # 获取分析摘要（不保存文件）
        analysis_results = weight_analyzer.run_comprehensive_analysis(aligned_returns, save_results=False)
        
        if 'error' in analysis_results:
            print(f"❌ 权重影响分析失败: {analysis_results['error']}")
            return False
        
        # 验证分析结果结构
        required_keys = ['sensitivity_analysis', 'marginal_risk_analysis', 'risk_budget_analysis', 
                        'optimization_recommendations', 'simulation_results']
        
        for key in required_keys:
            if key in analysis_results:
                print(f"   ✅ {key} - 完成")
            else:
                print(f"   ❌ {key} - 缺失")
        
        # 获取分析摘要
        summary = weight_analyzer.get_analysis_summary(analysis_results)
        print(f"   - 投资组合资产数量: {summary['portfolio_assets_count']}")
        print(f"   - 建议数量: {summary['recommendations_count']}")
        
        print("\n✅ 各个模块独立功能测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 模块测试过程中发生错误: {e}")
        return False


def test_data_validation():
    """测试数据验证功能"""
    print("\n🧪 测试数据验证功能...")
    
    try:
        # 创建测试数据
        dates = pd.date_range(end=pd.Timestamp.now(), periods=100, freq='D')
        assets = ['资产A', '资产B', '资产C']
        
        # 生成收益率数据
        np.random.seed(42)
        returns = np.random.normal(0, 0.02, (len(dates), len(assets)))
        returns_df = pd.DataFrame(returns, index=dates, columns=assets)
        
        # 生成权重数据
        weights = pd.Series([0.4, 0.35, 0.25], index=assets)
        
        # 创建投资组合管理器
        portfolio_manager = PortfolioManager(weights, returns_df)
        
        # 测试权重影响分析器
        weight_analyzer = WeightImpactAnalyzer(portfolio_manager)
        
        # 运行分析（不保存文件）
        analysis_results = weight_analyzer.run_comprehensive_analysis(returns_df, save_results=False)
        
        if 'error' in analysis_results:
            print(f"❌ 测试数据分析失败: {analysis_results['error']}")
            return False
        
        print("✅ 数据验证功能测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 数据验证测试过程中发生错误: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 权重影响分析功能测试套件")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("权重影响分析功能", test_weight_impact_analysis),
        ("各个模块独立功能", test_individual_modules),
        ("数据验证功能", test_data_validation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    # 测试结果摘要
    print("\n" + "=" * 50)
    print(f"📊 测试结果摘要: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! 权重影响分析功能正常工作")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
