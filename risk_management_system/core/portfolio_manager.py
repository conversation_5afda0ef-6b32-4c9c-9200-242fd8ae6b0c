"""
投资组合管理模块
管理投资组合权重，计算组合收益率和模拟权重调整
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from config.config_manager import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("PortfolioManager")


class PortfolioManager:
    """投资组合管理器"""
    
    def __init__(self, weights: pd.Series, returns_data: pd.DataFrame):
        """
        初始化投资组合管理器
        
        Args:
            weights: 投资组合权重
            returns_data: 历史收益率数据
        """
        self.weights = weights.copy()
        self.returns_data = returns_data.copy()
        self.config = config_manager.get_risk_calculation_config()
        
        # 验证权重和数据的一致性
        self._validate_inputs()
        
        logger.info(f"Initialized portfolio with {len(self.weights)} assets")
    
    def _validate_inputs(self) -> None:
        """验证输入数据"""
        # 检查权重和收益率数据的资产是否匹配
        common_assets = set(self.weights.index) & set(self.returns_data.columns)
        if len(common_assets) != len(self.weights):
            logger.warning("Weights and returns data have different assets")
            
        # 确保权重和为1
        if abs(self.weights.sum() - 1.0) > 1e-6:
            logger.warning(f"Weights sum to {self.weights.sum():.6f}, normalizing to 1.0")
            self.weights = self.weights / self.weights.sum()
    
    def calculate_portfolio_returns(self, returns_data: Optional[pd.DataFrame] = None) -> pd.Series:
        """
        计算投资组合收益率
        
        Args:
            returns_data: 收益率数据，如果为None则使用初始化时的数据
            
        Returns:
            Series: 投资组合收益率时间序列
        """
        if returns_data is None:
            returns_data = self.returns_data
        
        # 确保权重和收益率数据的资产匹配
        common_assets = returns_data.columns.intersection(self.weights.index)
        if len(common_assets) == 0:
            logger.error("No common assets between weights and returns data")
            return pd.Series()
        
        # 计算组合收益率
        aligned_returns = returns_data[common_assets]
        aligned_weights = self.weights[common_assets]
        
        portfolio_returns = (aligned_returns * aligned_weights).sum(axis=1)
        
        logger.debug(f"Calculated portfolio returns for {len(portfolio_returns)} periods")
        return portfolio_returns
    
    def get_asset_contributions(self, returns_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        计算各资产对组合收益率的贡献
        
        Args:
            returns_data: 收益率数据
            
        Returns:
            DataFrame: 各资产的收益率贡献
        """
        if returns_data is None:
            returns_data = self.returns_data
        
        common_assets = returns_data.columns.intersection(self.weights.index)
        aligned_returns = returns_data[common_assets]
        aligned_weights = self.weights[common_assets]
        
        # 计算各资产贡献
        contributions = aligned_returns.multiply(aligned_weights, axis=1)
        
        return contributions
    
    def simulate_weight_change(self, asset: str, delta_weight: float) -> pd.Series:
        """
        模拟权重变化
        
        Args:
            asset: 资产名称
            delta_weight: 权重变化量
            
        Returns:
            Series: 调整后的权重
        """
        if asset not in self.weights.index:
            logger.error(f"Asset {asset} not found in portfolio")
            return self.weights.copy()
        
        new_weights = self.weights.copy()
        
        # 调整目标资产权重
        new_weights[asset] += delta_weight
        
        # 确保权重非负
        if new_weights[asset] < 0:
            logger.warning(f"Adjusted weight for {asset} would be negative, setting to 0")
            delta_weight = -self.weights[asset]
            new_weights[asset] = 0
        
        # 按比例调整其他资产权重以保持权重和为1
        other_assets = new_weights.index[new_weights.index != asset]
        if len(other_assets) > 0 and new_weights[other_assets].sum() > 0:
            adjustment_factor = (1 - new_weights[asset]) / new_weights[other_assets].sum()
            new_weights[other_assets] *= adjustment_factor
        
        # 验证权重和
        if abs(new_weights.sum() - 1.0) > 1e-6:
            logger.warning(f"Weight adjustment resulted in sum = {new_weights.sum():.6f}")
            new_weights = new_weights / new_weights.sum()
        
        return new_weights
    
    def remove_asset(self, asset: str) -> pd.Series:
        """
        移除资产（将权重设为0）
        
        Args:
            asset: 要移除的资产名称
            
        Returns:
            Series: 调整后的权重
        """
        if asset not in self.weights.index:
            logger.error(f"Asset {asset} not found in portfolio")
            return self.weights.copy()
        
        new_weights = self.weights.copy()
        removed_weight = new_weights[asset]
        new_weights[asset] = 0
        
        # 将移除的权重按比例分配给其他资产
        other_assets = new_weights.index[new_weights.index != asset]
        if len(other_assets) > 0 and new_weights[other_assets].sum() > 0:
            adjustment_factor = 1 / new_weights[other_assets].sum()
            new_weights[other_assets] *= adjustment_factor
        
        logger.info(f"Removed asset {asset} with weight {removed_weight:.4f}")
        return new_weights
    
    def add_asset(self, asset: str, weight: float) -> pd.Series:
        """
        添加新资产
        
        Args:
            asset: 新资产名称
            weight: 新资产权重
            
        Returns:
            Series: 调整后的权重
        """
        if weight <= 0 or weight >= 1:
            logger.error(f"Invalid weight for new asset: {weight}")
            return self.weights.copy()
        
        new_weights = self.weights.copy()
        
        # 按比例减少现有资产权重
        adjustment_factor = (1 - weight)
        new_weights *= adjustment_factor
        
        # 添加新资产
        new_weights[asset] = weight
        
        logger.info(f"Added asset {asset} with weight {weight:.4f}")
        return new_weights
    
    def calculate_portfolio_statistics(self, returns_data: Optional[pd.DataFrame] = None) -> Dict[str, float]:
        """
        计算投资组合统计指标
        
        Args:
            returns_data: 收益率数据
            
        Returns:
            Dict: 包含各种统计指标的字典
        """
        portfolio_returns = self.calculate_portfolio_returns(returns_data)
        
        if portfolio_returns.empty:
            return {}
        
        stats = {
            'mean_return': portfolio_returns.mean(),
            'volatility': portfolio_returns.std(),
            'skewness': portfolio_returns.skew(),
            'kurtosis': portfolio_returns.kurtosis(),
            'min_return': portfolio_returns.min(),
            'max_return': portfolio_returns.max(),
            'sharpe_ratio': (portfolio_returns.mean() - self.config.risk_free_rate) / portfolio_returns.std()
        }
        
        return stats
    
    def get_correlation_matrix(self, returns_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        计算资产相关性矩阵
        
        Args:
            returns_data: 收益率数据
            
        Returns:
            DataFrame: 相关性矩阵
        """
        if returns_data is None:
            returns_data = self.returns_data
        
        common_assets = returns_data.columns.intersection(self.weights.index)
        aligned_returns = returns_data[common_assets]
        
        correlation_matrix = aligned_returns.corr()
        
        return correlation_matrix
    
    def get_covariance_matrix(self, returns_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        计算资产协方差矩阵
        
        Args:
            returns_data: 收益率数据
            
        Returns:
            DataFrame: 协方差矩阵
        """
        if returns_data is None:
            returns_data = self.returns_data
        
        common_assets = returns_data.columns.intersection(self.weights.index)
        aligned_returns = returns_data[common_assets]
        
        covariance_matrix = aligned_returns.cov()
        
        return covariance_matrix
    
    def calculate_portfolio_variance(self, weights: Optional[pd.Series] = None, 
                                   returns_data: Optional[pd.DataFrame] = None) -> float:
        """
        计算投资组合方差
        
        Args:
            weights: 投资组合权重，如果为None则使用当前权重
            returns_data: 收益率数据
            
        Returns:
            float: 投资组合方差
        """
        if weights is None:
            weights = self.weights
        
        covariance_matrix = self.get_covariance_matrix(returns_data)
        
        # 确保权重和协方差矩阵的资产匹配
        common_assets = covariance_matrix.index.intersection(weights.index)
        aligned_cov = covariance_matrix.loc[common_assets, common_assets]
        aligned_weights = weights[common_assets]
        
        # 计算组合方差: w^T * Σ * w
        portfolio_variance = np.dot(aligned_weights.values, 
                                  np.dot(aligned_cov.values, aligned_weights.values))
        
        return portfolio_variance
    
    def update_weights(self, new_weights: pd.Series) -> None:
        """
        更新投资组合权重
        
        Args:
            new_weights: 新的权重
        """
        # 验证新权重
        if abs(new_weights.sum() - 1.0) > 1e-6:
            logger.warning(f"New weights sum to {new_weights.sum():.6f}, normalizing")
            new_weights = new_weights / new_weights.sum()
        
        self.weights = new_weights.copy()
        logger.info("Portfolio weights updated")
    
    def get_portfolio_summary(self) -> Dict[str, any]:
        """
        获取投资组合摘要信息
        
        Returns:
            Dict: 投资组合摘要
        """
        stats = self.calculate_portfolio_statistics()
        
        summary = {
            'num_assets': len(self.weights),
            'weights': self.weights.to_dict(),
            'statistics': stats,
            'largest_position': self.weights.max(),
            'smallest_position': self.weights.min(),
            'concentration': (self.weights ** 2).sum()  # Herfindahl指数
        }
        
        return summary
