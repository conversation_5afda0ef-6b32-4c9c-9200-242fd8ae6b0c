"""
报告生成模块
负责格式化和输出风险管理分析报告
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import json

from config.config_manager import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("Reporter")


class RiskReporter:
    """风险报告生成器"""
    
    def __init__(self):
        self.config = config_manager.get_output_config()
        self.risk_config = config_manager.get_risk_calculation_config()
        
        # 创建输出目录
        Path(self.config.results_path).mkdir(parents=True, exist_ok=True)
        
    def generate_var_es_table(self, risk_results: Dict[str, Dict[str, Dict[str, float]]], 
                            asset_names: List[str]) -> str:
        """
        生成VaR和ES表格
        
        Args:
            risk_results: 风险计算结果
            asset_names: 资产名称列表
            
        Returns:
            str: Markdown格式的表格
        """
        # 表头
        headers = [
            "产品名称",
            "参数法 (GARCH) VaR (%)",
            "参数法 (GARCH) ES (%)",
            "历史模拟法 VaR (%)",
            "历史模拟法 ES (%)",
            "蒙特卡洛模拟法 VaR (%)",
            "蒙特卡洛模拟法 ES (%)"
        ]
        
        # 构建表格内容
        table_lines = []
        table_lines.append("| " + " | ".join(headers) + " |")
        table_lines.append("| " + " | ".join([":---"] * len(headers)) + " |")
        
        # 添加各资产的数据
        for asset in asset_names:
            row = [asset]
            
            # 参数法
            if 'parametric' in risk_results and asset in risk_results['parametric']:
                var_param = risk_results['parametric'][asset].get('var', 0) * 100
                es_param = risk_results['parametric'][asset].get('es', 0) * 100
                row.extend([f"{var_param:.2f}", f"{es_param:.2f}"])
            else:
                row.extend(["--", "--"])
            
            # 历史模拟法
            if 'historical' in risk_results and asset in risk_results['historical']:
                var_hist = risk_results['historical'][asset].get('var', 0) * 100
                es_hist = risk_results['historical'][asset].get('es', 0) * 100
                row.extend([f"{var_hist:.2f}", f"{es_hist:.2f}"])
            else:
                row.extend(["--", "--"])
            
            # 蒙特卡洛法
            if 'monte_carlo' in risk_results and asset in risk_results['monte_carlo']:
                var_mc = risk_results['monte_carlo'][asset].get('var', 0) * 100
                es_mc = risk_results['monte_carlo'][asset].get('es', 0) * 100
                row.extend([f"{var_mc:.2f}", f"{es_mc:.2f}"])
            else:
                row.extend(["--", "--"])
            
            table_lines.append("| " + " | ".join(row) + " |")
        
        # 添加组合总体数据
        if 'PORTFOLIO' in risk_results.get('parametric', {}):
            row = ["**当前组合**"]
            
            # 参数法
            if 'parametric' in risk_results and 'PORTFOLIO' in risk_results['parametric']:
                var_param = risk_results['parametric']['PORTFOLIO'].get('var', 0) * 100
                es_param = risk_results['parametric']['PORTFOLIO'].get('es', 0) * 100
                row.extend([f"**{var_param:.2f}**", f"**{es_param:.2f}**"])
            else:
                row.extend(["**--**", "**--**"])
            
            # 历史模拟法
            if 'historical' in risk_results and 'PORTFOLIO' in risk_results['historical']:
                var_hist = risk_results['historical']['PORTFOLIO'].get('var', 0) * 100
                es_hist = risk_results['historical']['PORTFOLIO'].get('es', 0) * 100
                row.extend([f"**{var_hist:.2f}**", f"**{es_hist:.2f}**"])
            else:
                row.extend(["**--**", "**--**"])
            
            # 蒙特卡洛法
            if 'monte_carlo' in risk_results and 'PORTFOLIO' in risk_results['monte_carlo']:
                var_mc = risk_results['monte_carlo']['PORTFOLIO'].get('var', 0) * 100
                es_mc = risk_results['monte_carlo']['PORTFOLIO'].get('es', 0) * 100
                row.extend([f"**{var_mc:.2f}**", f"**{es_mc:.2f}**"])
            else:
                row.extend(["**--**", "**--**"])
            
            table_lines.append("| " + " | ".join(row) + " |")
        
        return "\n".join(table_lines)
    
    def generate_risk_contribution_table(self, risk_contribution_results: Dict[str, Dict[str, float]], 
                                       asset_names: List[str]) -> str:
        """
        生成风险贡献度表格
        
        Args:
            risk_contribution_results: 风险贡献度结果
            asset_names: 资产名称列表
            
        Returns:
            str: Markdown格式的表格
        """
        # 表头
        headers = ["产品名称", "增量VaR (iVaR) (%)", "边际VaR (MVaR) (%)"]
        
        # 构建表格内容
        table_lines = []
        table_lines.append("| " + " | ".join(headers) + " |")
        table_lines.append("| " + " | ".join([":---"] * len(headers)) + " |")
        
        # 添加各资产的数据
        for asset in asset_names:
            if asset in risk_contribution_results:
                ivar = risk_contribution_results[asset].get('incremental_var', 0) * 100
                mvar = risk_contribution_results[asset].get('marginal_var', 0) * 100
                row = [asset, f"{ivar:.2f}", f"{mvar:.2f}"]
            else:
                row = [asset, "--", "--"]
            
            table_lines.append("| " + " | ".join(row) + " |")
        
        # 添加组合总体信息
        if 'PORTFOLIO' in risk_contribution_results:
            portfolio_data = risk_contribution_results['PORTFOLIO']
            total_var = portfolio_data.get('total_var', 0) * 100
            sum_ivar = portfolio_data.get('sum_incremental_var', 0) * 100
            
            row = ["**当前组合**", f"**{sum_ivar:.2f}**", f"**{total_var:.2f}**"]
            table_lines.append("| " + " | ".join(row) + " |")
        
        return "\n".join(table_lines)
    
    def generate_comprehensive_report(self, risk_results: Dict[str, Dict[str, Dict[str, float]]], 
                                    risk_contribution_results: Dict[str, Dict[str, float]], 
                                    asset_names: List[str],
                                    calculation_date: Optional[datetime] = None) -> str:
        """
        生成综合风险报告
        
        Args:
            risk_results: 风险计算结果
            risk_contribution_results: 风险贡献度结果
            asset_names: 资产名称列表
            calculation_date: 计算日期
            
        Returns:
            str: 完整的Markdown报告
        """
        if calculation_date is None:
            calculation_date = datetime.now()
        
        report_lines = []
        
        # 报告标题和基本信息
        report_lines.append("# 投资组合风险管理分析报告")
        report_lines.append("")
        report_lines.append(f"**计算日期**: {calculation_date.strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**置信水平**: {self.risk_config.confidence_level * 100:.1f}%")
        report_lines.append(f"**持有期**: {self.risk_config.holding_period} 天")
        report_lines.append("")
        
        # 执行摘要
        report_lines.append("## 执行摘要")
        report_lines.append("")
        
        # 获取组合VaR信息
        portfolio_vars = {}
        for method in ['parametric', 'historical', 'monte_carlo']:
            if method in risk_results and 'PORTFOLIO' in risk_results[method]:
                portfolio_vars[method] = risk_results[method]['PORTFOLIO'].get('var', 0) * 100
        
        if portfolio_vars:
            avg_var = np.mean(list(portfolio_vars.values()))
            max_var = max(portfolio_vars.values())
            min_var = min(portfolio_vars.values())
            
            report_lines.append(f"- **组合平均VaR**: {avg_var:.2f}%")
            report_lines.append(f"- **VaR范围**: {min_var:.2f}% - {max_var:.2f}%")
            report_lines.append(f"- **资产数量**: {len(asset_names)}")
        
        report_lines.append("")
        
        # VaR和ES表格
        report_lines.append("## 表格一：各产品及组合的VaR与ES（百分比损失）")
        report_lines.append("")
        var_es_table = self.generate_var_es_table(risk_results, asset_names)
        report_lines.append(var_es_table)
        report_lines.append("")
        
        # 风险贡献度表格
        report_lines.append("## 表格二：基于参数法(GARCH)的iVaR与MVaR（百分比贡献）")
        report_lines.append("")
        risk_contrib_table = self.generate_risk_contribution_table(risk_contribution_results, asset_names)
        report_lines.append(risk_contrib_table)
        report_lines.append("")
        
        # 风险分析
        report_lines.append("## 风险分析")
        report_lines.append("")
        
        # 方法比较
        if len(portfolio_vars) > 1:
            report_lines.append("### 方法比较")
            report_lines.append("")
            for method, var_value in portfolio_vars.items():
                method_name = {
                    'parametric': '参数法(GARCH)',
                    'historical': '历史模拟法',
                    'monte_carlo': '蒙特卡洛模拟法'
                }.get(method, method)
                report_lines.append(f"- **{method_name}**: {var_value:.2f}%")
            report_lines.append("")
        
        # 风险贡献度分析
        if risk_contribution_results:
            report_lines.append("### 风险贡献度分析")
            report_lines.append("")
            
            # 找出风险贡献最大的资产
            asset_contributions = []
            for asset in asset_names:
                if asset in risk_contribution_results:
                    contrib = risk_contribution_results[asset].get('var_contribution_pct', 0)
                    asset_contributions.append((asset, contrib))
            
            if asset_contributions:
                asset_contributions.sort(key=lambda x: x[1], reverse=True)
                top_contributors = asset_contributions[:3]
                
                report_lines.append("**主要风险贡献者**:")
                for i, (asset, contrib) in enumerate(top_contributors, 1):
                    report_lines.append(f"{i}. {asset}: {contrib:.2f}%")
                report_lines.append("")
        
        # 技术说明
        report_lines.append("## 技术说明")
        report_lines.append("")
        report_lines.append("### 计算方法")
        report_lines.append("")
        report_lines.append("1. **参数法(GARCH)**: 基于GARCH模型估计动态波动率，假设收益率服从正态分布或t分布")
        report_lines.append("2. **历史模拟法**: 直接使用历史收益率数据的经验分布")
        report_lines.append("3. **蒙特卡洛模拟法**: 基于几何布朗运动模型生成模拟路径")
        report_lines.append("")
        report_lines.append("### 风险指标定义")
        report_lines.append("")
        report_lines.append("- **VaR (风险价值)**: 在给定置信水平下，投资组合可能遭受的最大损失")
        report_lines.append("- **ES (期望损失)**: 当损失超过VaR时的预期平均损失")
        report_lines.append("- **iVaR (增量VaR)**: 移除或增加某资产后组合VaR的变化")
        report_lines.append("- **MVaR (边际VaR)**: 资产权重微小变化对组合VaR的影响率")
        report_lines.append("")
        
        # 免责声明
        report_lines.append("## 免责声明")
        report_lines.append("")
        report_lines.append("本报告仅供参考，不构成投资建议。风险度量结果基于历史数据和统计模型，")
        report_lines.append("实际损失可能超过模型预测。投资者应当根据自身情况谨慎决策。")
        report_lines.append("")
        
        # 报告生成信息
        report_lines.append("---")
        report_lines.append(f"*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        
        return "\n".join(report_lines)
    
    def save_daily_results(self, results: Dict[str, Any], date: datetime) -> str:
        """
        保存每日计算结果到Excel文件
        
        Args:
            results: 计算结果
            date: 计算日期
            
        Returns:
            str: 保存的文件路径
        """
        file_path = Path(self.config.results_path) / self.config.daily_results_file
        
        # 准备数据
        daily_data = {
            'Date': date.strftime('%Y-%m-%d'),
            'Confidence_Level': self.risk_config.confidence_level,
            'Holding_Period': self.risk_config.holding_period
        }
        
        # 添加VaR和ES结果
        if 'risk_results' in results:
            for method in ['parametric', 'historical', 'monte_carlo']:
                if method in results['risk_results'] and 'PORTFOLIO' in results['risk_results'][method]:
                    portfolio_data = results['risk_results'][method]['PORTFOLIO']
                    daily_data[f'{method}_var'] = portfolio_data.get('var', 0)
                    daily_data[f'{method}_es'] = portfolio_data.get('es', 0)
        
        # 添加风险贡献度结果
        if 'risk_contribution' in results and 'PORTFOLIO' in results['risk_contribution']:
            portfolio_contrib = results['risk_contribution']['PORTFOLIO']
            daily_data['total_var'] = portfolio_contrib.get('total_var', 0)
            daily_data['sum_component_var'] = portfolio_contrib.get('sum_component_var', 0)
        
        # 转换为DataFrame
        df_new = pd.DataFrame([daily_data])
        
        # 如果文件存在，追加数据；否则创建新文件
        if file_path.exists():
            try:
                df_existing = pd.read_excel(file_path)
                df_combined = pd.concat([df_existing, df_new], ignore_index=True)
                # 去重（基于日期）
                df_combined = df_combined.drop_duplicates(subset=['Date'], keep='last')
                df_combined = df_combined.sort_values('Date')
            except Exception as e:
                logger.warning(f"Error reading existing file: {e}, creating new file")
                df_combined = df_new
        else:
            df_combined = df_new
        
        # 保存到Excel
        try:
            df_combined.to_excel(file_path, index=False)
            logger.info(f"Daily results saved to {file_path}")
        except Exception as e:
            logger.error(f"Error saving daily results: {e}")
        
        return str(file_path)
    
    def save_report(self, report_content: str, filename: Optional[str] = None) -> str:
        """
        保存报告到文件
        
        Args:
            report_content: 报告内容
            filename: 文件名，如果为None则自动生成
            
        Returns:
            str: 保存的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"risk_report_{timestamp}.md"
        
        file_path = Path(self.config.results_path) / filename
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            logger.info(f"Report saved to {file_path}")
        except Exception as e:
            logger.error(f"Error saving report: {e}")
        
        return str(file_path)
    
    def export_results_to_json(self, results: Dict[str, Any], filename: Optional[str] = None) -> str:
        """
        导出结果到JSON文件
        
        Args:
            results: 计算结果
            filename: 文件名
            
        Returns:
            str: 保存的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"risk_results_{timestamp}.json"
        
        file_path = Path(self.config.results_path) / filename
        
        try:
            # 转换numpy类型为Python原生类型
            def convert_numpy(obj):
                if isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, dict):
                    return {key: convert_numpy(value) for key, value in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy(item) for item in obj]
                else:
                    return obj
            
            converted_results = convert_numpy(results)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(converted_results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Results exported to JSON: {file_path}")
        except Exception as e:
            logger.error(f"Error exporting to JSON: {e}")
        
        return str(file_path)
