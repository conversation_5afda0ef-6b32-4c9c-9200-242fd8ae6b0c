"""
权重配置管理器
支持多种权重输入格式和验证，管理多个权重配置方案
"""

import pandas as pd
import numpy as np
import yaml
import json
from pathlib import Path
from typing import Dict, List, Union, Optional, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

from config.config_manager import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("WeightConfigManager")


class WeightConfigManager:
    """权重配置管理器"""
    
    def __init__(self):
        """初始化权重配置管理器"""
        self.config = config_manager.get_config()
        self.composition_config = self.config.get('portfolio_composition', {})
        self.custom_weights_config = self.config.get('custom_weights', {})
        
        # 配置参数
        self.weight_tolerance = self.composition_config.get('weight_tolerance', 0.001)
        self.auto_normalize = self.composition_config.get('auto_normalize', True)
        self.min_weight_threshold = self.composition_config.get('min_weight_threshold', 0.001)
        self.max_scenarios = self.composition_config.get('max_scenarios', 10)
        
        logger.info("Weight config manager initialized")
    
    def validate_weights(self, weights: Union[Dict[str, float], pd.Series], 
                        normalize: Optional[bool] = None) -> Tuple[pd.Series, bool]:
        """
        验证和标准化权重
        
        Args:
            weights: 权重配置（字典或Series）
            normalize: 是否标准化，如果为None则使用配置默认值
            
        Returns:
            Tuple: (标准化后的权重Series, 是否进行了标准化)
        """
        if normalize is None:
            normalize = self.auto_normalize
        
        # 转换为Series
        if isinstance(weights, dict):
            weights_series = pd.Series(weights)
        elif isinstance(weights, pd.Series):
            weights_series = weights.copy()
        else:
            raise ValueError("权重必须是字典或pandas Series格式")
        
        # 检查负权重
        if (weights_series < 0).any():
            negative_assets = weights_series[weights_series < 0].index.tolist()
            raise ValueError(f"权重不能为负数，发现负权重资产: {negative_assets}")
        
        # 检查权重总和
        total_weight = weights_series.sum()
        is_normalized = False
        
        if abs(total_weight - 1.0) > self.weight_tolerance:
            if normalize:
                logger.info(f"权重总和为 {total_weight:.4f}，自动标准化为1.0")
                weights_series = weights_series / total_weight
                is_normalized = True
            else:
                raise ValueError(f"权重总和为 {total_weight:.4f}，不等于1.0（容差: {self.weight_tolerance}）")
        
        # 过滤小权重
        small_weights = weights_series[weights_series < self.min_weight_threshold]
        if len(small_weights) > 0:
            logger.warning(f"发现 {len(small_weights)} 个小权重资产（< {self.min_weight_threshold}）: {small_weights.index.tolist()}")
        
        logger.info(f"权重验证完成，资产数量: {len(weights_series)}")
        return weights_series, is_normalized
    
    def parse_weight_input(self, weight_input: Union[str, Dict, List]) -> Dict[str, float]:
        """
        解析多种格式的权重输入
        
        Args:
            weight_input: 权重输入（字符串、字典、列表等）
            
        Returns:
            Dict: 解析后的权重字典
        """
        if isinstance(weight_input, dict):
            return weight_input
        
        elif isinstance(weight_input, str):
            # 尝试解析JSON格式
            try:
                return json.loads(weight_input)
            except json.JSONDecodeError:
                pass
            
            # 尝试解析简单格式 "asset1:0.3,asset2:0.7"
            try:
                weights = {}
                pairs = weight_input.split(',')
                for pair in pairs:
                    asset, weight = pair.split(':')
                    weights[asset.strip()] = float(weight.strip())
                return weights
            except (ValueError, IndexError):
                raise ValueError(f"无法解析权重输入格式: {weight_input}")
        
        elif isinstance(weight_input, list):
            # 假设是等权重
            if all(isinstance(item, str) for item in weight_input):
                equal_weight = 1.0 / len(weight_input)
                return {asset: equal_weight for asset in weight_input}
            else:
                raise ValueError("列表格式权重输入必须是资产名称列表")
        
        else:
            raise ValueError(f"不支持的权重输入类型: {type(weight_input)}")
    
    def load_predefined_scenarios(self) -> Dict[str, pd.Series]:
        """
        加载预定义的权重配置方案
        
        Returns:
            Dict: 方案名称到权重Series的映射
        """
        scenarios = {}
        predefined_scenarios = self.custom_weights_config.get('scenarios', {})
        
        for scenario_name, weights_dict in predefined_scenarios.items():
            try:
                weights_series, _ = self.validate_weights(weights_dict)
                scenarios[scenario_name] = weights_series
                logger.info(f"加载预定义方案: {scenario_name}")
            except Exception as e:
                logger.error(f"加载预定义方案 {scenario_name} 失败: {e}")
        
        return scenarios
    
    def load_weights_from_file(self, file_path: Union[str, Path]) -> Dict[str, pd.Series]:
        """
        从文件加载权重配置
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 方案名称到权重Series的映射
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"权重配置文件不存在: {file_path}")
        
        scenarios = {}
        
        try:
            if file_path.suffix.lower() in ['.yaml', '.yml']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
            elif file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            elif file_path.suffix.lower() in ['.csv', '.xlsx']:
                # 从Excel或CSV加载
                if file_path.suffix.lower() == '.csv':
                    df = pd.read_csv(file_path, index_col=0)
                else:
                    df = pd.read_excel(file_path, index_col=0)
                
                # 每列作为一个方案
                for column in df.columns:
                    weights_dict = df[column].dropna().to_dict()
                    weights_series, _ = self.validate_weights(weights_dict)
                    scenarios[column] = weights_series
                
                logger.info(f"从文件加载 {len(scenarios)} 个权重方案")
                return scenarios
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")
            
            # 处理YAML/JSON格式
            if isinstance(data, dict):
                for scenario_name, weights_dict in data.items():
                    if isinstance(weights_dict, dict):
                        weights_series, _ = self.validate_weights(weights_dict)
                        scenarios[scenario_name] = weights_series
            
            logger.info(f"从文件加载 {len(scenarios)} 个权重方案")
            
        except Exception as e:
            logger.error(f"加载权重配置文件失败: {e}")
            raise
        
        return scenarios
    
    def create_weight_scenario(self, scenario_name: str, weights: Union[Dict, pd.Series, str],
                             description: Optional[str] = None) -> pd.Series:
        """
        创建权重配置方案
        
        Args:
            scenario_name: 方案名称
            weights: 权重配置
            description: 方案描述
            
        Returns:
            pd.Series: 验证后的权重Series
        """
        if isinstance(weights, str):
            weights = self.parse_weight_input(weights)
        
        weights_series, is_normalized = self.validate_weights(weights)
        
        if is_normalized:
            logger.info(f"方案 '{scenario_name}' 权重已标准化")
        
        if description:
            logger.info(f"创建权重方案 '{scenario_name}': {description}")
        else:
            logger.info(f"创建权重方案 '{scenario_name}'")
        
        return weights_series
    
    def generate_random_scenarios(self, assets: List[str], num_scenarios: int = 5,
                                random_seed: Optional[int] = None) -> Dict[str, pd.Series]:
        """
        生成随机权重配置方案
        
        Args:
            assets: 资产列表
            num_scenarios: 生成方案数量
            random_seed: 随机种子
            
        Returns:
            Dict: 方案名称到权重Series的映射
        """
        if random_seed is not None:
            np.random.seed(random_seed)
        
        scenarios = {}
        
        for i in range(num_scenarios):
            # 生成随机权重
            random_weights = np.random.dirichlet(np.ones(len(assets)))
            weights_dict = dict(zip(assets, random_weights))
            
            scenario_name = f"random_scenario_{i+1}"
            weights_series, _ = self.validate_weights(weights_dict)
            scenarios[scenario_name] = weights_series
        
        logger.info(f"生成 {num_scenarios} 个随机权重方案")
        return scenarios
    
    def compare_weight_scenarios(self, scenarios: Dict[str, pd.Series]) -> pd.DataFrame:
        """
        比较多个权重配置方案
        
        Args:
            scenarios: 方案字典
            
        Returns:
            pd.DataFrame: 权重对比表
        """
        if len(scenarios) > self.max_scenarios:
            logger.warning(f"方案数量 ({len(scenarios)}) 超过最大限制 ({self.max_scenarios})")
        
        # 获取所有资产
        all_assets = set()
        for weights in scenarios.values():
            all_assets.update(weights.index)
        all_assets = sorted(list(all_assets))
        
        # 创建对比表
        comparison_df = pd.DataFrame(index=all_assets, columns=list(scenarios.keys()))
        
        for scenario_name, weights in scenarios.items():
            for asset in all_assets:
                comparison_df.loc[asset, scenario_name] = weights.get(asset, 0.0)
        
        # 添加统计信息
        comparison_df.loc['总和'] = comparison_df.sum()
        comparison_df.loc['资产数量'] = (comparison_df > self.min_weight_threshold).sum()
        comparison_df.loc['最大权重'] = comparison_df.iloc[:-2].max()
        comparison_df.loc['最小权重'] = comparison_df.iloc[:-3].min()
        
        logger.info(f"权重方案对比完成，包含 {len(scenarios)} 个方案，{len(all_assets)} 个资产")
        return comparison_df
    
    def save_scenarios_to_file(self, scenarios: Dict[str, pd.Series], 
                             file_path: Union[str, Path]) -> None:
        """
        保存权重方案到文件
        
        Args:
            scenarios: 权重方案字典
            file_path: 保存路径
        """
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建对比表
        comparison_df = self.compare_weight_scenarios(scenarios)
        
        if file_path.suffix.lower() == '.csv':
            comparison_df.to_csv(file_path)
        elif file_path.suffix.lower() in ['.xlsx', '.xls']:
            comparison_df.to_excel(file_path)
        elif file_path.suffix.lower() in ['.yaml', '.yml']:
            # 转换为字典格式
            scenarios_dict = {}
            for scenario_name, weights in scenarios.items():
                scenarios_dict[scenario_name] = weights.to_dict()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(scenarios_dict, f, default_flow_style=False, allow_unicode=True)
        else:
            raise ValueError(f"不支持的保存格式: {file_path.suffix}")
        
        logger.info(f"权重方案已保存到: {file_path}")
    
    def get_weight_statistics(self, weights: pd.Series) -> Dict[str, float]:
        """
        计算权重统计信息
        
        Args:
            weights: 权重Series
            
        Returns:
            Dict: 统计信息
        """
        stats = {
            'total_weight': weights.sum(),
            'num_assets': len(weights),
            'num_positive_weights': (weights > self.min_weight_threshold).sum(),
            'max_weight': weights.max(),
            'min_weight': weights.min(),
            'mean_weight': weights.mean(),
            'std_weight': weights.std(),
            'concentration_hhi': (weights ** 2).sum(),  # Herfindahl指数
            'effective_assets': 1 / (weights ** 2).sum() if (weights ** 2).sum() > 0 else 0
        }
        
        return stats
