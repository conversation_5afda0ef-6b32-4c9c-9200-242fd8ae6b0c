"""
权重影响分析综合模块
整合权重敏感性分析、边际风险分析和风险预算优化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from config.config_manager import config_manager
from core.portfolio_manager import PortfolioManager
from core.weight_sensitivity import WeightSensitivityAnalyzer
from core.marginal_risk import MarginalRiskAnalyzer
from core.risk_budget_optimizer import RiskBudgetOptimizer
from utils.logger import get_module_logger

logger = get_module_logger("WeightImpactAnalyzer")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class WeightImpactAnalyzer:
    """权重影响分析综合器"""
    
    def __init__(self, portfolio_manager: PortfolioManager):
        """
        初始化权重影响分析器
        
        Args:
            portfolio_manager: 投资组合管理器
        """
        self.portfolio_manager = portfolio_manager
        self.config = config_manager.get_config()
        self.risk_config = config_manager.get_risk_calculation_config()
        
        # 初始化各个分析器
        self.sensitivity_analyzer = WeightSensitivityAnalyzer(portfolio_manager)
        self.marginal_analyzer = MarginalRiskAnalyzer(portfolio_manager)
        self.optimizer = RiskBudgetOptimizer(portfolio_manager)
        
        logger.info("Weight impact analyzer initialized")
    
    def run_comprehensive_analysis(self, returns_data: pd.DataFrame, 
                                 save_results: bool = True) -> Dict[str, Any]:
        """
        运行综合权重影响分析
        
        Args:
            returns_data: 收益率数据
            save_results: 是否保存结果
            
        Returns:
            Dict: 综合分析结果
        """
        logger.info("Starting comprehensive weight impact analysis")
        
        analysis_results = {
            'analysis_timestamp': datetime.now(),
            'portfolio_summary': self.portfolio_manager.get_portfolio_summary(),
            'sensitivity_analysis': {},
            'marginal_risk_analysis': {},
            'risk_budget_analysis': {},
            'optimization_recommendations': {},
            'simulation_results': {}
        }
        
        try:
            # 1. 权重敏感性分析
            logger.info("Step 1: Weight sensitivity analysis")
            
            # 单资产敏感性分析
            sensitivity_results = self.sensitivity_analyzer.single_asset_sensitivity_analysis(returns_data)
            
            # 两资产协同变动分析
            scenario_results = self.sensitivity_analyzer.two_asset_scenario_analysis(returns_data)
            
            analysis_results['sensitivity_analysis'] = {
                'single_asset_results': sensitivity_results,
                'scenario_results': scenario_results
            }
            
            # 2. 边际风险分析
            logger.info("Step 2: Marginal risk analysis")
            
            # 综合风险贡献度分析
            risk_analysis = self.marginal_analyzer.analyze_risk_contributions(returns_data)
            
            # 风险集中度识别
            concentrations = self.marginal_analyzer.identify_risk_concentrations(risk_analysis)
            
            analysis_results['marginal_risk_analysis'] = {
                'risk_contributions': risk_analysis,
                'risk_concentrations': concentrations
            }
            
            # 3. 风险预算分析和优化
            logger.info("Step 3: Risk budget optimization")
            
            # 当前风险预算分析
            risk_budget_analysis = self.optimizer.analyze_current_risk_budget(returns_data)
            
            # 生成优化建议
            optimization_recommendations = self.optimizer.generate_optimization_recommendations(
                returns_data, risk_budget_analysis
            )
            
            # 模拟优化效果
            simulation_results = self.optimizer.simulate_optimization_impact(
                returns_data, optimization_recommendations['weight_adjustments']
            )
            
            analysis_results['risk_budget_analysis'] = risk_budget_analysis
            analysis_results['optimization_recommendations'] = optimization_recommendations
            analysis_results['simulation_results'] = simulation_results
            
            # 4. 保存结果和生成报告
            if save_results:
                logger.info("Step 4: Saving results and generating reports")
                self._save_analysis_results(analysis_results, returns_data)
            
            logger.info("Comprehensive weight impact analysis completed successfully")
            return analysis_results
            
        except Exception as e:
            logger.error(f"Error in comprehensive analysis: {e}")
            analysis_results['error'] = str(e)
            return analysis_results
    
    def _save_analysis_results(self, analysis_results: Dict[str, Any], 
                             returns_data: pd.DataFrame) -> Dict[str, str]:
        """
        保存分析结果和生成报告
        
        Args:
            analysis_results: 分析结果
            returns_data: 收益率数据
            
        Returns:
            Dict: 生成的文件路径
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_dir = Path(self.config.get('output', {}).get('results_path', 'results'))
        results_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建专门的权重分析目录
        weight_analysis_dir = results_dir / f"weight_impact_analysis_{timestamp}"
        weight_analysis_dir.mkdir(exist_ok=True)
        
        generated_files = {}
        
        try:
            # 1. 生成可视化图表
            charts_dir = weight_analysis_dir / "charts"
            charts_dir.mkdir(exist_ok=True)
            
            # 敏感性分析图表
            if analysis_results['sensitivity_analysis']['single_asset_results']:
                sensitivity_chart_path = charts_dir / "single_asset_sensitivity.png"
                self.sensitivity_analyzer.visualize_single_asset_sensitivity(
                    analysis_results['sensitivity_analysis']['single_asset_results'],
                    str(sensitivity_chart_path)
                )
                generated_files['sensitivity_chart'] = str(sensitivity_chart_path)
            
            if analysis_results['sensitivity_analysis']['scenario_results']:
                scenario_chart_path = charts_dir / "two_asset_scenarios.png"
                self.sensitivity_analyzer.visualize_two_asset_scenarios(
                    analysis_results['sensitivity_analysis']['scenario_results'],
                    str(scenario_chart_path)
                )
                generated_files['scenario_chart'] = str(scenario_chart_path)
            
            # 边际风险分析图表
            if analysis_results['marginal_risk_analysis']['risk_contributions']:
                marginal_chart_path = charts_dir / "marginal_risk_contributions.png"
                self.marginal_analyzer.visualize_risk_contributions(
                    analysis_results['marginal_risk_analysis']['risk_contributions'],
                    str(marginal_chart_path)
                )
                generated_files['marginal_chart'] = str(marginal_chart_path)
            
            # 优化对比图表
            if analysis_results['simulation_results']:
                optimization_chart_path = charts_dir / "optimization_comparison.png"
                self.optimizer.visualize_optimization_comparison(
                    analysis_results['simulation_results'],
                    str(optimization_chart_path)
                )
                generated_files['optimization_chart'] = str(optimization_chart_path)
            
            # 2. 生成综合报告
            reports_dir = weight_analysis_dir / "reports"
            reports_dir.mkdir(exist_ok=True)
            
            # 敏感性分析报告
            if (analysis_results['sensitivity_analysis']['single_asset_results'] or 
                analysis_results['sensitivity_analysis']['scenario_results']):
                sensitivity_report = self.sensitivity_analyzer.generate_sensitivity_report(
                    analysis_results['sensitivity_analysis']['single_asset_results'],
                    analysis_results['sensitivity_analysis']['scenario_results']
                )
                sensitivity_report_path = reports_dir / "sensitivity_analysis_report.md"
                with open(sensitivity_report_path, 'w', encoding='utf-8') as f:
                    f.write(sensitivity_report)
                generated_files['sensitivity_report'] = str(sensitivity_report_path)
            
            # 边际风险分析报告
            if analysis_results['marginal_risk_analysis']['risk_contributions']:
                marginal_report = self.marginal_analyzer.generate_marginal_risk_report(
                    analysis_results['marginal_risk_analysis']['risk_contributions'],
                    analysis_results['marginal_risk_analysis']['risk_concentrations']
                )
                marginal_report_path = reports_dir / "marginal_risk_analysis_report.md"
                with open(marginal_report_path, 'w', encoding='utf-8') as f:
                    f.write(marginal_report)
                generated_files['marginal_report'] = str(marginal_report_path)
            
            # 风险预算优化报告
            if (analysis_results['risk_budget_analysis'] and 
                analysis_results['optimization_recommendations']):
                optimization_report = self.optimizer.generate_optimization_report(
                    analysis_results['risk_budget_analysis'],
                    analysis_results['optimization_recommendations'],
                    analysis_results['simulation_results']
                )
                optimization_report_path = reports_dir / "risk_budget_optimization_report.md"
                with open(optimization_report_path, 'w', encoding='utf-8') as f:
                    f.write(optimization_report)
                generated_files['optimization_report'] = str(optimization_report_path)
            
            # 3. 生成综合报告
            comprehensive_report = self._generate_comprehensive_report(analysis_results)
            comprehensive_report_path = weight_analysis_dir / "comprehensive_weight_impact_report.md"
            with open(comprehensive_report_path, 'w', encoding='utf-8') as f:
                f.write(comprehensive_report)
            generated_files['comprehensive_report'] = str(comprehensive_report_path)
            
            # 4. 保存数据到Excel
            excel_path = weight_analysis_dir / "weight_impact_analysis_data.xlsx"
            self._save_to_excel(analysis_results, excel_path)
            generated_files['excel_data'] = str(excel_path)
            
            logger.info(f"Analysis results saved to {weight_analysis_dir}")
            return generated_files
            
        except Exception as e:
            logger.error(f"Error saving analysis results: {e}")
            return generated_files
    
    def _generate_comprehensive_report(self, analysis_results: Dict[str, Any]) -> str:
        """生成综合分析报告"""
        report_lines = []
        
        # 报告标题和概览
        report_lines.append("# 投资组合权重影响综合分析报告")
        report_lines.append("")
        report_lines.append(f"**分析时间**: {analysis_results['analysis_timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**置信水平**: {self.risk_config.confidence_level * 100:.1f}%")
        report_lines.append("")
        
        # 投资组合概览
        portfolio_summary = analysis_results['portfolio_summary']
        report_lines.append("## 投资组合概览")
        report_lines.append("")
        report_lines.append(f"- **资产数量**: {portfolio_summary['num_assets']}")
        report_lines.append(f"- **权重分布**: 详见各项分析")
        report_lines.append("")
        
        # 分析摘要
        report_lines.append("## 分析摘要")
        report_lines.append("")
        report_lines.append("本报告包含以下三个核心分析模块：")
        report_lines.append("")
        report_lines.append("### 1. 权重敏感性分析")
        report_lines.append("- **单资产权重变动分析**: 分析重要资产权重±5%变动对组合VaR/ES的影响")
        report_lines.append("- **两资产协同变动分析**: 分析相关资产协同调整的风险影响")
        report_lines.append("- **关键发现**: 识别对组合风险影响最大的资产")
        report_lines.append("")
        
        report_lines.append("### 2. 边际风险贡献分析")
        report_lines.append("- **边际VaR (MVaR)**: 各资产增加1%权重对组合VaR的影响")
        report_lines.append("- **边际ES (MES)**: 各资产增加1%权重对组合ES的影响")
        report_lines.append("- **风险贡献度排名**: 识别风险贡献最大的资产")
        report_lines.append("")
        
        report_lines.append("### 3. 风险预算优化建议")
        report_lines.append("- **当前风险预算分析**: 评估风险集中度和分散化程度")
        report_lines.append("- **权重调整建议**: 基于边际风险提供具体调整方案")
        report_lines.append("- **优化效果预测**: 模拟优化后的风险改进效果")
        report_lines.append("")
        
        # 核心发现和建议
        report_lines.append("## 核心发现与建议")
        report_lines.append("")
        
        # 从各个分析中提取关键信息
        if analysis_results.get('marginal_risk_analysis', {}).get('risk_contributions'):
            risk_analysis = analysis_results['marginal_risk_analysis']['risk_contributions']
            
            # 最大风险贡献者
            if risk_analysis.get('risk_rankings', {}).get('var_ranking'):
                top_contributor = risk_analysis['risk_rankings']['var_ranking'][0]
                report_lines.append(f"### 🔍 关键发现")
                report_lines.append(f"- **最大风险贡献资产**: {top_contributor[0]} (VaR贡献度: {top_contributor[2]:.1f}%)")
                
                # 风险集中度评估
                if analysis_results.get('risk_budget_analysis', {}).get('diversification_ratio'):
                    div_ratio = analysis_results['risk_budget_analysis']['diversification_ratio']['var']
                    if div_ratio < 0.5:
                        report_lines.append("- **风险集中度**: 🔴 高度集中，需要立即关注")
                    elif div_ratio < 0.7:
                        report_lines.append("- **风险集中度**: 🟡 中度集中，建议优化")
                    else:
                        report_lines.append("- **风险集中度**: 🟢 良好分散")
                
                report_lines.append("")
        
        # 优化建议摘要
        if analysis_results.get('optimization_recommendations', {}).get('weight_adjustments'):
            adjustments = analysis_results['optimization_recommendations']['weight_adjustments']
            high_priority = {k: v for k, v in adjustments.items() if v['priority'] == 'high'}
            
            if high_priority:
                report_lines.append("### 💡 立即行动建议")
                report_lines.append("")
                for asset, adj in high_priority.items():
                    action = "减少" if adj['change'] < 0 else "增加"
                    report_lines.append(f"- **{asset}**: {action}权重至 {adj['suggested_weight']:.2%} ({adj['reason']})")
                report_lines.append("")
        
        # 预期改进效果
        if analysis_results.get('simulation_results', {}).get('improvements'):
            improvements = analysis_results['simulation_results']['improvements']
            if improvements['var_reduction_pct'] > 0:
                report_lines.append("### 📈 预期改进效果")
                report_lines.append(f"- **VaR降低**: {improvements['var_reduction_pct']:.1f}%")
                report_lines.append(f"- **ES降低**: {improvements['es_reduction_pct']:.1f}%")
                report_lines.append("")
        
        # 实施指导
        report_lines.append("## 实施指导")
        report_lines.append("")
        report_lines.append("### 📋 实施步骤")
        report_lines.append("1. **立即执行**: 高优先级权重调整")
        report_lines.append("2. **短期内**: 建立边际风险监控机制")
        report_lines.append("3. **中期**: 逐步实施中优先级调整")
        report_lines.append("4. **长期**: 定期重新评估和优化")
        report_lines.append("")
        
        report_lines.append("### ⚠️ 风险提示")
        report_lines.append("- 本分析基于历史数据，市场环境变化可能影响结果有效性")
        report_lines.append("- 权重调整应考虑市场流动性和交易成本")
        report_lines.append("- 建议结合定性分析和专业判断进行决策")
        report_lines.append("- 定期更新分析以反映最新市场状况")
        report_lines.append("")
        
        # 详细报告索引
        report_lines.append("## 详细报告索引")
        report_lines.append("")
        report_lines.append("本综合报告包含以下详细分析报告：")
        report_lines.append("- `sensitivity_analysis_report.md` - 权重敏感性分析详细报告")
        report_lines.append("- `marginal_risk_analysis_report.md` - 边际风险分析详细报告")
        report_lines.append("- `risk_budget_optimization_report.md` - 风险预算优化详细报告")
        report_lines.append("- `weight_impact_analysis_data.xlsx` - 完整分析数据")
        report_lines.append("")
        
        report_lines.append("---")
        report_lines.append("*本报告由风险管理系统自动生成，如有疑问请联系风险管理团队*")
        
        return "\n".join(report_lines)

    def _save_to_excel(self, analysis_results: Dict[str, Any], excel_path: Path) -> None:
        """
        保存分析结果到Excel文件

        Args:
            analysis_results: 分析结果
            excel_path: Excel文件路径
        """
        try:
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:

                # 1. 投资组合概览
                portfolio_summary = analysis_results['portfolio_summary']
                if 'weights' in portfolio_summary:
                    weights_df = pd.DataFrame(list(portfolio_summary['weights'].items()),
                                            columns=['资产', '权重'])
                    weights_df['权重(%)'] = weights_df['权重'] * 100
                    weights_df.to_excel(writer, sheet_name='投资组合权重', index=False)

                # 2. 敏感性分析结果
                sensitivity_results = analysis_results.get('sensitivity_analysis', {})

                # 单资产敏感性分析
                if sensitivity_results.get('single_asset_results'):
                    sensitivity_data = []
                    for asset, result in sensitivity_results['single_asset_results'].items():
                        data = result['sensitivity_data']
                        for _, row in data.iterrows():
                            sensitivity_data.append({
                                '资产': asset,
                                '权重变化': row['weight_change'],
                                '新权重': row['new_weight'],
                                'VaR(%)': row['var'] * 100,
                                'ES(%)': row['es'] * 100
                            })

                    if sensitivity_data:
                        sensitivity_df = pd.DataFrame(sensitivity_data)
                        sensitivity_df.to_excel(writer, sheet_name='单资产敏感性分析', index=False)

                # 两资产协同分析
                if sensitivity_results.get('scenario_results'):
                    scenario_data = []
                    for pair_name, result in sensitivity_results['scenario_results'].items():
                        asset1, asset2 = result['asset_pair']
                        scenarios = result['scenarios']
                        for _, row in scenarios.iterrows():
                            scenario_data.append({
                                '资产对': f"{asset1} & {asset2}",
                                '情景': row['scenario'],
                                f'{asset1}权重变化': row['asset1_change'],
                                f'{asset2}权重变化': row['asset2_change'],
                                'VaR(%)': row['var'] * 100,
                                'ES(%)': row['es'] * 100
                            })

                    if scenario_data:
                        scenario_df = pd.DataFrame(scenario_data)
                        scenario_df.to_excel(writer, sheet_name='两资产协同分析', index=False)

                # 3. 边际风险分析结果
                marginal_analysis = analysis_results.get('marginal_risk_analysis', {})
                if marginal_analysis.get('risk_contributions'):
                    risk_contrib = marginal_analysis['risk_contributions']['asset_contributions']

                    marginal_data = []
                    for asset, contrib in risk_contrib.items():
                        marginal_data.append({
                            '资产': asset,
                            '当前权重(%)': contrib['weight'] * 100,
                            '边际VaR(%)': contrib['marginal_var'] * 100,
                            '边际ES(%)': contrib['marginal_es'] * 100,
                            '成分VaR(%)': contrib['component_var'] * 100,
                            '成分ES(%)': contrib['component_es'] * 100,
                            'VaR贡献度(%)': contrib['var_contribution_pct'],
                            'ES贡献度(%)': contrib['es_contribution_pct']
                        })

                    marginal_df = pd.DataFrame(marginal_data)
                    marginal_df.to_excel(writer, sheet_name='边际风险分析', index=False)

                # 4. 风险预算分析
                risk_budget = analysis_results.get('risk_budget_analysis', {})
                if risk_budget:
                    budget_data = []

                    # 组合风险指标
                    portfolio_risk = risk_budget.get('portfolio_risk', {})
                    concentration = risk_budget.get('concentration_metrics', {})
                    diversification = risk_budget.get('diversification_ratio', {})

                    budget_summary = {
                        '指标': ['组合VaR(%)', '组合ES(%)', 'VaR集中度指数', 'ES集中度指数',
                               '有效资产数量(VaR)', '有效资产数量(ES)', '分散化比率(VaR)', '分散化比率(ES)'],
                        '数值': [
                            portfolio_risk.get('var', 0) * 100,
                            portfolio_risk.get('es', 0) * 100,
                            concentration.get('var_hhi', 0),
                            concentration.get('es_hhi', 0),
                            concentration.get('effective_assets_var', 0),
                            concentration.get('effective_assets_es', 0),
                            diversification.get('var', 0),
                            diversification.get('es', 0)
                        ]
                    }

                    budget_df = pd.DataFrame(budget_summary)
                    budget_df.to_excel(writer, sheet_name='风险预算分析', index=False)

                # 5. 优化建议
                optimization = analysis_results.get('optimization_recommendations', {})
                if optimization.get('weight_adjustments'):
                    opt_data = []
                    for asset, adj in optimization['weight_adjustments'].items():
                        opt_data.append({
                            '资产': asset,
                            '当前权重(%)': adj['current_weight'] * 100,
                            '建议权重(%)': adj['suggested_weight'] * 100,
                            '权重变化(%)': adj['change'] * 100,
                            '调整原因': adj['reason'],
                            '优先级': adj['priority']
                        })

                    opt_df = pd.DataFrame(opt_data)
                    opt_df.to_excel(writer, sheet_name='权重调整建议', index=False)

                # 6. 优化效果预测
                simulation = analysis_results.get('simulation_results', {})
                if simulation:
                    current_risk = simulation.get('current_risk', {})
                    optimized_risk = simulation.get('optimized_risk', {})
                    improvements = simulation.get('improvements', {})

                    effect_data = {
                        '风险指标': ['VaR', 'ES'],
                        '优化前(%)': [
                            current_risk.get('var', 0) * 100,
                            current_risk.get('es', 0) * 100
                        ],
                        '优化后(%)': [
                            optimized_risk.get('var', 0) * 100,
                            optimized_risk.get('es', 0) * 100
                        ],
                        '绝对改进(%)': [
                            improvements.get('var_absolute_change', 0) * 100,
                            improvements.get('es_absolute_change', 0) * 100
                        ],
                        '相对改进(%)': [
                            improvements.get('var_reduction_pct', 0),
                            improvements.get('es_reduction_pct', 0)
                        ]
                    }

                    effect_df = pd.DataFrame(effect_data)
                    effect_df.to_excel(writer, sheet_name='优化效果预测', index=False)

                # 7. 分析参数
                params_data = {
                    '参数': ['置信水平(%)', '持有期(天)', '分析时间'],
                    '数值': [
                        self.risk_config.confidence_level * 100,
                        self.risk_config.holding_period,
                        analysis_results['analysis_timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                    ]
                }

                params_df = pd.DataFrame(params_data)
                params_df.to_excel(writer, sheet_name='分析参数', index=False)

            logger.info(f"Analysis data saved to Excel: {excel_path}")

        except Exception as e:
            logger.error(f"Error saving to Excel: {e}")

    def get_analysis_summary(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取分析结果摘要

        Args:
            analysis_results: 分析结果

        Returns:
            Dict: 分析摘要
        """
        summary = {
            'analysis_timestamp': analysis_results.get('analysis_timestamp'),
            'portfolio_assets_count': 0,
            'sensitivity_analysis_completed': False,
            'marginal_risk_analysis_completed': False,
            'optimization_analysis_completed': False,
            'key_findings': {},
            'recommendations_count': 0,
            'expected_improvements': {}
        }

        # 投资组合信息
        if analysis_results.get('portfolio_summary'):
            summary['portfolio_assets_count'] = analysis_results['portfolio_summary'].get('num_assets', 0)

        # 分析完成状态
        summary['sensitivity_analysis_completed'] = bool(
            analysis_results.get('sensitivity_analysis', {}).get('single_asset_results')
        )
        summary['marginal_risk_analysis_completed'] = bool(
            analysis_results.get('marginal_risk_analysis', {}).get('risk_contributions')
        )
        summary['optimization_analysis_completed'] = bool(
            analysis_results.get('optimization_recommendations', {}).get('weight_adjustments')
        )

        # 关键发现
        if analysis_results.get('marginal_risk_analysis', {}).get('risk_contributions'):
            risk_analysis = analysis_results['marginal_risk_analysis']['risk_contributions']
            if risk_analysis.get('risk_rankings', {}).get('var_ranking'):
                top_contributor = risk_analysis['risk_rankings']['var_ranking'][0]
                summary['key_findings']['top_risk_contributor'] = {
                    'asset': top_contributor[0],
                    'contribution_pct': top_contributor[2]
                }

        # 建议数量
        if analysis_results.get('optimization_recommendations', {}).get('weight_adjustments'):
            summary['recommendations_count'] = len(
                analysis_results['optimization_recommendations']['weight_adjustments']
            )

        # 预期改进
        if analysis_results.get('simulation_results', {}).get('improvements'):
            improvements = analysis_results['simulation_results']['improvements']
            summary['expected_improvements'] = {
                'var_reduction_pct': improvements.get('var_reduction_pct', 0),
                'es_reduction_pct': improvements.get('es_reduction_pct', 0)
            }

        return summary
