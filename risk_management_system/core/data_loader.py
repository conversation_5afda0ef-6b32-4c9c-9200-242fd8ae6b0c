"""
数据加载模块
负责加载和预处理历史数据与投资组合权重
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Tuple, Optional, List
from datetime import datetime, timedelta

from config.config_manager import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("DataLoader")


class DataLoader:
    """数据加载器"""
    
    def __init__(self):
        self.config = config_manager.get_data_processing_config()
        
    def load_historical_returns(self, file_path: Optional[str] = None) -> pd.DataFrame:
        """
        加载历史收益率数据
        
        Args:
            file_path: 数据文件路径，如果为None则使用配置中的路径
            
        Returns:
            DataFrame: 包含日期和各资产收益率的数据框
        """
        file_path = file_path or self.config.historical_data_path
        
        try:
            if not Path(file_path).exists():
                logger.warning(f"Historical data file not found: {file_path}")
                return self._create_sample_data()
            
            # 读取数据
            data = pd.read_csv(file_path, index_col=0, parse_dates=True)
            
            # 数据验证
            if data.empty:
                logger.error("Historical data is empty")
                return pd.DataFrame()
            
            # 检查数据质量
            self._validate_data_quality(data)
            
            # 处理缺失值
            data = self._handle_missing_values(data)
            
            # 转换为对数收益率（如果配置要求）
            if self.config.log_returns:
                data = self._convert_to_log_returns(data)
            
            logger.info(f"Loaded historical returns: {data.shape[0]} dates, {data.shape[1]} assets")
            return data
            
        except Exception as e:
            logger.error(f"Error loading historical returns: {e}")
            return pd.DataFrame()
    
    def load_portfolio_weights(self, file_path: Optional[str] = None) -> pd.Series:
        """
        加载投资组合权重
        
        Args:
            file_path: 权重文件路径，如果为None则使用配置中的路径
            
        Returns:
            Series: 包含各资产权重的序列
        """
        file_path = file_path or self.config.portfolio_weights_path
        
        try:
            if not Path(file_path).exists():
                logger.warning(f"Portfolio weights file not found: {file_path}")
                return self._create_sample_weights()
            
            # 读取权重数据
            weights_data = pd.read_csv(file_path, index_col=0)
            
            if weights_data.empty:
                logger.error("Portfolio weights data is empty")
                return pd.Series()
            
            # 获取权重序列
            if weights_data.shape[1] == 1:
                weights = weights_data.iloc[:, 0]
            else:
                # 如果有多列，取最新日期的权重
                weights = weights_data.iloc[:, -1]
            
            # 权重标准化
            weights = weights / weights.sum()
            
            # 验证权重
            self._validate_weights(weights)
            
            logger.info(f"Loaded portfolio weights for {len(weights)} assets")
            return weights
            
        except Exception as e:
            logger.error(f"Error loading portfolio weights: {e}")
            return pd.Series()
    
    def _create_sample_data(self) -> pd.DataFrame:
        """创建示例历史收益率数据"""
        logger.info("Creating sample historical returns data")
        
        # 生成示例数据
        np.random.seed(42)
        dates = pd.date_range(end=datetime.now(), periods=self.config.data_window_size, freq='D')
        assets = ['Asset_A', 'Asset_B', 'Asset_C', 'Asset_D', 'Asset_E']
        
        # 生成相关的收益率数据
        n_assets = len(assets)
        correlation_matrix = np.random.uniform(0.1, 0.7, (n_assets, n_assets))
        correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2
        np.fill_diagonal(correlation_matrix, 1.0)
        
        # 确保相关矩阵正定
        eigenvals, eigenvecs = np.linalg.eigh(correlation_matrix)
        eigenvals = np.maximum(eigenvals, 0.01)
        correlation_matrix = eigenvecs @ np.diag(eigenvals) @ eigenvecs.T
        
        # 生成收益率
        returns = np.random.multivariate_normal(
            mean=np.zeros(n_assets),
            cov=correlation_matrix * 0.02**2,  # 2%的日波动率
            size=len(dates)
        )
        
        data = pd.DataFrame(returns, index=dates, columns=assets)
        
        # 保存示例数据
        output_dir = Path(self.config.historical_data_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        data.to_csv(self.config.historical_data_path)
        
        return data
    
    def _create_sample_weights(self) -> pd.Series:
        """创建示例投资组合权重"""
        logger.info("Creating sample portfolio weights")
        
        # 创建等权重组合
        assets = ['Asset_A', 'Asset_B', 'Asset_C', 'Asset_D', 'Asset_E']
        weights = pd.Series([0.2] * len(assets), index=assets)
        
        # 保存示例权重
        output_dir = Path(self.config.portfolio_weights_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        weights.to_csv(self.config.portfolio_weights_path, header=['Weight'])
        
        return weights
    
    def _validate_data_quality(self, data: pd.DataFrame) -> None:
        """验证数据质量"""
        # 检查数据点数量
        if len(data) < self.config.min_data_points:
            logger.warning(f"Insufficient data points: {len(data)} < {self.config.min_data_points}")
        
        # 检查缺失值比例
        missing_ratio = data.isnull().sum() / len(data)
        high_missing_assets = missing_ratio[missing_ratio > 0.1].index.tolist()
        if high_missing_assets:
            logger.warning(f"Assets with high missing data ratio: {high_missing_assets}")
        
        # 检查异常值
        extreme_returns = (np.abs(data) > 0.2).any(axis=1)
        if extreme_returns.sum() > 0:
            logger.warning(f"Found {extreme_returns.sum()} days with extreme returns (>20%)")
    
    def _handle_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        # 前向填充
        data = data.fillna(method='ffill')
        
        # 后向填充剩余的缺失值
        data = data.fillna(method='bfill')
        
        # 如果仍有缺失值，用0填充
        data = data.fillna(0)
        
        return data
    
    def _convert_to_log_returns(self, data: pd.DataFrame) -> pd.DataFrame:
        """转换为对数收益率"""
        # 假设输入数据是价格数据，计算对数收益率
        if data.min().min() > 0:  # 检查是否为价格数据
            log_returns = np.log(data / data.shift(1)).dropna()
            logger.info("Converted price data to log returns")
            return log_returns
        else:
            # 如果已经是收益率数据，直接返回
            logger.info("Data appears to be returns, no conversion needed")
            return data
    
    def _validate_weights(self, weights: pd.Series) -> None:
        """验证投资组合权重"""
        # 检查权重和是否接近1
        weight_sum = weights.sum()
        if abs(weight_sum - 1.0) > 0.01:
            logger.warning(f"Portfolio weights sum to {weight_sum:.4f}, not 1.0")
        
        # 检查负权重
        negative_weights = weights[weights < 0]
        if len(negative_weights) > 0:
            logger.warning(f"Found negative weights: {negative_weights.to_dict()}")
        
        # 检查零权重
        zero_weights = weights[weights == 0]
        if len(zero_weights) > 0:
            logger.info(f"Found zero weights for assets: {zero_weights.index.tolist()}")
    
    def get_data_window(self, data: pd.DataFrame, end_date: Optional[datetime] = None) -> pd.DataFrame:
        """
        获取指定窗口大小的数据
        
        Args:
            data: 原始数据
            end_date: 结束日期，如果为None则使用最新日期
            
        Returns:
            DataFrame: 窗口数据
        """
        if end_date is None:
            end_date = data.index.max()
        
        start_date = end_date - timedelta(days=self.config.data_window_size)
        window_data = data.loc[start_date:end_date]
        
        if len(window_data) < self.config.min_data_points:
            logger.warning(f"Window data has only {len(window_data)} points")
        
        return window_data
    
    def align_data_and_weights(self, returns_data: pd.DataFrame, weights: pd.Series) -> Tuple[pd.DataFrame, pd.Series]:
        """
        对齐收益率数据和权重
        
        Args:
            returns_data: 收益率数据
            weights: 投资组合权重
            
        Returns:
            Tuple: 对齐后的收益率数据和权重
        """
        # 找到共同的资产
        common_assets = returns_data.columns.intersection(weights.index)
        
        if len(common_assets) == 0:
            logger.error("No common assets found between returns data and weights")
            return pd.DataFrame(), pd.Series()
        
        # 对齐数据
        aligned_returns = returns_data[common_assets]
        aligned_weights = weights[common_assets]
        
        # 重新标准化权重
        aligned_weights = aligned_weights / aligned_weights.sum()
        
        logger.info(f"Aligned data for {len(common_assets)} common assets")
        return aligned_returns, aligned_weights
