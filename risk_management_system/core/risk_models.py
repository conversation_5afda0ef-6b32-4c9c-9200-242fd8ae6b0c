"""
风险模型模块
实现参数法(GARCH)、历史模拟法和蒙特卡洛模拟法的VaR和ES计算
"""

import pandas as pd
import numpy as np
from scipy import stats
from scipy.optimize import minimize
from arch import arch_model
from typing import Dict, Tuple, Optional, List
import warnings

from config.config_manager import config_manager
from utils.logger import get_module_logger

logger = get_module_logger("RiskModels")


class BaseRiskModel:
    """风险模型基类"""

    def __init__(self):
        self.risk_config = config_manager.get_risk_calculation_config()

    def calculate_var(self, returns: pd.Series, confidence_level: Optional[float] = None) -> float:
        """计算VaR"""
        raise NotImplementedError

    def calculate_es(self, returns: pd.Series, confidence_level: Optional[float] = None) -> float:
        """计算ES"""
        raise NotImplementedError

    def calculate_var_es(self, returns: pd.Series, confidence_level: Optional[float] = None) -> <PERSON><PERSON>[float, float]:
        """同时计算VaR和ES"""
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level

        var = self.calculate_var(returns, confidence_level)
        es = self.calculate_es(returns, confidence_level)

        return var, es


class ParametricRiskModel(BaseRiskModel):
    """参数法风险模型 (基于GARCH)"""

    def __init__(self):
        super().__init__()
        self.param_config = config_manager.get_parametric_method_config()

    def fit_garch_model(self, returns: pd.Series) -> Dict:
        """
        拟合GARCH模型

        Args:
            returns: 收益率序列

        Returns:
            Dict: 包含模型参数和预测结果的字典
        """
        try:
            # 创建GARCH模型
            p, q = self.param_config.garch_order

            if self.param_config.distribution_assumption == 'student_t':
                dist = 't'
            else:
                dist = 'normal'

            model = arch_model(
                returns * 100,  # 转换为百分比
                vol='GARCH',
                p=p,
                q=q,
                dist=dist
            )

            # 拟合模型
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                fitted_model = model.fit(
                    disp='off',
                    options={'maxiter': self.param_config.max_iter}
                )

            # 获取预测的条件方差
            forecast = fitted_model.forecast(horizon=self.risk_config.holding_period)
            conditional_volatility = np.sqrt(forecast.variance.iloc[-1, 0]) / 100  # 转换回小数

            # 获取分布参数
            if self.param_config.distribution_assumption == 'student_t':
                nu = fitted_model.params['nu']  # 自由度参数
                distribution_params = {'nu': nu}
            else:
                distribution_params = {}

            result = {
                'model': fitted_model,
                'conditional_volatility': conditional_volatility,
                'distribution': self.param_config.distribution_assumption,
                'distribution_params': distribution_params,
                'aic': fitted_model.aic,
                'bic': fitted_model.bic
            }

            logger.debug(f"GARCH model fitted successfully, volatility: {conditional_volatility:.4f}")
            return result

        except Exception as e:
            logger.error(f"Error fitting GARCH model: {e}")
            # 回退到简单的历史波动率
            volatility = returns.std()
            return {
                'model': None,
                'conditional_volatility': volatility,
                'distribution': 'normal',
                'distribution_params': {},
                'aic': np.inf,
                'bic': np.inf
            }

    def calculate_var(self, returns: pd.Series, confidence_level: Optional[float] = None) -> float:
        """
        使用参数法计算VaR (修复版本)

        Args:
            returns: 收益率序列
            confidence_level: 置信水平

        Returns:
            float: VaR值（正数表示损失）
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level

        # 拟合GARCH模型
        garch_result = self.fit_garch_model(returns)

        # 获取预测的波动率
        volatility = garch_result['conditional_volatility']

        # 获取均值 (重要：VaR计算需要考虑均值)
        mean_return = returns.mean()

        # 计算分位数
        alpha = 1 - confidence_level

        if garch_result['distribution'] == 'student_t':
            nu = garch_result['distribution_params']['nu']
            quantile = stats.t.ppf(alpha, df=nu)
        else:
            quantile = stats.norm.ppf(alpha)

        # 计算VaR (调整为持有期，包含均值项): VaR = -μ*T - σ*√T*Φ^(-1)(α)
        holding_period_factor = np.sqrt(self.risk_config.holding_period)

        # 检查是否需要与边际VaR计算保持一致 (简化版本)
        if hasattr(self.param_config, 'use_simplified_var') and self.param_config.use_simplified_var:
            # 简化版本：忽略均值项以确保与边际VaR计算的数学一致性
            var = -quantile * volatility * holding_period_factor
        else:
            # 完整版本：包含均值项
            var = -mean_return * self.risk_config.holding_period - quantile * volatility * holding_period_factor

        return var

    def calculate_es(self, returns: pd.Series, confidence_level: Optional[float] = None) -> float:
        """
        使用参数法计算ES (修复版本)

        Args:
            returns: 收益率序列
            confidence_level: 置信水平

        Returns:
            float: ES值（正数表示损失）
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level

        # 拟合GARCH模型
        garch_result = self.fit_garch_model(returns)

        # 获取预测的波动率
        volatility = garch_result['conditional_volatility']

        # 获取均值 (重要：ES计算需要考虑均值)
        mean_return = returns.mean()

        # 计算ES
        alpha = 1 - confidence_level

        if garch_result['distribution'] == 'student_t':
            nu = garch_result['distribution_params']['nu']
            var_quantile = stats.t.ppf(alpha, df=nu)
            # t分布的ES公式
            es_factor = stats.t.pdf(var_quantile, df=nu) / alpha * (nu + var_quantile**2) / (nu - 1)
        else:
            var_quantile = stats.norm.ppf(alpha)
            # 正态分布的ES公式: ES = -μ - σ * φ(Φ^(-1)(α)) / α
            es_factor = stats.norm.pdf(var_quantile) / alpha

        # 计算ES (调整为持有期，包含均值项)
        holding_period_factor = np.sqrt(self.risk_config.holding_period)
        es = -mean_return * self.risk_config.holding_period + es_factor * volatility * holding_period_factor

        return es

    def calculate_ewma_volatility(self, returns: pd.Series) -> float:
        """
        使用EWMA计算波动率

        Args:
            returns: 收益率序列

        Returns:
            float: EWMA波动率
        """
        lambda_param = self.param_config.ewma_lambda

        # 计算EWMA方差
        squared_returns = returns**2
        weights = np.array([(1 - lambda_param) * lambda_param**i
                           for i in range(len(squared_returns))])
        weights = weights[::-1]  # 反转权重，最新的数据权重最大
        weights = weights / weights.sum()  # 标准化权重

        ewma_variance = np.sum(weights * squared_returns)
        ewma_volatility = np.sqrt(ewma_variance)

        return ewma_volatility


class HistoricalSimulationModel(BaseRiskModel):
    """历史模拟法风险模型"""

    def __init__(self):
        super().__init__()
        self.hs_config = config_manager.get_historical_simulation_config()

    def calculate_var(self, returns: pd.Series, confidence_level: Optional[float] = None) -> float:
        """
        使用历史模拟法计算VaR

        Args:
            returns: 收益率序列
            confidence_level: 置信水平

        Returns:
            float: VaR值（正数表示损失）
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level

        if len(returns) == 0:
            logger.error("Empty returns series for VaR calculation")
            return 0.0

        # 计算分位数
        alpha = 1 - confidence_level

        if self.hs_config.use_weighted_hs:
            # 加权历史模拟（给近期数据更高权重）
            weights = self._calculate_exponential_weights(len(returns))
            var = self._weighted_quantile(returns, alpha, weights)
        else:
            # 标准历史模拟
            var = -np.percentile(returns, alpha * 100)

        # 调整为持有期
        holding_period_factor = np.sqrt(self.risk_config.holding_period)
        var = var * holding_period_factor

        return var

    def calculate_es(self, returns: pd.Series, confidence_level: Optional[float] = None) -> float:
        """
        使用历史模拟法计算ES

        Args:
            returns: 收益率序列
            confidence_level: 置信水平

        Returns:
            float: ES值（正数表示损失）
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level

        if len(returns) == 0:
            logger.error("Empty returns series for ES calculation")
            return 0.0

        # 计算VaR
        var = self.calculate_var(returns, confidence_level)

        # 计算ES（超过VaR的损失的平均值）
        alpha = 1 - confidence_level
        var_threshold = -var / np.sqrt(self.risk_config.holding_period)  # 转换回单期VaR

        # 找到超过VaR的损失
        tail_losses = returns[returns <= var_threshold]

        if len(tail_losses) == 0:
            # 如果没有超过VaR的损失，返回VaR
            es = var
        else:
            es = -tail_losses.mean()
            # 调整为持有期
            holding_period_factor = np.sqrt(self.risk_config.holding_period)
            es = es * holding_period_factor

        return es

    def _calculate_exponential_weights(self, n: int, lambda_param: float = 0.94) -> np.ndarray:
        """计算指数权重"""
        weights = np.array([(1 - lambda_param) * lambda_param**i for i in range(n)])
        weights = weights[::-1]  # 反转，最新数据权重最大
        return weights / weights.sum()

    def _weighted_quantile(self, values: pd.Series, quantile: float, weights: np.ndarray) -> float:
        """计算加权分位数"""
        # 排序
        sorted_indices = np.argsort(values)
        sorted_values = values.iloc[sorted_indices]
        sorted_weights = weights[sorted_indices]

        # 计算累积权重
        cumulative_weights = np.cumsum(sorted_weights)

        # 找到分位数位置
        target_weight = quantile
        idx = np.searchsorted(cumulative_weights, target_weight)

        if idx == 0:
            return -sorted_values.iloc[0]
        elif idx >= len(sorted_values):
            return -sorted_values.iloc[-1]
        else:
            # 线性插值
            w1, w2 = cumulative_weights[idx-1], cumulative_weights[idx]
            v1, v2 = sorted_values.iloc[idx-1], sorted_values.iloc[idx]
            interpolated_value = v1 + (v2 - v1) * (target_weight - w1) / (w2 - w1)
            return -interpolated_value

    def bootstrap_var_es(self, returns: pd.Series, confidence_level: Optional[float] = None) -> Tuple[float, float, Dict]:
        """
        使用自助法计算VaR和ES的置信区间

        Args:
            returns: 收益率序列
            confidence_level: 置信水平

        Returns:
            Tuple: (VaR均值, ES均值, 统计信息字典)
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level

        n_samples = self.hs_config.bootstrap_samples
        var_samples = []
        es_samples = []

        for _ in range(n_samples):
            # 自助法抽样
            bootstrap_returns = returns.sample(n=len(returns), replace=True)

            # 计算VaR和ES
            var = self.calculate_var(bootstrap_returns, confidence_level)
            es = self.calculate_es(bootstrap_returns, confidence_level)

            var_samples.append(var)
            es_samples.append(es)

        var_samples = np.array(var_samples)
        es_samples = np.array(es_samples)

        # 计算统计信息
        stats_info = {
            'var_mean': np.mean(var_samples),
            'var_std': np.std(var_samples),
            'var_95_ci': (np.percentile(var_samples, 2.5), np.percentile(var_samples, 97.5)),
            'es_mean': np.mean(es_samples),
            'es_std': np.std(es_samples),
            'es_95_ci': (np.percentile(es_samples, 2.5), np.percentile(es_samples, 97.5))
        }

        return stats_info['var_mean'], stats_info['es_mean'], stats_info


class MonteCarloSimulationModel(BaseRiskModel):
    """蒙特卡洛模拟法风险模型"""

    def __init__(self):
        super().__init__()
        self.mc_config = config_manager.get_monte_carlo_config()
        np.random.seed(self.mc_config.random_seed)

    def calculate_var(self, returns: pd.Series, confidence_level: Optional[float] = None) -> float:
        """
        使用蒙特卡洛模拟计算VaR

        Args:
            returns: 收益率序列
            confidence_level: 置信水平

        Returns:
            float: VaR值（正数表示损失）
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level

        # 生成模拟收益率
        simulated_returns = self._simulate_returns(returns)

        # 计算VaR
        alpha = 1 - confidence_level
        var = -np.percentile(simulated_returns, alpha * 100)

        return var

    def calculate_es(self, returns: pd.Series, confidence_level: Optional[float] = None) -> float:
        """
        使用蒙特卡洛模拟计算ES

        Args:
            returns: 收益率序列
            confidence_level: 置信水平

        Returns:
            float: ES值（正数表示损失）
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level

        # 生成模拟收益率
        simulated_returns = self._simulate_returns(returns)

        # 计算VaR阈值
        alpha = 1 - confidence_level
        var_threshold = np.percentile(simulated_returns, alpha * 100)

        # 计算ES
        tail_losses = simulated_returns[simulated_returns <= var_threshold]
        es = -np.mean(tail_losses) if len(tail_losses) > 0 else -var_threshold

        return es

    def _simulate_returns(self, returns: pd.Series) -> np.ndarray:
        """
        模拟收益率路径

        Args:
            returns: 历史收益率序列

        Returns:
            ndarray: 模拟的收益率
        """
        if self.mc_config.mc_model_params == "geometric_brownian_motion":
            return self._simulate_gbm(returns)
        elif self.mc_config.mc_model_params == "bootstrap":
            return self._simulate_bootstrap(returns)
        elif self.mc_config.mc_model_params == "garch":
            return self._simulate_garch(returns)
        else:
            logger.warning(f"Unknown MC model: {self.mc_config.mc_model_params}, using GBM")
            return self._simulate_gbm(returns)

    def _simulate_gbm(self, returns: pd.Series) -> np.ndarray:
        """几何布朗运动模拟"""
        mu = returns.mean()
        sigma = returns.std()

        # 生成随机数
        random_shocks = np.random.normal(0, 1, self.mc_config.num_simulations)

        # 对于日频数据，直接使用历史统计参数进行模拟
        # 持有期调整：如果持有期不是1天，需要调整
        holding_period_factor = np.sqrt(self.risk_config.holding_period)

        # 模拟收益率：基于历史均值和波动率
        simulated_returns = mu * self.risk_config.holding_period + sigma * holding_period_factor * random_shocks

        return simulated_returns

    def _simulate_bootstrap(self, returns: pd.Series) -> np.ndarray:
        """自助法模拟"""
        simulated_returns = np.random.choice(
            returns.values,
            size=self.mc_config.num_simulations,
            replace=True
        )

        # 调整为持有期
        holding_period_factor = np.sqrt(self.risk_config.holding_period)
        simulated_returns = simulated_returns * holding_period_factor

        return simulated_returns

    def _simulate_garch(self, returns: pd.Series) -> np.ndarray:
        """基于GARCH模型的模拟"""
        try:
            # 拟合GARCH模型
            parametric_model = ParametricRiskModel()
            garch_result = parametric_model.fit_garch_model(returns)

            # 获取模型参数
            volatility = garch_result['conditional_volatility']
            distribution = garch_result['distribution']

            # 生成随机冲击
            if distribution == 'student_t':
                nu = garch_result['distribution_params']['nu']
                random_shocks = np.random.standard_t(nu, self.mc_config.num_simulations)
            else:
                random_shocks = np.random.normal(0, 1, self.mc_config.num_simulations)

            # 生成收益率：GARCH模拟通常假设零均值
            holding_period_factor = np.sqrt(self.risk_config.holding_period)
            mean_return = returns.mean() * self.risk_config.holding_period  # 添加历史均值
            simulated_returns = mean_return + volatility * holding_period_factor * random_shocks

            return simulated_returns

        except Exception as e:
            logger.error(f"Error in GARCH simulation: {e}, falling back to GBM")
            return self._simulate_gbm(returns)

    def simulate_portfolio_paths(self, returns: pd.Series, num_paths: Optional[int] = None) -> np.ndarray:
        """
        模拟投资组合路径

        Args:
            returns: 收益率序列
            num_paths: 路径数量

        Returns:
            ndarray: 模拟的价格路径
        """
        if num_paths is None:
            num_paths = self.mc_config.num_simulations

        # 估计参数
        mu = returns.mean()
        sigma = returns.std()

        # 时间步长
        dt = 1 / 252  # 日频率
        num_steps = self.mc_config.num_steps_mc * self.risk_config.holding_period

        # 初始化路径矩阵
        paths = np.zeros((num_paths, num_steps + 1))
        paths[:, 0] = 1  # 初始价格标准化为1

        # 生成随机数
        random_matrix = np.random.normal(0, 1, (num_paths, num_steps))

        # 模拟路径
        for t in range(1, num_steps + 1):
            paths[:, t] = paths[:, t-1] * np.exp(
                (mu - 0.5 * sigma**2) * dt + sigma * np.sqrt(dt) * random_matrix[:, t-1]
            )

        return paths


class RiskModelFactory:
    """风险模型工厂类"""

    @staticmethod
    def create_model(model_type: str) -> BaseRiskModel:
        """
        创建风险模型实例

        Args:
            model_type: 模型类型 ('parametric', 'historical', 'monte_carlo')

        Returns:
            BaseRiskModel: 风险模型实例
        """
        if model_type.lower() == 'parametric':
            return ParametricRiskModel()
        elif model_type.lower() == 'historical':
            return HistoricalSimulationModel()
        elif model_type.lower() == 'monte_carlo':
            return MonteCarloSimulationModel()
        else:
            raise ValueError(f"Unknown model type: {model_type}")

    @staticmethod
    def get_all_models() -> Dict[str, BaseRiskModel]:
        """获取所有风险模型"""
        return {
            'parametric': ParametricRiskModel(),
            'historical': HistoricalSimulationModel(),
            'monte_carlo': MonteCarloSimulationModel()
        }
