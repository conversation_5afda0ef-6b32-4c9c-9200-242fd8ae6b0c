"""
风险贡献度计算模块
实现增量VaR (iVaR) 和边际VaR (MVaR) 的计算逻辑
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional, List
from scipy.optimize import minimize_scalar

from config.config_manager import config_manager
from core.risk_models import ParametricRiskModel, RiskModelFactory
from core.portfolio_manager import PortfolioManager
from utils.logger import get_module_logger

logger = get_module_logger("RiskContribution")


class RiskContributionCalculator:
    """风险贡献度计算器"""
    
    def __init__(self, portfolio_manager: PortfolioManager):
        """
        初始化风险贡献度计算器
        
        Args:
            portfolio_manager: 投资组合管理器
        """
        self.portfolio_manager = portfolio_manager
        self.config = config_manager.get_risk_contribution_config()
        self.risk_config = config_manager.get_risk_calculation_config()
        
        # 默认使用参数法计算风险贡献度（速度最快）
        self.risk_model = ParametricRiskModel()
        
        logger.info("Risk contribution calculator initialized")
    
    def calculate_incremental_var(self, returns_data: pd.DataFrame, 
                                confidence_level: Optional[float] = None) -> Dict[str, float]:
        """
        计算增量VaR (iVaR)
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            Dict: 各资产的增量VaR
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level
        
        # 计算当前组合的VaR
        portfolio_returns = self.portfolio_manager.calculate_portfolio_returns(returns_data)
        current_var = self.risk_model.calculate_var(portfolio_returns, confidence_level)
        
        incremental_vars = {}
        
        for asset in self.portfolio_manager.weights.index:
            if self.portfolio_manager.weights[asset] == 0:
                # 如果权重为0，iVaR就是添加该资产的影响
                incremental_vars[asset] = self._calculate_addition_impact(
                    asset, returns_data, confidence_level
                )
            else:
                # 计算移除该资产后的VaR
                new_weights = self.portfolio_manager.remove_asset(asset)
                new_portfolio_manager = PortfolioManager(new_weights, returns_data)
                new_portfolio_returns = new_portfolio_manager.calculate_portfolio_returns(returns_data)
                
                if len(new_portfolio_returns) > 0:
                    new_var = self.risk_model.calculate_var(new_portfolio_returns, confidence_level)
                    # iVaR = 当前VaR - 移除资产后的VaR
                    incremental_vars[asset] = current_var - new_var
                else:
                    incremental_vars[asset] = current_var
        
        logger.info(f"Calculated incremental VaR for {len(incremental_vars)} assets")
        return incremental_vars
    
    def calculate_marginal_var(self, returns_data: pd.DataFrame, 
                             confidence_level: Optional[float] = None,
                             use_analytical: Optional[bool] = None) -> Dict[str, float]:
        """
        计算边际VaR (MVaR)
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            use_analytical: 是否使用解析解
            
        Returns:
            Dict: 各资产的边际VaR
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level
        
        if use_analytical is None:
            use_analytical = self.config.use_analytical_mvar
        
        if use_analytical:
            return self._calculate_analytical_mvar(returns_data, confidence_level)
        else:
            return self._calculate_numerical_mvar(returns_data, confidence_level)
    
    def _calculate_analytical_mvar(self, returns_data: pd.DataFrame, 
                                 confidence_level: float) -> Dict[str, float]:
        """
        使用解析解计算边际VaR
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            Dict: 各资产的边际VaR
        """
        # 获取协方差矩阵
        covariance_matrix = self.portfolio_manager.get_covariance_matrix(returns_data)
        
        # 确保权重和协方差矩阵的资产匹配
        common_assets = covariance_matrix.index.intersection(self.portfolio_manager.weights.index)
        aligned_cov = covariance_matrix.loc[common_assets, common_assets]
        aligned_weights = self.portfolio_manager.weights[common_assets]
        
        # 计算组合方差
        portfolio_variance = np.dot(aligned_weights.values, 
                                  np.dot(aligned_cov.values, aligned_weights.values))
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        # 计算VaR的分位数
        alpha = 1 - confidence_level
        if hasattr(self.risk_model, 'param_config'):
            # 使用参数法的分布假设
            if self.risk_model.param_config.distribution_assumption == 'student_t':
                # 对于t分布，需要拟合模型获取自由度参数
                portfolio_returns = self.portfolio_manager.calculate_portfolio_returns(returns_data)
                garch_result = self.risk_model.fit_garch_model(portfolio_returns)
                if 'nu' in garch_result['distribution_params']:
                    from scipy import stats
                    nu = garch_result['distribution_params']['nu']
                    quantile = stats.t.ppf(alpha, df=nu)
                else:
                    quantile = stats.norm.ppf(alpha)
            else:
                from scipy import stats
                quantile = stats.norm.ppf(alpha)
        else:
            from scipy import stats
            quantile = stats.norm.ppf(alpha)
        
        # 计算边际VaR: MVaR_i = -quantile * (Σw_j * Cov(i,j)) / σ_p
        marginal_vars = {}
        
        for i, asset in enumerate(common_assets):
            # 计算资产i与组合的协方差
            asset_portfolio_cov = np.dot(aligned_cov.iloc[i, :].values, aligned_weights.values)
            
            # 边际VaR
            if portfolio_volatility > 0:
                marginal_var = -quantile * asset_portfolio_cov / portfolio_volatility
                # 调整为持有期
                holding_period_factor = np.sqrt(self.risk_config.holding_period)
                marginal_var *= holding_period_factor
            else:
                marginal_var = 0.0
            
            marginal_vars[asset] = marginal_var
        
        # 为不在协方差矩阵中的资产设置0
        for asset in self.portfolio_manager.weights.index:
            if asset not in marginal_vars:
                marginal_vars[asset] = 0.0
        
        logger.info("Calculated analytical marginal VaR")
        return marginal_vars
    
    def _calculate_numerical_mvar(self, returns_data: pd.DataFrame, 
                                confidence_level: float) -> Dict[str, float]:
        """
        使用数值方法计算边际VaR
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            Dict: 各资产的边际VaR
        """
        # 计算当前组合的VaR
        portfolio_returns = self.portfolio_manager.calculate_portfolio_returns(returns_data)
        current_var = self.risk_model.calculate_var(portfolio_returns, confidence_level)
        
        marginal_vars = {}
        delta_weight = self.config.delta_weight
        
        for asset in self.portfolio_manager.weights.index:
            # 增加权重
            new_weights_up = self.portfolio_manager.simulate_weight_change(asset, delta_weight)
            new_portfolio_manager_up = PortfolioManager(new_weights_up, returns_data)
            new_portfolio_returns_up = new_portfolio_manager_up.calculate_portfolio_returns(returns_data)
            
            if len(new_portfolio_returns_up) > 0:
                new_var_up = self.risk_model.calculate_var(new_portfolio_returns_up, confidence_level)
                # 边际VaR ≈ (VaR(w+Δw) - VaR(w)) / Δw
                marginal_var = (new_var_up - current_var) / delta_weight
            else:
                marginal_var = 0.0
            
            marginal_vars[asset] = marginal_var
        
        logger.info("Calculated numerical marginal VaR")
        return marginal_vars
    
    def _calculate_addition_impact(self, asset: str, returns_data: pd.DataFrame, 
                                 confidence_level: float) -> float:
        """
        计算添加资产的影响
        
        Args:
            asset: 资产名称
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            float: 添加资产的VaR影响
        """
        # 当前组合VaR
        portfolio_returns = self.portfolio_manager.calculate_portfolio_returns(returns_data)
        current_var = self.risk_model.calculate_var(portfolio_returns, confidence_level)
        
        # 添加少量该资产
        small_weight = 0.01  # 1%权重
        new_weights = self.portfolio_manager.add_asset(asset, small_weight)
        new_portfolio_manager = PortfolioManager(new_weights, returns_data)
        new_portfolio_returns = new_portfolio_manager.calculate_portfolio_returns(returns_data)
        
        if len(new_portfolio_returns) > 0:
            new_var = self.risk_model.calculate_var(new_portfolio_returns, confidence_level)
            # 标准化为单位权重的影响
            addition_impact = (new_var - current_var) / small_weight
        else:
            addition_impact = 0.0
        
        return addition_impact
    
    def calculate_component_var(self, returns_data: pd.DataFrame, 
                              confidence_level: Optional[float] = None) -> Dict[str, float]:
        """
        计算成分VaR (Component VaR)
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            Dict: 各资产的成分VaR
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level
        
        # 计算边际VaR
        marginal_vars = self.calculate_marginal_var(returns_data, confidence_level)
        
        # 成分VaR = 权重 × 边际VaR
        component_vars = {}
        for asset in self.portfolio_manager.weights.index:
            weight = self.portfolio_manager.weights[asset]
            mvar = marginal_vars.get(asset, 0.0)
            component_vars[asset] = weight * mvar
        
        logger.info("Calculated component VaR")
        return component_vars
    
    def calculate_risk_contribution_summary(self, returns_data: pd.DataFrame, 
                                          confidence_level: Optional[float] = None) -> Dict[str, Dict[str, float]]:
        """
        计算风险贡献度摘要
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            Dict: 包含所有风险贡献度指标的字典
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level
        
        # 计算组合VaR
        portfolio_returns = self.portfolio_manager.calculate_portfolio_returns(returns_data)
        portfolio_var = self.risk_model.calculate_var(portfolio_returns, confidence_level)
        
        # 计算各种风险贡献度指标
        incremental_vars = self.calculate_incremental_var(returns_data, confidence_level)
        marginal_vars = self.calculate_marginal_var(returns_data, confidence_level)
        component_vars = self.calculate_component_var(returns_data, confidence_level)
        
        # 组织结果
        summary = {}
        for asset in self.portfolio_manager.weights.index:
            summary[asset] = {
                'weight': self.portfolio_manager.weights[asset],
                'incremental_var': incremental_vars.get(asset, 0.0),
                'marginal_var': marginal_vars.get(asset, 0.0),
                'component_var': component_vars.get(asset, 0.0),
                'var_contribution_pct': (component_vars.get(asset, 0.0) / portfolio_var * 100) if portfolio_var > 0 else 0.0
            }
        
        # 添加组合总体信息
        summary['PORTFOLIO'] = {
            'weight': 1.0,
            'total_var': portfolio_var,
            'sum_component_var': sum(component_vars.values()),
            'sum_incremental_var': sum(incremental_vars.values())
        }
        
        logger.info("Calculated risk contribution summary")
        return summary
    
    def validate_risk_contributions(self, component_vars: Dict[str, float], 
                                  portfolio_var: float, tolerance: float = 0.01) -> bool:
        """
        验证风险贡献度的一致性
        
        Args:
            component_vars: 成分VaR
            portfolio_var: 组合VaR
            tolerance: 容忍度
            
        Returns:
            bool: 是否通过验证
        """
        sum_component_var = sum(component_vars.values())
        difference = abs(sum_component_var - portfolio_var)
        relative_error = difference / portfolio_var if portfolio_var > 0 else 0
        
        is_valid = relative_error <= tolerance
        
        if not is_valid:
            logger.warning(f"Risk contribution validation failed: "
                         f"Sum of component VaR ({sum_component_var:.6f}) != "
                         f"Portfolio VaR ({portfolio_var:.6f}), "
                         f"Relative error: {relative_error:.4f}")
        else:
            logger.info("Risk contribution validation passed")
        
        return is_valid
