"""
权重敏感性分析模块
分析投资组合权重变动对VaR和ES的影响
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from config.config_manager import config_manager
from core.portfolio_manager import PortfolioManager
from core.risk_models import RiskModelFactory
from utils.logger import get_module_logger

logger = get_module_logger("WeightSensitivity")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class WeightSensitivityAnalyzer:
    """权重敏感性分析器"""
    
    def __init__(self, portfolio_manager: PortfolioManager):
        """
        初始化权重敏感性分析器
        
        Args:
            portfolio_manager: 投资组合管理器
        """
        self.portfolio_manager = portfolio_manager
        self.config = config_manager.get_config()
        self.sensitivity_config = self.config.get('weight_sensitivity', {})
        self.risk_config = config_manager.get_risk_calculation_config()
        
        # 默认使用参数法进行敏感性分析（速度较快）
        self.risk_model = RiskModelFactory.create_model('parametric')
        
        logger.info("Weight sensitivity analyzer initialized")
    
    def identify_top_influential_assets(self, returns_data: pd.DataFrame, 
                                      method: str = 'component_var') -> List[str]:
        """
        识别最有影响力的资产
        
        Args:
            returns_data: 收益率数据
            method: 识别方法 ('component_var', 'volatility', 'weight')
            
        Returns:
            List: 最有影响力的资产列表
        """
        top_count = self.sensitivity_config.get('top_assets_count', 3)
        
        if method == 'component_var':
            # 基于成分VaR识别
            from core.risk_contribution import RiskContributionCalculator
            risk_calc = RiskContributionCalculator(self.portfolio_manager)
            component_vars = risk_calc.calculate_component_var(returns_data)
            
            # 按绝对值排序
            sorted_assets = sorted(component_vars.items(), 
                                 key=lambda x: abs(x[1]), reverse=True)
            top_assets = [asset for asset, _ in sorted_assets[:top_count]]
            
        elif method == 'volatility':
            # 基于波动率识别
            volatilities = returns_data.std()
            top_assets = volatilities.nlargest(top_count).index.tolist()
            
        elif method == 'weight':
            # 基于权重识别
            top_assets = self.portfolio_manager.weights.nlargest(top_count).index.tolist()
            
        else:
            logger.warning(f"Unknown method {method}, using component_var")
            return self.identify_top_influential_assets(returns_data, 'component_var')
        
        logger.info(f"Identified top {top_count} influential assets: {top_assets}")
        return top_assets
    
    def single_asset_sensitivity_analysis(self, returns_data: pd.DataFrame,
                                        assets: Optional[List[str]] = None) -> Dict[str, Dict[str, Any]]:
        """
        单资产权重敏感性分析
        
        Args:
            returns_data: 收益率数据
            assets: 要分析的资产列表，如果为None则自动识别
            
        Returns:
            Dict: 敏感性分析结果
        """
        if assets is None:
            assets = self.identify_top_influential_assets(returns_data)
        
        weight_range = self.sensitivity_config.get('single_asset_weight_range', 0.05)
        step_size = self.sensitivity_config.get('weight_step_size', 0.01)
        
        results = {}
        
        for asset in assets:
            if asset not in self.portfolio_manager.weights.index:
                logger.warning(f"Asset {asset} not in portfolio, skipping")
                continue
                
            logger.info(f"Analyzing sensitivity for asset: {asset}")
            
            current_weight = self.portfolio_manager.weights[asset]
            
            # 生成权重变动范围
            min_weight = max(0.001, current_weight - weight_range)
            max_weight = min(0.999, current_weight + weight_range)
            weight_changes = np.arange(-weight_range, weight_range + step_size, step_size)
            
            sensitivity_data = []
            
            for delta_weight in weight_changes:
                new_weight = current_weight + delta_weight
                
                # 确保权重在合理范围内
                if new_weight < 0.001 or new_weight > 0.999:
                    continue
                
                # 创建新的权重配置
                new_weights = self._adjust_single_asset_weight(asset, delta_weight)
                
                # 计算新的VaR和ES
                var, es = self._calculate_portfolio_risk(new_weights, returns_data)
                
                sensitivity_data.append({
                    'weight_change': delta_weight,
                    'new_weight': new_weight,
                    'var': var,
                    'es': es
                })
            
            if sensitivity_data:
                results[asset] = {
                    'current_weight': current_weight,
                    'sensitivity_data': pd.DataFrame(sensitivity_data),
                    'weight_range': weight_range
                }
        
        logger.info(f"Completed single asset sensitivity analysis for {len(results)} assets")
        return results
    
    def two_asset_scenario_analysis(self, returns_data: pd.DataFrame,
                                  asset_pairs: Optional[List[Tuple[str, str]]] = None) -> Dict[str, Dict[str, Any]]:
        """
        两资产权重协同变动分析
        
        Args:
            returns_data: 收益率数据
            asset_pairs: 资产对列表，如果为None则自动选择
            
        Returns:
            Dict: 协同变动分析结果
        """
        if asset_pairs is None:
            asset_pairs = self._identify_correlated_asset_pairs(returns_data)
        
        scenario_count = self.sensitivity_config.get('scenario_count', 3)
        results = {}
        
        for i, (asset1, asset2) in enumerate(asset_pairs):
            if asset1 not in self.portfolio_manager.weights.index or \
               asset2 not in self.portfolio_manager.weights.index:
                logger.warning(f"Asset pair ({asset1}, {asset2}) not in portfolio, skipping")
                continue
            
            logger.info(f"Analyzing scenario for asset pair: ({asset1}, {asset2})")
            
            scenarios = self._generate_two_asset_scenarios(asset1, asset2, scenario_count)
            scenario_results = []
            
            for scenario_name, weight_changes in scenarios.items():
                new_weights = self._adjust_two_asset_weights(weight_changes)
                var, es = self._calculate_portfolio_risk(new_weights, returns_data)
                
                scenario_results.append({
                    'scenario': scenario_name,
                    'asset1_change': weight_changes[asset1],
                    'asset2_change': weight_changes[asset2],
                    'var': var,
                    'es': es
                })
            
            results[f"{asset1}_{asset2}"] = {
                'asset_pair': (asset1, asset2),
                'scenarios': pd.DataFrame(scenario_results)
            }
        
        logger.info(f"Completed two asset scenario analysis for {len(results)} pairs")
        return results
    
    def _adjust_single_asset_weight(self, asset: str, delta_weight: float) -> pd.Series:
        """调整单个资产权重"""
        new_weights = self.portfolio_manager.weights.copy()
        
        # 调整目标资产权重
        new_weights[asset] += delta_weight
        
        # 按比例调整其他资产权重以保持总和为1
        other_assets = new_weights.index[new_weights.index != asset]
        if len(other_assets) > 0:
            adjustment_factor = (1 - new_weights[asset]) / new_weights[other_assets].sum()
            new_weights[other_assets] *= adjustment_factor
        
        return new_weights
    
    def _adjust_two_asset_weights(self, weight_changes: Dict[str, float]) -> pd.Series:
        """调整两个资产权重"""
        new_weights = self.portfolio_manager.weights.copy()
        
        # 应用权重变化
        for asset, delta_weight in weight_changes.items():
            new_weights[asset] += delta_weight
        
        # 调整其他资产权重
        changed_assets = list(weight_changes.keys())
        other_assets = new_weights.index[~new_weights.index.isin(changed_assets)]
        
        if len(other_assets) > 0:
            total_changed_weight = sum(new_weights[changed_assets])
            remaining_weight = 1 - total_changed_weight
            
            if remaining_weight > 0:
                adjustment_factor = remaining_weight / new_weights[other_assets].sum()
                new_weights[other_assets] *= adjustment_factor
        
        return new_weights
    
    def _calculate_portfolio_risk(self, weights: pd.Series, returns_data: pd.DataFrame) -> Tuple[float, float]:
        """计算给定权重下的组合风险"""
        temp_portfolio = PortfolioManager(weights, returns_data)
        portfolio_returns = temp_portfolio.calculate_portfolio_returns(returns_data)
        
        if len(portfolio_returns) == 0:
            return 0.0, 0.0
        
        var, es = self.risk_model.calculate_var_es(portfolio_returns)
        return var, es
    
    def _identify_correlated_asset_pairs(self, returns_data: pd.DataFrame) -> List[Tuple[str, str]]:
        """识别相关性高的资产对"""
        correlation_matrix = returns_data.corr()
        
        # 找到相关性最高的资产对
        pairs = []
        assets = list(self.portfolio_manager.weights.index)
        
        for i in range(len(assets)):
            for j in range(i+1, len(assets)):
                asset1, asset2 = assets[i], assets[j]
                if asset1 in correlation_matrix.index and asset2 in correlation_matrix.columns:
                    corr = abs(correlation_matrix.loc[asset1, asset2])
                    pairs.append((asset1, asset2, corr))
        
        # 按相关性排序，选择前几对
        pairs.sort(key=lambda x: x[2], reverse=True)
        top_pairs = [(asset1, asset2) for asset1, asset2, _ in pairs[:3]]
        
        logger.info(f"Identified correlated asset pairs: {top_pairs}")
        return top_pairs
    
    def _generate_two_asset_scenarios(self, asset1: str, asset2: str, scenario_count: int) -> Dict[str, Dict[str, float]]:
        """生成两资产协同变动情景"""
        scenarios = {}
        
        # 情景1：一个增加，另一个减少
        scenarios['对冲情景'] = {asset1: 0.03, asset2: -0.03}
        
        # 情景2：两者同时增加
        scenarios['同向增加'] = {asset1: 0.02, asset2: 0.02}
        
        # 情景3：两者同时减少
        scenarios['同向减少'] = {asset1: -0.02, asset2: -0.02}
        
        return scenarios

    def visualize_single_asset_sensitivity(self, sensitivity_results: Dict[str, Dict[str, Any]],
                                         save_path: Optional[str] = None) -> None:
        """
        可视化单资产敏感性分析结果

        Args:
            sensitivity_results: 敏感性分析结果
            save_path: 保存路径
        """
        n_assets = len(sensitivity_results)
        if n_assets == 0:
            logger.warning("No sensitivity results to visualize")
            return

        fig, axes = plt.subplots(2, n_assets, figsize=(5*n_assets, 10))
        if n_assets == 1:
            axes = axes.reshape(2, 1)

        for i, (asset, result) in enumerate(sensitivity_results.items()):
            data = result['sensitivity_data']
            current_weight = result['current_weight']

            # VaR敏感性图
            axes[0, i].plot(data['weight_change'], data['var'] * 100, 'b-', linewidth=2, label='VaR')
            axes[0, i].axvline(x=0, color='r', linestyle='--', alpha=0.7, label='当前权重')
            axes[0, i].set_title(f'{asset} - VaR敏感性分析', fontsize=12, fontweight='bold')
            axes[0, i].set_xlabel('权重变化', fontsize=10)
            axes[0, i].set_ylabel('VaR (%)', fontsize=10)
            axes[0, i].grid(True, alpha=0.3)
            axes[0, i].legend()

            # ES敏感性图
            axes[1, i].plot(data['weight_change'], data['es'] * 100, 'g-', linewidth=2, label='ES')
            axes[1, i].axvline(x=0, color='r', linestyle='--', alpha=0.7, label='当前权重')
            axes[1, i].set_title(f'{asset} - ES敏感性分析', fontsize=12, fontweight='bold')
            axes[1, i].set_xlabel('权重变化', fontsize=10)
            axes[1, i].set_ylabel('ES (%)', fontsize=10)
            axes[1, i].grid(True, alpha=0.3)
            axes[1, i].legend()

            # 添加当前权重信息
            axes[0, i].text(0.02, 0.98, f'当前权重: {current_weight:.2%}',
                          transform=axes[0, i].transAxes, verticalalignment='top',
                          bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Single asset sensitivity chart saved to {save_path}")

        plt.show()

    def visualize_two_asset_scenarios(self, scenario_results: Dict[str, Dict[str, Any]],
                                    save_path: Optional[str] = None) -> None:
        """
        可视化两资产协同变动分析结果

Args:
            scenario_results: 协同变动分析结果
            save_path: 保存路径
        """
        if not scenario_results:
            logger.warning("No scenario results to visualize")
            return

        n_pairs = len(scenario_results)
        fig, axes = plt.subplots(1, n_pairs, figsize=(6*n_pairs, 6))
        if n_pairs == 1:
            axes = [axes]

        for i, (pair_name, result) in enumerate(scenario_results.items()):
            data = result['scenarios']
            asset1, asset2 = result['asset_pair']

            # 创建条形图
            scenarios = data['scenario'].tolist()
            var_values = (data['var'] * 100).tolist()
            es_values = (data['es'] * 100).tolist()

            x = np.arange(len(scenarios))
            width = 0.35

            bars1 = axes[i].bar(x - width/2, var_values, width, label='VaR', color='skyblue', alpha=0.8)
            bars2 = axes[i].bar(x + width/2, es_values, width, label='ES', color='lightcoral', alpha=0.8)

            axes[i].set_title(f'{asset1} & {asset2}\n协同变动情景分析', fontsize=12, fontweight='bold')
            axes[i].set_xlabel('情景', fontsize=10)
            axes[i].set_ylabel('风险指标 (%)', fontsize=10)
            axes[i].set_xticks(x)
            axes[i].set_xticklabels(scenarios, rotation=45, ha='right')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)

            # 添加数值标签
            for bar in bars1:
                height = bar.get_height()
                axes[i].text(bar.get_x() + bar.get_width()/2., height,
                           f'{height:.2f}%', ha='center', va='bottom', fontsize=8)

            for bar in bars2:
                height = bar.get_height()
                axes[i].text(bar.get_x() + bar.get_width()/2., height,
                           f'{height:.2f}%', ha='center', va='bottom', fontsize=8)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Two asset scenario chart saved to {save_path}")

        plt.show()

    def generate_sensitivity_report(self, sensitivity_results: Dict[str, Dict[str, Any]],
                                  scenario_results: Dict[str, Dict[str, Any]]) -> str:
        """
        生成敏感性分析报告

        Args:
            sensitivity_results: 单资产敏感性分析结果
            scenario_results: 两资产协同变动分析结果

        Returns:
            str: 报告内容
        """
        report_lines = []

        # 报告标题
        report_lines.append("# 投资组合权重敏感性分析报告")
        report_lines.append("")
        report_lines.append(f"**分析时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**置信水平**: {self.risk_config.confidence_level * 100:.1f}%")
        report_lines.append("")

        # 单资产敏感性分析
        if sensitivity_results:
            report_lines.append("## 1. 单资产权重敏感性分析")
            report_lines.append("")

            for asset, result in sensitivity_results.items():
                data = result['sensitivity_data']
                current_weight = result['current_weight']

                # 计算敏感性指标
                var_sensitivity = (data['var'].max() - data['var'].min()) * 100
                es_sensitivity = (data['es'].max() - data['es'].min()) * 100

                report_lines.append(f"### {asset}")
                report_lines.append(f"- **当前权重**: {current_weight:.2%}")
                report_lines.append(f"- **VaR敏感性范围**: {var_sensitivity:.2f}%")
                report_lines.append(f"- **ES敏感性范围**: {es_sensitivity:.2f}%")

                # 找到最优和最差权重
                min_var_idx = data['var'].idxmin()
                max_var_idx = data['var'].idxmax()

                report_lines.append(f"- **最低VaR权重**: {data.loc[min_var_idx, 'new_weight']:.2%} (VaR: {data.loc[min_var_idx, 'var']*100:.2f}%)")
                report_lines.append(f"- **最高VaR权重**: {data.loc[max_var_idx, 'new_weight']:.2%} (VaR: {data.loc[max_var_idx, 'var']*100:.2f}%)")
                report_lines.append("")

        # 两资产协同变动分析
        if scenario_results:
            report_lines.append("## 2. 两资产协同变动分析")
            report_lines.append("")

            for pair_name, result in scenario_results.items():
                asset1, asset2 = result['asset_pair']
                data = result['scenarios']

                report_lines.append(f"### {asset1} & {asset2}")
                report_lines.append("")
                report_lines.append("| 情景 | VaR (%) | ES (%) | 风险变化 |")
                report_lines.append("|:-----|:--------|:-------|:---------|")

                for _, row in data.iterrows():
                    var_pct = row['var'] * 100
                    es_pct = row['es'] * 100
                    risk_level = "高" if var_pct > 2.0 else "中" if var_pct > 1.0 else "低"
                    report_lines.append(f"| {row['scenario']} | {var_pct:.2f} | {es_pct:.2f} | {risk_level} |")

                report_lines.append("")

        # 总结和建议
        report_lines.append("## 3. 分析总结与建议")
        report_lines.append("")

        if sensitivity_results:
            # 找到最敏感的资产
            max_sensitivity = 0
            most_sensitive_asset = ""

            for asset, result in sensitivity_results.items():
                data = result['sensitivity_data']
                var_range = (data['var'].max() - data['var'].min()) * 100
                if var_range > max_sensitivity:
                    max_sensitivity = var_range
                    most_sensitive_asset = asset

            report_lines.append(f"- **最敏感资产**: {most_sensitive_asset} (VaR变化范围: {max_sensitivity:.2f}%)")
            report_lines.append("- **风险管理建议**: 对敏感性高的资产应谨慎调整权重，小幅变动可能带来显著风险变化")
            report_lines.append("- **优化方向**: 考虑降低高敏感性资产的权重，增加风险分散效果")

        report_lines.append("")
        report_lines.append("---")
        report_lines.append("*本报告基于历史数据分析，实际投资决策应结合市场环境和投资目标综合考虑*")

        return "\n".join(report_lines)
