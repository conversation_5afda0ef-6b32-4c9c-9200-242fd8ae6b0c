"""
边际风险分析模块
计算边际VaR (MVaR) 和边际ES (MES)，分析各资产的风险贡献
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from config.config_manager import config_manager
from core.portfolio_manager import PortfolioManager
from core.risk_models import RiskModelFactory
from core.risk_contribution import RiskContributionCalculator
from utils.logger import get_module_logger

logger = get_module_logger("MarginalRisk")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class MarginalRiskAnalyzer:
    """边际风险分析器"""
    
    def __init__(self, portfolio_manager: PortfolioManager):
        """
        初始化边际风险分析器
        
        Args:
            portfolio_manager: 投资组合管理器
        """
        self.portfolio_manager = portfolio_manager
        self.config = config_manager.get_config()
        self.marginal_config = self.config.get('marginal_risk', {})
        self.risk_config = config_manager.get_risk_calculation_config()
        
        # 使用参数法计算边际风险
        self.risk_model = RiskModelFactory.create_model('parametric')
        self.risk_contribution_calc = RiskContributionCalculator(portfolio_manager)
        
        logger.info("Marginal risk analyzer initialized")
    
    def calculate_marginal_var(self, returns_data: pd.DataFrame, 
                             confidence_level: Optional[float] = None) -> Dict[str, float]:
        """
        计算边际VaR (MVaR)
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            Dict: 各资产的边际VaR
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level
        
        logger.info("Calculating Marginal VaR")
        
        # 使用现有的边际VaR计算方法
        marginal_vars = self.risk_contribution_calc.calculate_marginal_var(
            returns_data, confidence_level
        )
        
        logger.info(f"Calculated MVaR for {len(marginal_vars)} assets")
        return marginal_vars
    
    def calculate_marginal_es(self, returns_data: pd.DataFrame, 
                            confidence_level: Optional[float] = None) -> Dict[str, float]:
        """
        计算边际ES (MES)
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            Dict: 各资产的边际ES
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level
        
        logger.info("Calculating Marginal ES")
        
        # 计算组合ES
        portfolio_returns = self.portfolio_manager.calculate_portfolio_returns(returns_data)
        _, portfolio_es = self.risk_model.calculate_var_es(portfolio_returns, confidence_level)
        
        marginal_es = {}
        delta_weight = self.marginal_config.get('delta_weight', 0.01)
        
        for asset in self.portfolio_manager.weights.index:
            if asset not in returns_data.columns:
                marginal_es[asset] = 0.0
                continue
            
            # 增加该资产权重
            new_weights = self.portfolio_manager.weights.copy()
            new_weights[asset] += delta_weight
            
            # 按比例调整其他资产权重
            other_assets = new_weights.index[new_weights.index != asset]
            if len(other_assets) > 0:
                adjustment_factor = (1 - new_weights[asset]) / new_weights[other_assets].sum()
                new_weights[other_assets] *= adjustment_factor
            
            # 计算新的组合ES
            temp_portfolio = PortfolioManager(new_weights, returns_data)
            new_portfolio_returns = temp_portfolio.calculate_portfolio_returns(returns_data)
            
            if len(new_portfolio_returns) > 0:
                _, new_es = self.risk_model.calculate_var_es(new_portfolio_returns, confidence_level)
                # 边际ES = (新ES - 原ES) / 权重变化
                marginal_es[asset] = (new_es - portfolio_es) / delta_weight
            else:
                marginal_es[asset] = 0.0
        
        logger.info(f"Calculated MES for {len(marginal_es)} assets")
        return marginal_es
    
    def calculate_component_var_es(self, returns_data: pd.DataFrame, 
                                 confidence_level: Optional[float] = None) -> Dict[str, Dict[str, float]]:
        """
        计算成分VaR和成分ES
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            Dict: 各资产的成分VaR和成分ES
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level
        
        logger.info("Calculating Component VaR and ES")
        
        # 计算边际VaR和边际ES
        marginal_vars = self.calculate_marginal_var(returns_data, confidence_level)
        marginal_es = self.calculate_marginal_es(returns_data, confidence_level)
        
        # 计算成分风险 = 权重 × 边际风险
        component_risks = {}
        
        for asset in self.portfolio_manager.weights.index:
            weight = self.portfolio_manager.weights[asset]
            mvar = marginal_vars.get(asset, 0.0)
            mes = marginal_es.get(asset, 0.0)
            
            component_risks[asset] = {
                'component_var': weight * mvar,
                'component_es': weight * mes,
                'marginal_var': mvar,
                'marginal_es': mes,
                'weight': weight
            }
        
        logger.info("Calculated component VaR and ES")
        return component_risks
    
    def analyze_risk_contributions(self, returns_data: pd.DataFrame, 
                                 confidence_level: Optional[float] = None) -> Dict[str, Any]:
        """
        综合分析风险贡献度
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            Dict: 风险贡献度分析结果
        """
        if confidence_level is None:
            confidence_level = self.risk_config.confidence_level
        
        logger.info("Analyzing comprehensive risk contributions")
        
        # 计算组合总风险
        portfolio_returns = self.portfolio_manager.calculate_portfolio_returns(returns_data)
        portfolio_var, portfolio_es = self.risk_model.calculate_var_es(portfolio_returns, confidence_level)
        
        # 计算各种风险贡献度指标
        component_risks = self.calculate_component_var_es(returns_data, confidence_level)
        
        # 计算风险贡献度百分比
        total_component_var = sum([risk['component_var'] for risk in component_risks.values()])
        total_component_es = sum([risk['component_es'] for risk in component_risks.values()])
        
        risk_analysis = {
            'portfolio_metrics': {
                'portfolio_var': portfolio_var,
                'portfolio_es': portfolio_es,
                'total_component_var': total_component_var,
                'total_component_es': total_component_es
            },
            'asset_contributions': {},
            'risk_rankings': {}
        }
        
        # 计算各资产的风险贡献度
        for asset, risks in component_risks.items():
            var_contribution_pct = (risks['component_var'] / total_component_var * 100) if total_component_var != 0 else 0
            es_contribution_pct = (risks['component_es'] / total_component_es * 100) if total_component_es != 0 else 0
            
            risk_analysis['asset_contributions'][asset] = {
                **risks,
                'var_contribution_pct': var_contribution_pct,
                'es_contribution_pct': es_contribution_pct
            }
        
        # 风险贡献度排名
        var_ranking = sorted(risk_analysis['asset_contributions'].items(), 
                           key=lambda x: abs(x[1]['component_var']), reverse=True)
        es_ranking = sorted(risk_analysis['asset_contributions'].items(), 
                          key=lambda x: abs(x[1]['component_es']), reverse=True)
        
        risk_analysis['risk_rankings'] = {
            'var_ranking': [(asset, contrib['component_var'], contrib['var_contribution_pct']) 
                          for asset, contrib in var_ranking],
            'es_ranking': [(asset, contrib['component_es'], contrib['es_contribution_pct']) 
                         for asset, contrib in es_ranking]
        }
        
        logger.info("Completed comprehensive risk contribution analysis")
        return risk_analysis
    
    def identify_risk_concentrations(self, risk_analysis: Dict[str, Any], 
                                   threshold: Optional[float] = None) -> Dict[str, List[str]]:
        """
        识别风险集中度
        
        Args:
            risk_analysis: 风险分析结果
            threshold: 风险贡献度阈值
            
        Returns:
            Dict: 风险集中资产列表
        """
        if threshold is None:
            threshold = self.marginal_config.get('risk_attribution_threshold', 0.01)
        
        threshold_pct = threshold * 100
        
        high_var_contributors = []
        high_es_contributors = []
        
        for asset, contrib in risk_analysis['asset_contributions'].items():
            if contrib['var_contribution_pct'] > threshold_pct:
                high_var_contributors.append(asset)
            if contrib['es_contribution_pct'] > threshold_pct:
                high_es_contributors.append(asset)
        
        concentrations = {
            'high_var_contributors': high_var_contributors,
            'high_es_contributors': high_es_contributors,
            'threshold_pct': threshold_pct
        }
        
        logger.info(f"Identified {len(high_var_contributors)} high VaR contributors and "
                   f"{len(high_es_contributors)} high ES contributors")
        
        return concentrations
    
    def visualize_risk_contributions(self, risk_analysis: Dict[str, Any], 
                                   save_path: Optional[str] = None) -> None:
        """
        可视化风险贡献度分析结果
        
        Args:
            risk_analysis: 风险分析结果
            save_path: 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 准备数据
        assets = list(risk_analysis['asset_contributions'].keys())
        var_contributions = [risk_analysis['asset_contributions'][asset]['var_contribution_pct'] 
                           for asset in assets]
        es_contributions = [risk_analysis['asset_contributions'][asset]['es_contribution_pct'] 
                          for asset in assets]
        marginal_vars = [risk_analysis['asset_contributions'][asset]['marginal_var'] * 100 
                        for asset in assets]
        marginal_es = [risk_analysis['asset_contributions'][asset]['marginal_es'] * 100 
                      for asset in assets]
        
        # 1. VaR贡献度饼图 (只显示正值)
        positive_var_contributions = [max(0, x) for x in var_contributions]
        if sum(positive_var_contributions) > 0:
            axes[0, 0].pie(positive_var_contributions, labels=assets, autopct='%1.1f%%', startangle=90)
            axes[0, 0].set_title('VaR贡献度分布 (正值)', fontsize=14, fontweight='bold')
        else:
            axes[0, 0].text(0.5, 0.5, '无正值贡献', ha='center', va='center', transform=axes[0, 0].transAxes)
            axes[0, 0].set_title('VaR贡献度分布', fontsize=14, fontweight='bold')

        # 2. ES贡献度饼图 (只显示正值)
        positive_es_contributions = [max(0, x) for x in es_contributions]
        if sum(positive_es_contributions) > 0:
            axes[0, 1].pie(positive_es_contributions, labels=assets, autopct='%1.1f%%', startangle=90)
            axes[0, 1].set_title('ES贡献度分布 (正值)', fontsize=14, fontweight='bold')
        else:
            axes[0, 1].text(0.5, 0.5, '无正值贡献', ha='center', va='center', transform=axes[0, 1].transAxes)
            axes[0, 1].set_title('ES贡献度分布', fontsize=14, fontweight='bold')
        
        # 3. 边际VaR条形图
        bars1 = axes[1, 0].bar(assets, marginal_vars, color='skyblue', alpha=0.8)
        axes[1, 0].set_title('边际VaR (MVaR)', fontsize=14, fontweight='bold')
        axes[1, 0].set_ylabel('边际VaR (%)', fontsize=12)
        axes[1, 0].tick_params(axis='x', rotation=45)
        axes[1, 0].grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            axes[1, 0].text(bar.get_x() + bar.get_width()/2., height,
                          f'{height:.2f}%', ha='center', va='bottom', fontsize=9)
        
        # 4. 边际ES条形图
        bars2 = axes[1, 1].bar(assets, marginal_es, color='lightcoral', alpha=0.8)
        axes[1, 1].set_title('边际ES (MES)', fontsize=14, fontweight='bold')
        axes[1, 1].set_ylabel('边际ES (%)', fontsize=12)
        axes[1, 1].tick_params(axis='x', rotation=45)
        axes[1, 1].grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars2:
            height = bar.get_height()
            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height,
                          f'{height:.2f}%', ha='center', va='bottom', fontsize=9)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Risk contribution chart saved to {save_path}")
        
        plt.show()

    def generate_marginal_risk_report(self, risk_analysis: Dict[str, Any],
                                    concentrations: Dict[str, List[str]]) -> str:
        """
        生成边际风险分析报告

        Args:
            risk_analysis: 风险分析结果
            concentrations: 风险集中度分析结果

        Returns:
            str: 报告内容
        """
        report_lines = []

        # 报告标题
        report_lines.append("# 边际风险贡献分析报告")
        report_lines.append("")
        report_lines.append(f"**分析时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**置信水平**: {self.risk_config.confidence_level * 100:.1f}%")
        report_lines.append("")

        # 组合风险概览
        portfolio_metrics = risk_analysis['portfolio_metrics']
        report_lines.append("## 1. 组合风险概览")
        report_lines.append("")
        report_lines.append(f"- **组合VaR**: {portfolio_metrics['portfolio_var'] * 100:.2f}%")
        report_lines.append(f"- **组合ES**: {portfolio_metrics['portfolio_es'] * 100:.2f}%")
        report_lines.append(f"- **成分VaR总和**: {portfolio_metrics['total_component_var'] * 100:.2f}%")
        report_lines.append(f"- **成分ES总和**: {portfolio_metrics['total_component_es'] * 100:.2f}%")
        report_lines.append("")

        # 边际风险分析
        report_lines.append("## 2. 边际风险分析 (MVaR & MES)")
        report_lines.append("")
        report_lines.append("### 2.1 边际VaR和边际ES含义")
        report_lines.append("")
        report_lines.append("- **边际VaR (MVaR)**: 表示增加该资产1%权重时，组合VaR的变化量")
        report_lines.append("- **边际ES (MES)**: 表示增加该资产1%权重时，组合ES的变化量")
        report_lines.append("- **正值**: 增加该资产权重会增加组合风险")
        report_lines.append("- **负值**: 增加该资产权重会降低组合风险（分散化效应）")
        report_lines.append("")

        # 详细的边际风险数据
        report_lines.append("### 2.2 各资产边际风险指标")
        report_lines.append("")
        report_lines.append("| 资产 | 当前权重 | 边际VaR (%) | 边际ES (%) | 成分VaR (%) | 成分ES (%) | VaR贡献度 | ES贡献度 |")
        report_lines.append("|:-----|:---------|:------------|:-----------|:------------|:-----------|:----------|:---------|")

        for asset, contrib in risk_analysis['asset_contributions'].items():
            report_lines.append(
                f"| {asset} | {contrib['weight']:.2%} | "
                f"{contrib['marginal_var']*100:.2f} | {contrib['marginal_es']*100:.2f} | "
                f"{contrib['component_var']*100:.2f} | {contrib['component_es']*100:.2f} | "
                f"{contrib['var_contribution_pct']:.1f}% | {contrib['es_contribution_pct']:.1f}% |"
            )

        report_lines.append("")

        # 风险贡献度排名
        report_lines.append("## 3. 风险贡献度排名")
        report_lines.append("")

        # VaR贡献度排名
        report_lines.append("### 3.1 VaR贡献度排名")
        report_lines.append("")
        report_lines.append("| 排名 | 资产 | 成分VaR (%) | 贡献度 |")
        report_lines.append("|:-----|:-----|:------------|:-------|")

        for i, (asset, component_var, contribution_pct) in enumerate(risk_analysis['risk_rankings']['var_ranking'], 1):
            report_lines.append(f"| {i} | {asset} | {component_var*100:.2f} | {contribution_pct:.1f}% |")

        report_lines.append("")

        # ES贡献度排名
        report_lines.append("### 3.2 ES贡献度排名")
        report_lines.append("")
        report_lines.append("| 排名 | 资产 | 成分ES (%) | 贡献度 |")
        report_lines.append("|:-----|:-----|:-----------|:-------|")

        for i, (asset, component_es, contribution_pct) in enumerate(risk_analysis['risk_rankings']['es_ranking'], 1):
            report_lines.append(f"| {i} | {asset} | {component_es*100:.2f} | {contribution_pct:.1f}% |")

        report_lines.append("")

        # 风险集中度分析
        report_lines.append("## 4. 风险集中度分析")
        report_lines.append("")
        report_lines.append(f"**风险贡献度阈值**: {concentrations['threshold_pct']:.1f}%")
        report_lines.append("")

        if concentrations['high_var_contributors']:
            report_lines.append("### 4.1 高VaR贡献资产")
            for asset in concentrations['high_var_contributors']:
                contrib = risk_analysis['asset_contributions'][asset]
                report_lines.append(f"- **{asset}**: VaR贡献度 {contrib['var_contribution_pct']:.1f}%")
            report_lines.append("")

        if concentrations['high_es_contributors']:
            report_lines.append("### 4.2 高ES贡献资产")
            for asset in concentrations['high_es_contributors']:
                contrib = risk_analysis['asset_contributions'][asset]
                report_lines.append(f"- **{asset}**: ES贡献度 {contrib['es_contribution_pct']:.1f}%")
            report_lines.append("")

        # 专业洞察和建议
        report_lines.append("## 5. 专业洞察与投资建议")
        report_lines.append("")

        # 识别最大风险贡献者
        top_var_contributor = risk_analysis['risk_rankings']['var_ranking'][0]
        top_es_contributor = risk_analysis['risk_rankings']['es_ranking'][0]

        report_lines.append("### 5.1 风险特性分析")
        report_lines.append("")
        report_lines.append(f"- **最大VaR贡献者**: {top_var_contributor[0]} (贡献度: {top_var_contributor[2]:.1f}%)")
        report_lines.append(f"- **最大ES贡献者**: {top_es_contributor[0]} (贡献度: {top_es_contributor[2]:.1f}%)")
        report_lines.append("")

        # 分析边际风险的含义
        report_lines.append("### 5.2 边际风险含义解释")
        report_lines.append("")

        positive_mvar_assets = [asset for asset, contrib in risk_analysis['asset_contributions'].items()
                              if contrib['marginal_var'] > 0]
        negative_mvar_assets = [asset for asset, contrib in risk_analysis['asset_contributions'].items()
                              if contrib['marginal_var'] < 0]

        if positive_mvar_assets:
            report_lines.append("**正边际VaR资产** (增加权重会增加组合风险):")
            for asset in positive_mvar_assets:
                mvar = risk_analysis['asset_contributions'][asset]['marginal_var']
                report_lines.append(f"- {asset}: MVaR = {mvar*100:.2f}%")
            report_lines.append("")

        if negative_mvar_assets:
            report_lines.append("**负边际VaR资产** (增加权重会降低组合风险):")
            for asset in negative_mvar_assets:
                mvar = risk_analysis['asset_contributions'][asset]['marginal_var']
                report_lines.append(f"- {asset}: MVaR = {mvar*100:.2f}%")
            report_lines.append("")

        # 投资组合调整建议
        report_lines.append("### 5.3 投资组合调整建议")
        report_lines.append("")

        if concentrations['high_var_contributors']:
            report_lines.append("**降低风险的建议**:")
            report_lines.append("- 考虑减少高VaR贡献资产的权重")
            for asset in concentrations['high_var_contributors'][:2]:  # 只显示前2个
                contrib = risk_analysis['asset_contributions'][asset]
                report_lines.append(f"  - 减少 {asset} 权重 (当前: {contrib['weight']:.2%})")
            report_lines.append("")

        if negative_mvar_assets:
            report_lines.append("**增强分散化的建议**:")
            report_lines.append("- 考虑增加负边际VaR资产的权重以降低组合风险")
            for asset in negative_mvar_assets[:2]:  # 只显示前2个
                contrib = risk_analysis['asset_contributions'][asset]
                report_lines.append(f"  - 增加 {asset} 权重 (当前: {contrib['weight']:.2%})")
            report_lines.append("")

        # 风险预算指导
        report_lines.append("### 5.4 风险预算指导价值")
        report_lines.append("")
        report_lines.append("**MVaR和MES在投资组合调整中的应用**:")
        report_lines.append("- **风险预算分配**: 根据边际风险贡献度分配风险预算")
        report_lines.append("- **权重优化**: 优先调整高边际风险贡献的资产权重")
        report_lines.append("- **风险监控**: 定期监控边际风险变化，及时调整投资策略")
        report_lines.append("- **压力测试**: 基于边际风险进行情景分析和压力测试")
        report_lines.append("")

        report_lines.append("---")
        report_lines.append("*本分析基于历史数据和统计模型，投资决策应结合市场环境和投资目标*")

        return "\n".join(report_lines)
