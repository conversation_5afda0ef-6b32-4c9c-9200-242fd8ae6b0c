"""
多资产组合风险构成分析器
分析不同权重配置下的风险构成和对比分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from config.config_manager import config_manager
from core.portfolio_manager import PortfolioManager
from core.risk_models import RiskModelFactory
from core.marginal_risk import MarginalRiskAnalyzer
from core.weight_config_manager import WeightConfigManager
from utils.logger import get_module_logger

logger = get_module_logger("PortfolioCompositionAnalyzer")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class PortfolioCompositionAnalyzer:
    """多资产组合风险构成分析器"""
    
    def __init__(self):
        """初始化组合风险构成分析器"""
        self.config = config_manager.get_config()
        self.risk_config = config_manager.get_risk_calculation_config()
        self.composition_config = self.config.get('portfolio_composition', {})
        
        # 初始化组件
        self.weight_manager = WeightConfigManager()
        self.risk_model = RiskModelFactory.create_model('parametric')
        
        logger.info("Portfolio composition analyzer initialized")
    
    def analyze_single_portfolio(self, weights: pd.Series, returns_data: pd.DataFrame,
                                scenario_name: str = "Portfolio") -> Dict[str, Any]:
        """
        分析单个投资组合的风险构成
        
        Args:
            weights: 权重配置
            returns_data: 收益率数据
            scenario_name: 方案名称
            
        Returns:
            Dict: 风险构成分析结果
        """
        logger.info(f"分析投资组合风险构成: {scenario_name}")
        
        # 创建投资组合管理器
        portfolio_manager = PortfolioManager(weights, returns_data)
        
        # 计算组合风险
        portfolio_returns = portfolio_manager.calculate_portfolio_returns(returns_data)
        portfolio_var, portfolio_es = self.risk_model.calculate_var_es(portfolio_returns)
        
        # 边际风险分析
        marginal_analyzer = MarginalRiskAnalyzer(portfolio_manager)
        risk_contributions = marginal_analyzer.analyze_risk_contributions(returns_data)
        
        # 权重统计
        weight_stats = self.weight_manager.get_weight_statistics(weights)
        
        # 整合分析结果
        analysis_result = {
            'scenario_name': scenario_name,
            'portfolio_metrics': {
                'portfolio_var': portfolio_var,
                'portfolio_es': portfolio_es,
                'num_assets': len(weights),
                'effective_assets': weight_stats['effective_assets'],
                'concentration_hhi': weight_stats['concentration_hhi']
            },
            'weights': weights.to_dict(),
            'weight_statistics': weight_stats,
            'risk_contributions': risk_contributions,
            'asset_analysis': {}
        }
        
        # 各资产详细分析
        asset_contributions = risk_contributions['asset_contributions']
        for asset, contrib in asset_contributions.items():
            analysis_result['asset_analysis'][asset] = {
                'weight': contrib['weight'],
                'weight_pct': contrib['weight'] * 100,
                'marginal_var': contrib['marginal_var'],
                'marginal_es': contrib['marginal_es'],
                'component_var': contrib['component_var'],
                'component_es': contrib['component_es'],
                'var_contribution_pct': contrib['var_contribution_pct'],
                'es_contribution_pct': contrib['es_contribution_pct']
            }
        
        logger.info(f"完成投资组合 {scenario_name} 的风险构成分析")
        return analysis_result
    
    def analyze_multiple_portfolios(self, scenarios: Dict[str, pd.Series], 
                                  returns_data: pd.DataFrame) -> Dict[str, Any]:
        """
        分析多个投资组合的风险构成并进行对比
        
        Args:
            scenarios: 多个权重配置方案
            returns_data: 收益率数据
            
        Returns:
            Dict: 多组合对比分析结果
        """
        logger.info(f"开始分析 {len(scenarios)} 个投资组合方案")
        
        # 分析各个方案
        portfolio_analyses = {}
        for scenario_name, weights in scenarios.items():
            try:
                analysis = self.analyze_single_portfolio(weights, returns_data, scenario_name)
                portfolio_analyses[scenario_name] = analysis
            except Exception as e:
                logger.error(f"分析方案 {scenario_name} 失败: {e}")
                continue
        
        # 对比分析
        comparison_results = self._compare_portfolios(portfolio_analyses)
        
        # 整合结果
        multi_analysis_results = {
            'individual_analyses': portfolio_analyses,
            'comparison_analysis': comparison_results,
            'summary_statistics': self._calculate_summary_statistics(portfolio_analyses)
        }
        
        logger.info(f"完成 {len(portfolio_analyses)} 个投资组合的对比分析")
        return multi_analysis_results
    
    def _compare_portfolios(self, portfolio_analyses: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        对比多个投资组合分析结果
        
        Args:
            portfolio_analyses: 各投资组合分析结果
            
        Returns:
            Dict: 对比分析结果
        """
        if len(portfolio_analyses) < 2:
            return {}
        
        # 提取关键指标进行对比
        comparison_data = []
        
        for scenario_name, analysis in portfolio_analyses.items():
            metrics = analysis['portfolio_metrics']
            weight_stats = analysis['weight_statistics']
            
            comparison_data.append({
                'scenario': scenario_name,
                'var_pct': metrics['portfolio_var'] * 100,
                'es_pct': metrics['portfolio_es'] * 100,
                'num_assets': metrics['num_assets'],
                'effective_assets': metrics['effective_assets'],
                'concentration_hhi': metrics['concentration_hhi'],
                'max_weight': weight_stats['max_weight'],
                'min_weight': weight_stats['min_weight'],
                'weight_std': weight_stats['std_weight']
            })
        
        comparison_df = pd.DataFrame(comparison_data).set_index('scenario')
        
        # 排名分析
        rankings = {}
        rankings['lowest_var'] = comparison_df['var_pct'].idxmin()
        rankings['lowest_es'] = comparison_df['es_pct'].idxmin()
        rankings['highest_diversification'] = comparison_df['effective_assets'].idxmax()
        rankings['lowest_concentration'] = comparison_df['concentration_hhi'].idxmin()
        
        # 风险效率分析
        risk_efficiency = {}
        for scenario in comparison_df.index:
            var_rank = comparison_df['var_pct'].rank().loc[scenario]
            es_rank = comparison_df['es_pct'].rank().loc[scenario]
            div_rank = comparison_df['effective_assets'].rank(ascending=False).loc[scenario]
            
            # 综合评分（越低越好）
            risk_efficiency[scenario] = (var_rank + es_rank + div_rank) / 3
        
        best_overall = min(risk_efficiency.items(), key=lambda x: x[1])[0]
        
        return {
            'comparison_table': comparison_df,
            'rankings': rankings,
            'risk_efficiency': risk_efficiency,
            'best_overall_scenario': best_overall,
            'risk_range': {
                'var_range': (comparison_df['var_pct'].min(), comparison_df['var_pct'].max()),
                'es_range': (comparison_df['es_pct'].min(), comparison_df['es_pct'].max()),
                'diversification_range': (comparison_df['effective_assets'].min(), 
                                        comparison_df['effective_assets'].max())
            }
        }
    
    def _calculate_summary_statistics(self, portfolio_analyses: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算汇总统计信息
        
        Args:
            portfolio_analyses: 各投资组合分析结果
            
        Returns:
            Dict: 汇总统计信息
        """
        if not portfolio_analyses:
            return {}
        
        # 提取所有VaR和ES值
        var_values = [analysis['portfolio_metrics']['portfolio_var'] * 100 
                     for analysis in portfolio_analyses.values()]
        es_values = [analysis['portfolio_metrics']['portfolio_es'] * 100 
                    for analysis in portfolio_analyses.values()]
        
        # 提取分散化指标
        diversification_values = [analysis['portfolio_metrics']['effective_assets'] 
                                for analysis in portfolio_analyses.values()]
        concentration_values = [analysis['portfolio_metrics']['concentration_hhi'] 
                              for analysis in portfolio_analyses.values()]
        
        summary_stats = {
            'num_scenarios': len(portfolio_analyses),
            'var_statistics': {
                'mean': np.mean(var_values),
                'std': np.std(var_values),
                'min': np.min(var_values),
                'max': np.max(var_values),
                'range': np.max(var_values) - np.min(var_values)
            },
            'es_statistics': {
                'mean': np.mean(es_values),
                'std': np.std(es_values),
                'min': np.min(es_values),
                'max': np.max(es_values),
                'range': np.max(es_values) - np.min(es_values)
            },
            'diversification_statistics': {
                'mean': np.mean(diversification_values),
                'std': np.std(diversification_values),
                'min': np.min(diversification_values),
                'max': np.max(diversification_values)
            },
            'concentration_statistics': {
                'mean': np.mean(concentration_values),
                'std': np.std(concentration_values),
                'min': np.min(concentration_values),
                'max': np.max(concentration_values)
            }
        }
        
        return summary_stats
    
    def find_optimal_portfolio(self, scenarios: Dict[str, pd.Series], returns_data: pd.DataFrame,
                             objective: str = 'min_var') -> Tuple[str, Dict[str, Any]]:
        """
        寻找最优投资组合配置
        
        Args:
            scenarios: 权重配置方案
            returns_data: 收益率数据
            objective: 优化目标 ('min_var', 'min_es', 'max_diversification', 'min_concentration')
            
        Returns:
            Tuple: (最优方案名称, 分析结果)
        """
        logger.info(f"寻找最优投资组合，目标: {objective}")
        
        # 分析所有方案
        multi_analysis = self.analyze_multiple_portfolios(scenarios, returns_data)
        comparison_table = multi_analysis['comparison_analysis']['comparison_table']
        
        # 根据目标选择最优方案
        if objective == 'min_var':
            optimal_scenario = comparison_table['var_pct'].idxmin()
        elif objective == 'min_es':
            optimal_scenario = comparison_table['es_pct'].idxmin()
        elif objective == 'max_diversification':
            optimal_scenario = comparison_table['effective_assets'].idxmax()
        elif objective == 'min_concentration':
            optimal_scenario = comparison_table['concentration_hhi'].idxmin()
        else:
            # 使用综合评分
            optimal_scenario = multi_analysis['comparison_analysis']['best_overall_scenario']
        
        optimal_analysis = multi_analysis['individual_analyses'][optimal_scenario]
        
        logger.info(f"最优投资组合方案: {optimal_scenario}")
        return optimal_scenario, optimal_analysis
    
    def generate_weight_recommendations(self, current_weights: pd.Series, returns_data: pd.DataFrame,
                                      target_var_reduction: float = 0.1) -> Dict[str, Any]:
        """
        生成权重调整建议
        
        Args:
            current_weights: 当前权重配置
            returns_data: 收益率数据
            target_var_reduction: 目标VaR降低比例
            
        Returns:
            Dict: 权重调整建议
        """
        logger.info("生成权重调整建议")
        
        # 分析当前组合
        current_analysis = self.analyze_single_portfolio(current_weights, returns_data, "Current")
        current_var = current_analysis['portfolio_metrics']['portfolio_var']
        target_var = current_var * (1 - target_var_reduction)
        
        # 基于边际风险生成调整建议
        asset_contributions = current_analysis['risk_contributions']['asset_contributions']
        
        recommendations = {
            'current_var': current_var * 100,
            'target_var': target_var * 100,
            'target_reduction_pct': target_var_reduction * 100,
            'weight_adjustments': {},
            'expected_impact': {}
        }
        
        # 识别高风险贡献资产
        high_risk_assets = []
        low_risk_assets = []
        
        for asset, contrib in asset_contributions.items():
            if contrib['marginal_var'] > 0 and contrib['var_contribution_pct'] > 20:
                high_risk_assets.append((asset, contrib['var_contribution_pct']))
            elif contrib['marginal_var'] < 0:
                low_risk_assets.append((asset, contrib['marginal_var']))
        
        # 排序
        high_risk_assets.sort(key=lambda x: x[1], reverse=True)
        low_risk_assets.sort(key=lambda x: x[1])
        
        # 生成调整建议
        for asset, contribution in high_risk_assets[:3]:  # 前3个高风险资产
            current_weight = current_weights[asset]
            suggested_reduction = min(0.05, current_weight * 0.2)  # 最多减少5%或当前权重的20%
            
            recommendations['weight_adjustments'][asset] = {
                'current_weight': current_weight,
                'suggested_change': -suggested_reduction,
                'suggested_weight': current_weight - suggested_reduction,
                'reason': f'高风险贡献度({contribution:.1f}%)，建议减少权重',
                'priority': 'high'
            }
        
        for asset, marginal_var in low_risk_assets[:2]:  # 前2个负边际风险资产
            current_weight = current_weights[asset]
            suggested_increase = min(0.03, 0.15 - current_weight)
            
            if suggested_increase > 0:
                recommendations['weight_adjustments'][asset] = {
                    'current_weight': current_weight,
                    'suggested_change': suggested_increase,
                    'suggested_weight': current_weight + suggested_increase,
                    'reason': f'负边际VaR({marginal_var*100:.2f}%)，有助于风险分散',
                    'priority': 'medium'
                }
        
        logger.info(f"生成 {len(recommendations['weight_adjustments'])} 项权重调整建议")
        return recommendations

    def visualize_portfolio_composition(self, analysis_result: Dict[str, Any],
                                      save_path: Optional[str] = None) -> None:
        """
        可视化单个投资组合的风险构成

        Args:
            analysis_result: 投资组合分析结果
            save_path: 保存路径
        """
        scenario_name = analysis_result['scenario_name']
        asset_analysis = analysis_result['asset_analysis']

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 准备数据
        assets = list(asset_analysis.keys())
        weights = [asset_analysis[asset]['weight_pct'] for asset in assets]
        var_contributions = [asset_analysis[asset]['var_contribution_pct'] for asset in assets]
        es_contributions = [asset_analysis[asset]['es_contribution_pct'] for asset in assets]
        marginal_vars = [asset_analysis[asset]['marginal_var'] * 100 for asset in assets]

        # 1. 权重分布饼图
        axes[0, 0].pie(weights, labels=assets, autopct='%1.1f%%', startangle=90)
        axes[0, 0].set_title(f'{scenario_name} - 权重分布', fontsize=14, fontweight='bold')

        # 2. VaR贡献度饼图
        positive_var_contributions = [max(0, x) for x in var_contributions]
        if sum(positive_var_contributions) > 0:
            axes[0, 1].pie(positive_var_contributions, labels=assets, autopct='%1.1f%%', startangle=90)
            axes[0, 1].set_title(f'{scenario_name} - VaR贡献度分布', fontsize=14, fontweight='bold')
        else:
            axes[0, 1].text(0.5, 0.5, '无正值VaR贡献', ha='center', va='center',
                          transform=axes[0, 1].transAxes)
            axes[0, 1].set_title(f'{scenario_name} - VaR贡献度分布', fontsize=14, fontweight='bold')

        # 3. 权重 vs VaR贡献度对比
        x = np.arange(len(assets))
        width = 0.35

        bars1 = axes[1, 0].bar(x - width/2, weights, width, label='权重 (%)',
                              color='skyblue', alpha=0.8)
        bars2 = axes[1, 0].bar(x + width/2, var_contributions, width, label='VaR贡献度 (%)',
                              color='lightcoral', alpha=0.8)

        axes[1, 0].set_title(f'{scenario_name} - 权重 vs VaR贡献度', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('资产', fontsize=12)
        axes[1, 0].set_ylabel('百分比 (%)', fontsize=12)
        axes[1, 0].set_xticks(x)
        axes[1, 0].set_xticklabels(assets, rotation=45, ha='right')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 边际VaR条形图
        colors = ['green' if x < 0 else 'red' for x in marginal_vars]
        bars = axes[1, 1].bar(assets, marginal_vars, color=colors, alpha=0.7)

        axes[1, 1].set_title(f'{scenario_name} - 边际VaR', fontsize=14, fontweight='bold')
        axes[1, 1].set_xlabel('资产', fontsize=12)
        axes[1, 1].set_ylabel('边际VaR (%)', fontsize=12)
        axes[1, 1].tick_params(axis='x', rotation=45)
        axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
        axes[1, 1].grid(True, alpha=0.3)

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            if abs(height) > 0.01:
                axes[1, 1].text(bar.get_x() + bar.get_width()/2., height,
                               f'{height:.2f}%', ha='center',
                               va='bottom' if height > 0 else 'top', fontsize=9)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Portfolio composition chart saved to {save_path}")

        plt.show()

    def visualize_portfolio_comparison(self, multi_analysis_results: Dict[str, Any],
                                     save_path: Optional[str] = None) -> None:
        """
        可视化多个投资组合的对比分析

        Args:
            multi_analysis_results: 多组合分析结果
            save_path: 保存路径
        """
        comparison_table = multi_analysis_results['comparison_analysis']['comparison_table']

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        scenarios = comparison_table.index.tolist()

        # 1. 风险指标对比
        var_values = comparison_table['var_pct'].values
        es_values = comparison_table['es_pct'].values

        x = np.arange(len(scenarios))
        width = 0.35

        bars1 = axes[0, 0].bar(x - width/2, var_values, width, label='VaR',
                              color='skyblue', alpha=0.8)
        bars2 = axes[0, 0].bar(x + width/2, es_values, width, label='ES',
                              color='lightcoral', alpha=0.8)

        axes[0, 0].set_title('风险指标对比', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('投资组合方案', fontsize=12)
        axes[0, 0].set_ylabel('风险值 (%)', fontsize=12)
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(scenarios, rotation=45, ha='right')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                axes[0, 0].text(bar.get_x() + bar.get_width()/2., height,
                               f'{height:.2f}%', ha='center', va='bottom', fontsize=8)

        # 2. 分散化指标对比
        effective_assets = comparison_table['effective_assets'].values
        concentration_hhi = comparison_table['concentration_hhi'].values

        bars1 = axes[0, 1].bar(x - width/2, effective_assets, width, label='有效资产数量',
                              color='lightgreen', alpha=0.8)

        # 创建第二个y轴显示集中度
        ax2 = axes[0, 1].twinx()
        bars2 = ax2.bar(x + width/2, concentration_hhi, width, label='集中度指数',
                       color='orange', alpha=0.8)

        axes[0, 1].set_title('分散化指标对比', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('投资组合方案', fontsize=12)
        axes[0, 1].set_ylabel('有效资产数量', fontsize=12)
        ax2.set_ylabel('集中度指数 (HHI)', fontsize=12)
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels(scenarios, rotation=45, ha='right')
        axes[0, 1].grid(True, alpha=0.3)

        # 合并图例
        lines1, labels1 = axes[0, 1].get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        axes[0, 1].legend(lines1 + lines2, labels1 + labels2, loc='upper left')

        # 3. 风险-分散化散点图
        axes[1, 0].scatter(effective_assets, var_values, s=100, alpha=0.7, c='blue')

        for i, scenario in enumerate(scenarios):
            axes[1, 0].annotate(scenario, (effective_assets[i], var_values[i]),
                               xytext=(5, 5), textcoords='offset points', fontsize=9)

        axes[1, 0].set_title('风险 vs 分散化', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('有效资产数量', fontsize=12)
        axes[1, 0].set_ylabel('VaR (%)', fontsize=12)
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 权重分布对比（热力图）
        individual_analyses = multi_analysis_results['individual_analyses']

        # 获取所有资产
        all_assets = set()
        for analysis in individual_analyses.values():
            all_assets.update(analysis['weights'].keys())
        all_assets = sorted(list(all_assets))

        # 创建权重矩阵
        weight_matrix = np.zeros((len(scenarios), len(all_assets)))
        for i, scenario in enumerate(scenarios):
            weights = individual_analyses[scenario]['weights']
            for j, asset in enumerate(all_assets):
                weight_matrix[i, j] = weights.get(asset, 0) * 100

        im = axes[1, 1].imshow(weight_matrix, cmap='YlOrRd', aspect='auto')
        axes[1, 1].set_title('权重分布热力图', fontsize=14, fontweight='bold')
        axes[1, 1].set_xlabel('资产', fontsize=12)
        axes[1, 1].set_ylabel('投资组合方案', fontsize=12)
        axes[1, 1].set_xticks(range(len(all_assets)))
        axes[1, 1].set_xticklabels(all_assets, rotation=45, ha='right')
        axes[1, 1].set_yticks(range(len(scenarios)))
        axes[1, 1].set_yticklabels(scenarios)

        # 添加颜色条
        cbar = plt.colorbar(im, ax=axes[1, 1])
        cbar.set_label('权重 (%)', fontsize=10)

        # 添加数值标签
        for i in range(len(scenarios)):
            for j in range(len(all_assets)):
                if weight_matrix[i, j] > 1:  # 只显示大于1%的权重
                    text = axes[1, 1].text(j, i, f'{weight_matrix[i, j]:.1f}%',
                                         ha="center", va="center", color="black", fontsize=8)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Portfolio comparison chart saved to {save_path}")

        plt.show()

    def generate_composition_report(self, analysis_result: Dict[str, Any]) -> str:
        """
        生成单个投资组合的风险构成分析报告

        Args:
            analysis_result: 投资组合分析结果

        Returns:
            str: 报告内容
        """
        scenario_name = analysis_result['scenario_name']
        portfolio_metrics = analysis_result['portfolio_metrics']
        weight_stats = analysis_result['weight_statistics']
        asset_analysis = analysis_result['asset_analysis']

        report_lines = []

        # 报告标题
        report_lines.append(f"# {scenario_name} 投资组合风险构成分析报告")
        report_lines.append("")
        report_lines.append(f"**分析时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**置信水平**: {self.risk_config.confidence_level * 100:.1f}%")
        report_lines.append("")

        # 组合概览
        report_lines.append("## 1. 投资组合概览")
        report_lines.append("")
        report_lines.append(f"- **组合VaR**: {portfolio_metrics['portfolio_var'] * 100:.2f}%")
        report_lines.append(f"- **组合ES**: {portfolio_metrics['portfolio_es'] * 100:.2f}%")
        report_lines.append(f"- **资产数量**: {portfolio_metrics['num_assets']}")
        report_lines.append(f"- **有效资产数量**: {portfolio_metrics['effective_assets']:.1f}")
        report_lines.append(f"- **集中度指数 (HHI)**: {portfolio_metrics['concentration_hhi']:.3f}")
        report_lines.append("")

        # 权重统计
        report_lines.append("## 2. 权重配置统计")
        report_lines.append("")
        report_lines.append(f"- **最大权重**: {weight_stats['max_weight']:.2%}")
        report_lines.append(f"- **最小权重**: {weight_stats['min_weight']:.2%}")
        report_lines.append(f"- **平均权重**: {weight_stats['mean_weight']:.2%}")
        report_lines.append(f"- **权重标准差**: {weight_stats['std_weight']:.3f}")
        report_lines.append(f"- **正权重资产数**: {weight_stats['num_positive_weights']}")
        report_lines.append("")

        # 风险分散化评估
        diversification_ratio = portfolio_metrics['effective_assets'] / portfolio_metrics['num_assets']
        if diversification_ratio > 0.8:
            div_assessment = "🟢 良好分散"
        elif diversification_ratio > 0.5:
            div_assessment = "🟡 中等分散"
        else:
            div_assessment = "🔴 集中度较高"

        report_lines.append("## 3. 风险分散化评估")
        report_lines.append("")
        report_lines.append(f"- **分散化比率**: {diversification_ratio:.2f}")
        report_lines.append(f"- **分散化评估**: {div_assessment}")
        report_lines.append("")

        # 各资产风险构成分析
        report_lines.append("## 4. 各资产风险构成分析")
        report_lines.append("")
        report_lines.append("| 资产 | 权重 | 边际VaR | 边际ES | 成分VaR | 成分ES | VaR贡献度 | ES贡献度 |")
        report_lines.append("|:-----|:-----|:--------|:-------|:--------|:-------|:----------|:---------|")

        # 按VaR贡献度排序
        sorted_assets = sorted(asset_analysis.items(),
                             key=lambda x: abs(x[1]['var_contribution_pct']), reverse=True)

        for asset, analysis in sorted_assets:
            report_lines.append(
                f"| {asset} | {analysis['weight_pct']:.1f}% | "
                f"{analysis['marginal_var']*100:.2f}% | {analysis['marginal_es']*100:.2f}% | "
                f"{analysis['component_var']*100:.2f}% | {analysis['component_es']*100:.2f}% | "
                f"{analysis['var_contribution_pct']:.1f}% | {analysis['es_contribution_pct']:.1f}% |"
            )

        report_lines.append("")

        # 风险贡献度分析
        report_lines.append("## 5. 风险贡献度分析")
        report_lines.append("")

        # 识别主要风险贡献者
        top_var_contributors = sorted_assets[:3]
        report_lines.append("### 5.1 主要VaR贡献者")
        for i, (asset, analysis) in enumerate(top_var_contributors, 1):
            report_lines.append(f"{i}. **{asset}**: {analysis['var_contribution_pct']:.1f}% "
                               f"(权重: {analysis['weight_pct']:.1f}%)")
        report_lines.append("")

        # 边际风险分析
        positive_marginal_assets = [(asset, analysis) for asset, analysis in asset_analysis.items()
                                  if analysis['marginal_var'] > 0]
        negative_marginal_assets = [(asset, analysis) for asset, analysis in asset_analysis.items()
                                  if analysis['marginal_var'] < 0]

        if positive_marginal_assets:
            report_lines.append("### 5.2 正边际VaR资产 (增加权重会增加风险)")
            for asset, analysis in positive_marginal_assets:
                report_lines.append(f"- **{asset}**: 边际VaR = {analysis['marginal_var']*100:.2f}%")
            report_lines.append("")

        if negative_marginal_assets:
            report_lines.append("### 5.3 负边际VaR资产 (增加权重会降低风险)")
            for asset, analysis in negative_marginal_assets:
                report_lines.append(f"- **{asset}**: 边际VaR = {analysis['marginal_var']*100:.2f}%")
            report_lines.append("")

        # 投资建议
        report_lines.append("## 6. 投资建议")
        report_lines.append("")

        if diversification_ratio < 0.5:
            report_lines.append("### 🔴 风险集中度过高")
            report_lines.append("- 建议增加资产分散化")
            report_lines.append("- 考虑减少主要风险贡献者的权重")
            report_lines.append("- 引入相关性较低的资产")
        elif diversification_ratio < 0.8:
            report_lines.append("### 🟡 分散化程度适中")
            report_lines.append("- 可考虑进一步优化权重配置")
            report_lines.append("- 关注边际风险贡献较高的资产")
        else:
            report_lines.append("### 🟢 分散化程度良好")
            report_lines.append("- 当前权重配置较为合理")
            report_lines.append("- 建议定期监控风险贡献度变化")

        report_lines.append("")

        # 风险管理建议
        report_lines.append("## 7. 风险管理建议")
        report_lines.append("")
        report_lines.append("### 7.1 权重调整建议")

        # 基于边际风险提供建议
        high_risk_assets = [asset for asset, analysis in asset_analysis.items()
                          if analysis['var_contribution_pct'] > 25 and analysis['marginal_var'] > 0]

        if high_risk_assets:
            report_lines.append("**高风险贡献资产调整**:")
            for asset in high_risk_assets:
                current_weight = asset_analysis[asset]['weight_pct']
                report_lines.append(f"- 考虑减少 {asset} 的权重 (当前: {current_weight:.1f}%)")

        if negative_marginal_assets:
            report_lines.append("**分散化改进**:")
            for asset, analysis in negative_marginal_assets[:2]:
                current_weight = analysis['weight_pct']
                report_lines.append(f"- 考虑增加 {asset} 的权重 (当前: {current_weight:.1f}%)")

        report_lines.append("")
        report_lines.append("### 7.2 监控要点")
        report_lines.append("- 定期重新计算边际风险贡献度")
        report_lines.append("- 监控市场环境变化对风险构成的影响")
        report_lines.append("- 关注资产间相关性的变化")
        report_lines.append("- 设置风险贡献度预警阈值")
        report_lines.append("")

        report_lines.append("---")
        report_lines.append("*本报告基于历史数据分析，投资决策应结合市场环境和投资目标*")

        return "\n".join(report_lines)

    def generate_comparison_report(self, multi_analysis_results: Dict[str, Any]) -> str:
        """
        生成多投资组合对比分析报告

        Args:
            multi_analysis_results: 多组合分析结果

        Returns:
            str: 对比报告内容
        """
        comparison_analysis = multi_analysis_results['comparison_analysis']
        summary_stats = multi_analysis_results['summary_statistics']

        report_lines = []

        # 报告标题
        report_lines.append("# 多投资组合风险构成对比分析报告")
        report_lines.append("")
        report_lines.append(f"**分析时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**对比方案数量**: {summary_stats['num_scenarios']}")
        report_lines.append(f"**置信水平**: {self.risk_config.confidence_level * 100:.1f}%")
        report_lines.append("")

        # 对比概览
        comparison_table = comparison_analysis['comparison_table']

        report_lines.append("## 1. 投资组合对比概览")
        report_lines.append("")
        report_lines.append("| 方案 | VaR (%) | ES (%) | 有效资产数 | 集中度指数 | 最大权重 |")
        report_lines.append("|:-----|:--------|:-------|:-----------|:-----------|:---------|")

        for scenario in comparison_table.index:
            row = comparison_table.loc[scenario]
            report_lines.append(
                f"| {scenario} | {row['var_pct']:.2f} | {row['es_pct']:.2f} | "
                f"{row['effective_assets']:.1f} | {row['concentration_hhi']:.3f} | "
                f"{row['max_weight']:.1%} |"
            )

        report_lines.append("")

        # 排名分析
        rankings = comparison_analysis['rankings']
        report_lines.append("## 2. 各项指标排名")
        report_lines.append("")
        report_lines.append(f"- **最低VaR方案**: {rankings['lowest_var']}")
        report_lines.append(f"- **最低ES方案**: {rankings['lowest_es']}")
        report_lines.append(f"- **最高分散化方案**: {rankings['highest_diversification']}")
        report_lines.append(f"- **最低集中度方案**: {rankings['lowest_concentration']}")
        report_lines.append(f"- **综合最优方案**: {comparison_analysis['best_overall_scenario']}")
        report_lines.append("")

        # 风险统计分析
        var_stats = summary_stats['var_statistics']
        es_stats = summary_stats['es_statistics']

        report_lines.append("## 3. 风险统计分析")
        report_lines.append("")
        report_lines.append("### 3.1 VaR统计")
        report_lines.append(f"- **平均值**: {var_stats['mean']:.2f}%")
        report_lines.append(f"- **标准差**: {var_stats['std']:.2f}%")
        report_lines.append(f"- **最小值**: {var_stats['min']:.2f}%")
        report_lines.append(f"- **最大值**: {var_stats['max']:.2f}%")
        report_lines.append(f"- **变化范围**: {var_stats['range']:.2f}%")
        report_lines.append("")

        report_lines.append("### 3.2 ES统计")
        report_lines.append(f"- **平均值**: {es_stats['mean']:.2f}%")
        report_lines.append(f"- **标准差**: {es_stats['std']:.2f}%")
        report_lines.append(f"- **最小值**: {es_stats['min']:.2f}%")
        report_lines.append(f"- **最大值**: {es_stats['max']:.2f}%")
        report_lines.append(f"- **变化范围**: {es_stats['range']:.2f}%")
        report_lines.append("")

        # 分散化分析
        div_stats = summary_stats['diversification_statistics']
        report_lines.append("### 3.3 分散化分析")
        report_lines.append(f"- **平均有效资产数**: {div_stats['mean']:.1f}")
        report_lines.append(f"- **分散化范围**: {div_stats['min']:.1f} - {div_stats['max']:.1f}")
        report_lines.append("")

        # 风险效率分析
        risk_efficiency = comparison_analysis['risk_efficiency']
        sorted_efficiency = sorted(risk_efficiency.items(), key=lambda x: x[1])

        report_lines.append("## 4. 风险效率排名")
        report_lines.append("")
        report_lines.append("| 排名 | 方案 | 综合评分 | 评级 |")
        report_lines.append("|:-----|:-----|:---------|:-----|")

        for i, (scenario, score) in enumerate(sorted_efficiency, 1):
            if i <= 2:
                rating = "🥇 优秀"
            elif i <= len(sorted_efficiency) // 2:
                rating = "🥈 良好"
            else:
                rating = "🥉 一般"

            report_lines.append(f"| {i} | {scenario} | {score:.2f} | {rating} |")

        report_lines.append("")

        # 投资建议
        best_scenario = comparison_analysis['best_overall_scenario']
        report_lines.append("## 5. 投资建议")
        report_lines.append("")
        report_lines.append(f"### 5.1 推荐方案: {best_scenario}")

        best_row = comparison_table.loc[best_scenario]
        report_lines.append(f"- **VaR**: {best_row['var_pct']:.2f}%")
        report_lines.append(f"- **ES**: {best_row['es_pct']:.2f}%")
        report_lines.append(f"- **有效资产数**: {best_row['effective_assets']:.1f}")
        report_lines.append(f"- **集中度指数**: {best_row['concentration_hhi']:.3f}")
        report_lines.append("")

        report_lines.append("### 5.2 方案选择指导")
        report_lines.append("- **风险厌恶型投资者**: 选择最低VaR/ES方案")
        report_lines.append("- **分散化偏好投资者**: 选择最高分散化方案")
        report_lines.append("- **平衡型投资者**: 选择综合最优方案")
        report_lines.append("")

        report_lines.append("### 5.3 风险管理建议")
        report_lines.append("- 根据投资目标和风险承受能力选择合适方案")
        report_lines.append("- 定期重新评估和调整投资组合配置")
        report_lines.append("- 关注市场环境变化对各方案表现的影响")
        report_lines.append("- 建立动态的风险监控和预警机制")
        report_lines.append("")

        report_lines.append("---")
        report_lines.append("*本对比分析基于历史数据，实际投资决策应结合当前市场环境*")

        return "\n".join(report_lines)
