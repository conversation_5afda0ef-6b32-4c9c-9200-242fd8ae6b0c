"""
风险预算优化建议模块
基于边际风险分析提供投资组合优化建议
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

from config.config_manager import config_manager
from core.portfolio_manager import PortfolioManager
from core.risk_models import RiskModelFactory
from core.marginal_risk import MarginalRiskAnalyzer
from utils.logger import get_module_logger

logger = get_module_logger("RiskBudgetOptimizer")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class RiskBudgetOptimizer:
    """风险预算优化器"""
    
    def __init__(self, portfolio_manager: PortfolioManager):
        """
        初始化风险预算优化器
        
        Args:
            portfolio_manager: 投资组合管理器
        """
        self.portfolio_manager = portfolio_manager
        self.config = config_manager.get_config()
        self.budget_config = self.config.get('risk_budget', {})
        self.risk_config = config_manager.get_risk_calculation_config()
        
        # 风险模型和分析器
        self.risk_model = RiskModelFactory.create_model('parametric')
        self.marginal_analyzer = MarginalRiskAnalyzer(portfolio_manager)
        
        logger.info("Risk budget optimizer initialized")
    
    def analyze_current_risk_budget(self, returns_data: pd.DataFrame) -> Dict[str, Any]:
        """
        分析当前风险预算状况
        
        Args:
            returns_data: 收益率数据
            
        Returns:
            Dict: 当前风险预算分析结果
        """
        logger.info("Analyzing current risk budget")
        
        # 获取边际风险分析结果
        risk_analysis = self.marginal_analyzer.analyze_risk_contributions(returns_data)
        
        # 计算风险预算指标
        portfolio_metrics = risk_analysis['portfolio_metrics']
        asset_contributions = risk_analysis['asset_contributions']
        
        # 风险集中度分析
        var_contributions = [contrib['var_contribution_pct'] for contrib in asset_contributions.values()]
        es_contributions = [contrib['es_contribution_pct'] for contrib in asset_contributions.values()]
        
        # 计算风险集中度指标 (Herfindahl指数)
        var_hhi = sum([(x/100)**2 for x in var_contributions])
        es_hhi = sum([(x/100)**2 for x in es_contributions])
        
        # 有效资产数量
        effective_assets_var = 1 / var_hhi if var_hhi > 0 else len(asset_contributions)
        effective_assets_es = 1 / es_hhi if es_hhi > 0 else len(asset_contributions)
        
        risk_budget_analysis = {
            'portfolio_risk': {
                'var': portfolio_metrics['portfolio_var'],
                'es': portfolio_metrics['portfolio_es']
            },
            'concentration_metrics': {
                'var_hhi': var_hhi,
                'es_hhi': es_hhi,
                'effective_assets_var': effective_assets_var,
                'effective_assets_es': effective_assets_es,
                'total_assets': len(asset_contributions)
            },
            'risk_contributions': asset_contributions,
            'diversification_ratio': {
                'var': effective_assets_var / len(asset_contributions),
                'es': effective_assets_es / len(asset_contributions)
            }
        }
        
        logger.info("Current risk budget analysis completed")
        return risk_budget_analysis
    
    def generate_optimization_recommendations(self, returns_data: pd.DataFrame, 
                                            risk_budget_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成优化建议
        
        Args:
            returns_data: 收益率数据
            risk_budget_analysis: 风险预算分析结果
            
        Returns:
            Dict: 优化建议
        """
        logger.info("Generating optimization recommendations")
        
        current_weights = self.portfolio_manager.weights
        asset_contributions = risk_budget_analysis['risk_contributions']
        
        recommendations = {
            'weight_adjustments': {},
            'risk_reduction_strategies': [],
            'diversification_improvements': [],
            'specific_actions': []
        }
        
        # 1. 识别需要调整的资产
        high_risk_threshold = 100 / len(current_weights) * 1.5  # 平均贡献度的1.5倍
        
        for asset, contrib in asset_contributions.items():
            current_weight = contrib['weight']
            var_contribution = contrib['var_contribution_pct']
            marginal_var = contrib['marginal_var']
            
            # 权重调整建议
            if var_contribution > high_risk_threshold and marginal_var > 0:
                # 高风险贡献且边际风险为正 -> 建议减少权重
                suggested_reduction = min(0.05, current_weight * 0.2)  # 最多减少5%或当前权重的20%
                new_weight = max(0.01, current_weight - suggested_reduction)
                
                recommendations['weight_adjustments'][asset] = {
                    'current_weight': current_weight,
                    'suggested_weight': new_weight,
                    'change': new_weight - current_weight,
                    'reason': f'高风险贡献度({var_contribution:.1f}%)且正边际VaR',
                    'priority': 'high'
                }
                
            elif marginal_var < 0 and current_weight < 0.15:
                # 负边际风险且权重较低 -> 建议增加权重
                suggested_increase = min(0.03, 0.15 - current_weight)
                new_weight = current_weight + suggested_increase
                
                recommendations['weight_adjustments'][asset] = {
                    'current_weight': current_weight,
                    'suggested_weight': new_weight,
                    'change': new_weight - current_weight,
                    'reason': f'负边际VaR({marginal_var*100:.2f}%)有助于风险分散',
                    'priority': 'medium'
                }
        
        # 2. 风险降低策略
        current_var = risk_budget_analysis['portfolio_risk']['var']
        target_var_reduction = self.budget_config.get('target_var_reduction', 0.1)
        
        recommendations['risk_reduction_strategies'] = [
            {
                'strategy': '权重再平衡',
                'description': '调整高风险贡献资产权重',
                'expected_var_reduction': f'{target_var_reduction*100:.1f}%',
                'implementation': '逐步调整权重，避免市场冲击'
            },
            {
                'strategy': '增加分散化资产',
                'description': '增加负边际VaR资产权重',
                'expected_benefit': '降低组合整体风险',
                'implementation': '重点关注相关性低的资产'
            }
        ]
        
        # 3. 分散化改进建议
        diversification_ratio = risk_budget_analysis['diversification_ratio']['var']
        
        if diversification_ratio < 0.7:
            recommendations['diversification_improvements'] = [
                '当前投资组合风险过于集中',
                '建议增加有效资产数量',
                '考虑引入新的资产类别',
                '优化现有资产权重分配'
            ]
        elif diversification_ratio > 0.9:
            recommendations['diversification_improvements'] = [
                '投资组合分散化程度较好',
                '可考虑适度集中于优质资产',
                '关注风险调整后收益'
            ]
        else:
            recommendations['diversification_improvements'] = [
                '投资组合分散化程度适中',
                '继续监控风险集中度变化',
                '根据市场环境微调权重'
            ]
        
        # 4. 具体行动建议
        recommendations['specific_actions'] = self._generate_specific_actions(
            recommendations['weight_adjustments'], 
            risk_budget_analysis
        )
        
        logger.info("Optimization recommendations generated")
        return recommendations
    
    def simulate_optimization_impact(self, returns_data: pd.DataFrame, 
                                   weight_adjustments: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """
        模拟优化建议的影响
        
        Args:
            returns_data: 收益率数据
            weight_adjustments: 权重调整建议
            
        Returns:
            Dict: 优化影响分析
        """
        logger.info("Simulating optimization impact")
        
        # 当前组合风险
        current_portfolio_returns = self.portfolio_manager.calculate_portfolio_returns(returns_data)
        current_var, current_es = self.risk_model.calculate_var_es(current_portfolio_returns)
        
        # 构建优化后的权重
        optimized_weights = self.portfolio_manager.weights.copy()
        
        for asset, adjustment in weight_adjustments.items():
            if adjustment['priority'] == 'high':  # 只应用高优先级调整
                optimized_weights[asset] = adjustment['suggested_weight']
        
        # 重新标准化权重
        optimized_weights = optimized_weights / optimized_weights.sum()
        
        # 计算优化后的风险
        optimized_portfolio = PortfolioManager(optimized_weights, returns_data)
        optimized_portfolio_returns = optimized_portfolio.calculate_portfolio_returns(returns_data)
        optimized_var, optimized_es = self.risk_model.calculate_var_es(optimized_portfolio_returns)
        
        # 计算改进效果
        var_improvement = (current_var - optimized_var) / current_var * 100
        es_improvement = (current_es - optimized_es) / current_es * 100
        
        simulation_results = {
            'current_risk': {
                'var': current_var,
                'es': current_es
            },
            'optimized_risk': {
                'var': optimized_var,
                'es': optimized_es
            },
            'improvements': {
                'var_reduction_pct': var_improvement,
                'es_reduction_pct': es_improvement,
                'var_absolute_change': current_var - optimized_var,
                'es_absolute_change': current_es - optimized_es
            },
            'weight_changes': {
                'current_weights': self.portfolio_manager.weights.to_dict(),
                'optimized_weights': optimized_weights.to_dict(),
                'weight_differences': (optimized_weights - self.portfolio_manager.weights).to_dict()
            }
        }
        
        logger.info(f"Optimization simulation completed: VaR reduction {var_improvement:.2f}%, ES reduction {es_improvement:.2f}%")
        return simulation_results
    
    def _generate_specific_actions(self, weight_adjustments: Dict[str, Dict[str, float]], 
                                 risk_budget_analysis: Dict[str, Any]) -> List[Dict[str, str]]:
        """生成具体的行动建议"""
        actions = []
        
        # 高优先级调整
        high_priority_adjustments = {k: v for k, v in weight_adjustments.items() 
                                   if v['priority'] == 'high'}
        
        if high_priority_adjustments:
            actions.append({
                'action': '立即调整高风险资产权重',
                'details': f'优先调整 {", ".join(high_priority_adjustments.keys())} 的权重',
                'timeline': '1-2周内完成',
                'expected_impact': '显著降低组合风险'
            })
        
        # 分散化改进
        diversification_ratio = risk_budget_analysis['diversification_ratio']['var']
        if diversification_ratio < 0.7:
            actions.append({
                'action': '增强投资组合分散化',
                'details': '考虑引入新的资产类别或增加现有低权重资产',
                'timeline': '1-3个月内实施',
                'expected_impact': '提高风险分散效果'
            })
        
        # 风险监控
        actions.append({
            'action': '建立动态风险监控机制',
            'details': '定期计算边际VaR和ES，监控风险贡献度变化',
            'timeline': '持续进行',
            'expected_impact': '及时发现和应对风险集中'
        })
        
        return actions

    def visualize_optimization_comparison(self, simulation_results: Dict[str, Any],
                                        save_path: Optional[str] = None) -> None:
        """
        可视化优化前后对比

        Args:
            simulation_results: 优化模拟结果
            save_path: 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 风险指标对比
        current_risk = simulation_results['current_risk']
        optimized_risk = simulation_results['optimized_risk']

        risk_metrics = ['VaR', 'ES']
        current_values = [current_risk['var'] * 100, current_risk['es'] * 100]
        optimized_values = [optimized_risk['var'] * 100, optimized_risk['es'] * 100]

        x = np.arange(len(risk_metrics))
        width = 0.35

        bars1 = axes[0, 0].bar(x - width/2, current_values, width, label='优化前', color='lightcoral', alpha=0.8)
        bars2 = axes[0, 0].bar(x + width/2, optimized_values, width, label='优化后', color='lightgreen', alpha=0.8)

        axes[0, 0].set_title('风险指标对比', fontsize=14, fontweight='bold')
        axes[0, 0].set_ylabel('风险值 (%)', fontsize=12)
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(risk_metrics)
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                axes[0, 0].text(bar.get_x() + bar.get_width()/2., height,
                              f'{height:.2f}%', ha='center', va='bottom', fontsize=9)

        # 2. 权重变化图
        weight_changes = simulation_results['weight_changes']
        assets = list(weight_changes['current_weights'].keys())
        current_weights = [weight_changes['current_weights'][asset] * 100 for asset in assets]
        optimized_weights = [weight_changes['optimized_weights'][asset] * 100 for asset in assets]

        x = np.arange(len(assets))
        bars1 = axes[0, 1].bar(x - width/2, current_weights, width, label='当前权重', color='skyblue', alpha=0.8)
        bars2 = axes[0, 1].bar(x + width/2, optimized_weights, width, label='优化权重', color='orange', alpha=0.8)

        axes[0, 1].set_title('权重配置对比', fontsize=14, fontweight='bold')
        axes[0, 1].set_ylabel('权重 (%)', fontsize=12)
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels(assets, rotation=45, ha='right')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 改进效果图
        improvements = simulation_results['improvements']
        improvement_metrics = ['VaR降低', 'ES降低']
        improvement_values = [improvements['var_reduction_pct'], improvements['es_reduction_pct']]

        colors = ['green' if x > 0 else 'red' for x in improvement_values]
        bars = axes[1, 0].bar(improvement_metrics, improvement_values, color=colors, alpha=0.7)

        axes[1, 0].set_title('风险改进效果', fontsize=14, fontweight='bold')
        axes[1, 0].set_ylabel('改进幅度 (%)', fontsize=12)
        axes[1, 0].axhline(y=0, color='black', linestyle='-', alpha=0.3)
        axes[1, 0].grid(True, alpha=0.3)

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            axes[1, 0].text(bar.get_x() + bar.get_width()/2., height,
                          f'{height:.1f}%', ha='center',
                          va='bottom' if height > 0 else 'top', fontsize=10)

        # 4. 权重变化幅度
        weight_differences = [weight_changes['weight_differences'][asset] * 100 for asset in assets]
        colors = ['green' if x > 0 else 'red' if x < 0 else 'gray' for x in weight_differences]

        bars = axes[1, 1].bar(assets, weight_differences, color=colors, alpha=0.7)
        axes[1, 1].set_title('权重调整幅度', fontsize=14, fontweight='bold')
        axes[1, 1].set_ylabel('权重变化 (%)', fontsize=12)
        axes[1, 1].set_xticklabels(assets, rotation=45, ha='right')
        axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
        axes[1, 1].grid(True, alpha=0.3)

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            if abs(height) > 0.1:  # 只显示变化较大的标签
                axes[1, 1].text(bar.get_x() + bar.get_width()/2., height,
                              f'{height:.1f}%', ha='center',
                              va='bottom' if height > 0 else 'top', fontsize=8)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Optimization comparison chart saved to {save_path}")

        plt.show()

    def generate_optimization_report(self, risk_budget_analysis: Dict[str, Any],
                                   recommendations: Dict[str, Any],
                                   simulation_results: Dict[str, Any]) -> str:
        """
        生成风险预算优化报告

        Args:
            risk_budget_analysis: 风险预算分析结果
            recommendations: 优化建议
            simulation_results: 优化模拟结果

        Returns:
            str: 报告内容
        """
        report_lines = []

        # 报告标题
        report_lines.append("# 风险预算优化建议报告")
        report_lines.append("")
        report_lines.append(f"**分析时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**置信水平**: {self.risk_config.confidence_level * 100:.1f}%")
        report_lines.append("")

        # 当前风险预算状况
        report_lines.append("## 1. 当前风险预算状况")
        report_lines.append("")

        portfolio_risk = risk_budget_analysis['portfolio_risk']
        concentration_metrics = risk_budget_analysis['concentration_metrics']

        report_lines.append(f"- **组合VaR**: {portfolio_risk['var'] * 100:.2f}%")
        report_lines.append(f"- **组合ES**: {portfolio_risk['es'] * 100:.2f}%")
        report_lines.append(f"- **VaR集中度指数 (HHI)**: {concentration_metrics['var_hhi']:.3f}")
        report_lines.append(f"- **ES集中度指数 (HHI)**: {concentration_metrics['es_hhi']:.3f}")
        report_lines.append(f"- **有效资产数量 (VaR)**: {concentration_metrics['effective_assets_var']:.1f}")
        report_lines.append(f"- **有效资产数量 (ES)**: {concentration_metrics['effective_assets_es']:.1f}")
        report_lines.append(f"- **分散化比率 (VaR)**: {risk_budget_analysis['diversification_ratio']['var']:.2f}")
        report_lines.append("")

        # 风险集中度评估
        diversification_ratio = risk_budget_analysis['diversification_ratio']['var']
        if diversification_ratio < 0.5:
            risk_level = "高度集中"
            risk_color = "🔴"
        elif diversification_ratio < 0.7:
            risk_level = "中度集中"
            risk_color = "🟡"
        else:
            risk_level = "良好分散"
            risk_color = "🟢"

        report_lines.append(f"**风险集中度评估**: {risk_color} {risk_level}")
        report_lines.append("")

        # 权重调整建议
        if recommendations['weight_adjustments']:
            report_lines.append("## 2. 权重调整建议")
            report_lines.append("")

            # 高优先级调整
            high_priority = {k: v for k, v in recommendations['weight_adjustments'].items()
                           if v['priority'] == 'high'}
            if high_priority:
                report_lines.append("### 2.1 高优先级调整 (建议立即执行)")
                report_lines.append("")
                report_lines.append("| 资产 | 当前权重 | 建议权重 | 权重变化 | 调整原因 |")
                report_lines.append("|:-----|:---------|:---------|:---------|:---------|")

                for asset, adj in high_priority.items():
                    report_lines.append(
                        f"| {asset} | {adj['current_weight']:.2%} | {adj['suggested_weight']:.2%} | "
                        f"{adj['change']:+.2%} | {adj['reason']} |"
                    )
                report_lines.append("")

            # 中优先级调整
            medium_priority = {k: v for k, v in recommendations['weight_adjustments'].items()
                             if v['priority'] == 'medium'}
            if medium_priority:
                report_lines.append("### 2.2 中优先级调整 (建议逐步实施)")
                report_lines.append("")
                report_lines.append("| 资产 | 当前权重 | 建议权重 | 权重变化 | 调整原因 |")
                report_lines.append("|:-----|:---------|:---------|:---------|:---------|")

                for asset, adj in medium_priority.items():
                    report_lines.append(
                        f"| {asset} | {adj['current_weight']:.2%} | {adj['suggested_weight']:.2%} | "
                        f"{adj['change']:+.2%} | {adj['reason']} |"
                    )
                report_lines.append("")

        # 风险降低策略
        if recommendations['risk_reduction_strategies']:
            report_lines.append("## 3. 风险降低策略")
            report_lines.append("")

            for i, strategy in enumerate(recommendations['risk_reduction_strategies'], 1):
                report_lines.append(f"### 3.{i} {strategy['strategy']}")
                report_lines.append(f"- **描述**: {strategy['description']}")
                if 'expected_var_reduction' in strategy:
                    report_lines.append(f"- **预期VaR降低**: {strategy['expected_var_reduction']}")
                if 'expected_benefit' in strategy:
                    report_lines.append(f"- **预期效果**: {strategy['expected_benefit']}")
                report_lines.append(f"- **实施方式**: {strategy['implementation']}")
                report_lines.append("")

        # 优化效果预测
        if simulation_results:
            report_lines.append("## 4. 优化效果预测")
            report_lines.append("")

            improvements = simulation_results['improvements']
            report_lines.append("### 4.1 风险改进预期")
            report_lines.append("")
            report_lines.append("| 风险指标 | 优化前 | 优化后 | 绝对改进 | 相对改进 |")
            report_lines.append("|:---------|:-------|:-------|:---------|:---------|")

            current_risk = simulation_results['current_risk']
            optimized_risk = simulation_results['optimized_risk']

            report_lines.append(
                f"| VaR | {current_risk['var']*100:.2f}% | {optimized_risk['var']*100:.2f}% | "
                f"{improvements['var_absolute_change']*100:.2f}% | {improvements['var_reduction_pct']:.1f}% |"
            )
            report_lines.append(
                f"| ES | {current_risk['es']*100:.2f}% | {optimized_risk['es']*100:.2f}% | "
                f"{improvements['es_absolute_change']*100:.2f}% | {improvements['es_reduction_pct']:.1f}% |"
            )
            report_lines.append("")

        # 具体行动计划
        if recommendations['specific_actions']:
            report_lines.append("## 5. 具体行动计划")
            report_lines.append("")

            for i, action in enumerate(recommendations['specific_actions'], 1):
                report_lines.append(f"### 5.{i} {action['action']}")
                report_lines.append(f"- **具体内容**: {action['details']}")
                report_lines.append(f"- **实施时间**: {action['timeline']}")
                report_lines.append(f"- **预期影响**: {action['expected_impact']}")
                report_lines.append("")

        # 风险与收益权衡分析
        report_lines.append("## 6. 风险与收益权衡分析")
        report_lines.append("")
        report_lines.append("### 6.1 优化原则")
        report_lines.append("- **风险预算目标**: 在给定VaR限制下最大化预期收益")
        report_lines.append("- **分散化原则**: 避免风险过度集中于少数资产")
        report_lines.append("- **边际效应**: 优先调整边际风险贡献度高的资产")
        report_lines.append("- **实施可行性**: 考虑市场流动性和交易成本")
        report_lines.append("")

        report_lines.append("### 6.2 风险控制建议")
        report_lines.append("- **动态监控**: 建立边际VaR/ES的定期监控机制")
        report_lines.append("- **阈值管理**: 设置风险贡献度预警阈值")
        report_lines.append("- **情景分析**: 定期进行压力测试和情景分析")
        report_lines.append("- **再平衡频率**: 建议每月评估，每季度调整")
        report_lines.append("")

        # 实施注意事项
        report_lines.append("## 7. 实施注意事项")
        report_lines.append("")
        report_lines.append("### 7.1 市场环境考虑")
        report_lines.append("- 当前市场波动性水平")
        report_lines.append("- 资产间相关性变化趋势")
        report_lines.append("- 流动性状况和交易成本")
        report_lines.append("")

        report_lines.append("### 7.2 风险管理要点")
        report_lines.append("- 避免过度频繁调整")
        report_lines.append("- 保持适度的风险缓冲")
        report_lines.append("- 关注模型假设的有效性")
        report_lines.append("- 结合定性分析和专业判断")
        report_lines.append("")

        report_lines.append("---")
        report_lines.append("*本报告基于历史数据和统计模型分析，实际投资决策应结合市场环境、投资目标和风险承受能力综合考虑*")

        return "\n".join(report_lines)
