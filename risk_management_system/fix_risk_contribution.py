#!/usr/bin/env python3
"""
修复风险贡献度计算问题
解决成分VaR总和不等于组合VaR的问题
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from scipy import stats

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.risk_models import ParametricRiskModel
from core.portfolio_manager import PortfolioManager
from config.config_manager import config_manager


def diagnose_risk_contribution_issue():
    """诊断风险贡献度计算问题"""
    print("🔍 诊断风险贡献度计算问题")
    print("="*50)
    
    # 生成测试数据
    np.random.seed(42)
    n_obs = 1000
    n_assets = 4
    
    mean_returns = np.array([0.0005, 0.0003, 0.0002, 0.0001])
    volatilities = np.array([0.02, 0.015, 0.01, 0.008])
    correlation_matrix = np.array([
        [1.0, 0.3, 0.1, 0.05],
        [0.3, 1.0, 0.2, 0.1],
        [0.1, 0.2, 1.0, 0.15],
        [0.05, 0.1, 0.15, 1.0]
    ])
    
    cov_matrix = np.outer(volatilities, volatilities) * correlation_matrix
    returns = np.random.multivariate_normal(mean_returns, cov_matrix, n_obs)
    
    asset_names = [f"股票{chr(65+i)}" for i in range(n_assets)]
    dates = pd.date_range('2020-01-01', periods=n_obs, freq='D')
    returns_data = pd.DataFrame(returns, index=dates, columns=asset_names)
    
    # 创建投资组合
    weights = pd.Series([0.3, 0.25, 0.25, 0.2], index=asset_names)
    portfolio_manager = PortfolioManager(weights, returns_data)
    
    # 计算组合VaR
    portfolio_returns = portfolio_manager.calculate_portfolio_returns(returns_data)
    parametric_model = ParametricRiskModel()
    portfolio_var = parametric_model.calculate_var(portfolio_returns, 0.95)
    
    print(f"组合VaR: {portfolio_var:.6f}")
    
    # 手动计算正确的边际VaR
    print("\n🧮 手动计算正确的边际VaR")
    
    # 获取协方差矩阵
    cov_matrix_df = portfolio_manager.get_covariance_matrix(returns_data)
    
    # 计算组合方差和波动率
    portfolio_variance = np.dot(weights.values, np.dot(cov_matrix_df.values, weights.values))
    portfolio_volatility = np.sqrt(portfolio_variance)
    
    print(f"组合方差: {portfolio_variance:.8f}")
    print(f"组合波动率: {portfolio_volatility:.6f}")
    
    # 计算VaR分位数
    alpha = 1 - 0.95
    quantile = stats.norm.ppf(alpha)
    print(f"VaR分位数 (α={alpha}): {quantile:.6f}")
    
    # 计算理论组合VaR (不考虑GARCH)
    holding_period = config_manager.get_risk_calculation_config().holding_period
    theoretical_var = -quantile * portfolio_volatility * np.sqrt(holding_period)
    print(f"理论组合VaR: {theoretical_var:.6f}")
    
    # 计算正确的边际VaR
    print(f"\n📊 各资产边际VaR计算:")
    marginal_vars = {}
    component_vars = {}
    
    for i, asset in enumerate(asset_names):
        # 边际VaR = -quantile * (协方差矩阵第i行与权重向量的点积) / 组合波动率
        asset_portfolio_cov = np.dot(cov_matrix_df.iloc[i, :].values, weights.values)
        marginal_var = -quantile * asset_portfolio_cov / portfolio_volatility * np.sqrt(holding_period)
        component_var = weights[asset] * marginal_var
        
        marginal_vars[asset] = marginal_var
        component_vars[asset] = component_var
        
        print(f"  {asset}: 权重={weights[asset]:.3f}, MVaR={marginal_var:.6f}, 成分VaR={component_var:.6f}")
    
    # 验证成分VaR总和
    sum_component_var = sum(component_vars.values())
    print(f"\n✅ 成分VaR总和: {sum_component_var:.6f}")
    print(f"📊 理论组合VaR: {theoretical_var:.6f}")
    print(f"🔍 差异: {abs(sum_component_var - theoretical_var):.8f}")
    
    # 验证欧拉定理
    print(f"\n🧮 欧拉定理验证 (组合VaR的齐次性):")
    print(f"Σ(w_i * ∂VaR/∂w_i) = VaR")
    print(f"左边: {sum_component_var:.6f}")
    print(f"右边: {theoretical_var:.6f}")
    print(f"是否相等: {abs(sum_component_var - theoretical_var) < 1e-10}")
    
    return marginal_vars, component_vars, theoretical_var


def create_fixed_risk_contribution_calculator():
    """创建修复后的风险贡献度计算器"""
    
    fixed_code = '''
    def _calculate_analytical_mvar_fixed(self, returns_data: pd.DataFrame, 
                                       confidence_level: float) -> Dict[str, float]:
        """
        使用修复后的解析解计算边际VaR
        确保成分VaR总和等于组合VaR
        
        Args:
            returns_data: 收益率数据
            confidence_level: 置信水平
            
        Returns:
            Dict: 各资产的边际VaR
        """
        # 获取协方差矩阵
        covariance_matrix = self.portfolio_manager.get_covariance_matrix(returns_data)
        
        # 确保权重和协方差矩阵的资产匹配
        common_assets = covariance_matrix.index.intersection(self.portfolio_manager.weights.index)
        aligned_cov = covariance_matrix.loc[common_assets, common_assets]
        aligned_weights = self.portfolio_manager.weights[common_assets]
        
        # 计算组合方差和波动率
        portfolio_variance = np.dot(aligned_weights.values, 
                                  np.dot(aligned_cov.values, aligned_weights.values))
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        # 计算VaR的分位数 (使用标准正态分布)
        alpha = 1 - confidence_level
        quantile = stats.norm.ppf(alpha)
        
        # 持有期调整
        holding_period_factor = np.sqrt(self.risk_config.holding_period)
        
        # 计算边际VaR: MVaR_i = -quantile * (Σ_j w_j * Cov(i,j)) / σ_p * √T
        marginal_vars = {}
        
        for i, asset in enumerate(common_assets):
            # 计算资产i与组合的协方差 (权重加权)
            asset_portfolio_cov = np.dot(aligned_cov.iloc[i, :].values, aligned_weights.values)
            
            # 边际VaR
            if portfolio_volatility > 0:
                marginal_var = -quantile * asset_portfolio_cov / portfolio_volatility * holding_period_factor
            else:
                marginal_var = 0.0
            
            marginal_vars[asset] = marginal_var
        
        # 为不在协方差矩阵中的资产设置0
        for asset in self.portfolio_manager.weights.index:
            if asset not in marginal_vars:
                marginal_vars[asset] = 0.0
        
        logger.info("Calculated fixed analytical marginal VaR")
        return marginal_vars
    '''
    
    print("🔧 修复后的边际VaR计算方法:")
    print(fixed_code)


def test_fixed_calculation():
    """测试修复后的计算"""
    print("\n🧪 测试修复后的计算")
    print("="*50)
    
    # 使用诊断函数的结果
    marginal_vars, component_vars, theoretical_var = diagnose_risk_contribution_issue()
    
    # 验证修复效果
    sum_component_var = sum(component_vars.values())
    relative_error = abs(sum_component_var - theoretical_var) / theoretical_var
    
    print(f"\n✅ 修复验证结果:")
    print(f"成分VaR总和: {sum_component_var:.6f}")
    print(f"理论组合VaR: {theoretical_var:.6f}")
    print(f"相对误差: {relative_error:.8f} ({relative_error*100:.6f}%)")
    print(f"验证通过: {relative_error < 1e-10}")
    
    return relative_error < 1e-10


def main():
    """主函数"""
    print("🔧 风险贡献度计算修复")
    print("="*60)
    
    # 诊断问题
    diagnose_risk_contribution_issue()
    
    # 显示修复方案
    create_fixed_risk_contribution_calculator()
    
    # 测试修复效果
    success = test_fixed_calculation()
    
    print(f"\n📋 修复总结:")
    print(f"问题根源: 边际VaR计算公式实现有误")
    print(f"解决方案: 使用标准的投资组合理论公式")
    print(f"关键修复: 确保使用正确的协方差计算和分位数")
    print(f"验证结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print(f"\n💡 下一步:")
        print(f"1. 更新 risk_contribution.py 中的 _calculate_analytical_mvar 方法")
        print(f"2. 确保使用标准正态分布分位数而不是GARCH分布")
        print(f"3. 重新运行验证脚本确认修复效果")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
